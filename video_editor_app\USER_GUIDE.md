# دليل المستخدم - User Guide

## 🎬 مرحباً بك في معالج الفيديوهات المتكامل!

هذا الدليل سيساعدك على استخدام جميع ميزات التطبيق بسهولة.

## 🚀 البدء السريع

### 1. تشغيل التطبيق
```bash
# Windows
start.bat

# macOS/Linux
./start.sh

# أو مباشرة
python main.py
```

### 2. الواجهة الرئيسية

عند تشغيل التطبيق ستجد:
- **شريط علوي**: أزرار سريعة (مشروع جديد، فتح ملف، تحميل فيديو، إعدادات)
- **تبويبات رئيسية**: معالجة الفيديوهات، الذكاء اللغوي، المشاريع، الإعدادات
- **شريط سفلي**: حالة التطبيق وشريط التقدم

## 📹 معالجة الفيديوهات

### إضافة فيديو

#### من الجهاز
1. انقر **"📁 فتح ملف"**
2. اختر ملف الفيديو (MP4, AVI, MOV, MKV, WMV)
3. سيظهر الفيديو في القائمة

#### من الإنترنت
1. انقر **"⬇️ تحميل فيديو"**
2. أدخل رابط الفيديو (YouTube, TikTok, Facebook, Instagram)
3. انتظر انتهاء التحميل

### إعدادات القص

في تبويب **"🎬 معالجة الفيديوهات"**:

1. **مدة المقطع**: حدد طول كل مقطع (بالثواني)
2. **نوع القص**:
   - **تلقائي (ذكي)**: قص ذكي حسب المحتوى
   - **مقاطع متساوية**: تقسيم متساوي
   - **حسب المشاهد**: قص عند تغيير المشهد
   - **حسب الصوت**: قص حسب فترات الصمت

3. **جودة الإخراج**: اختر الدقة المطلوبة

### إعدادات المونتاج

- ✅ **إضافة مقدمة**: مقدمة تلقائية للفيديو
- ✅ **إضافة خاتمة**: خاتمة تلقائية
- ✅ **إضافة انتقالات**: تأثيرات انتقال بين المقاطع
- ✅ **إضافة موسيقى خلفية**: موسيقى تلقائية
- **الفلاتر**: اختر فلتر للفيديو

### تتبع الوجوه

1. ✅ **تفعيل تتبع الوجوه**
2. **نوع التتبع**:
   - تتبع الوجه الرئيسي
   - تتبع جميع الوجوه  
   - تتبع أكبر وجه
3. **حساسية التتبع**: اضبط دقة التتبع

## 🌐 الذكاء اللغوي

### تفريغ الصوت (Speech-to-Text)

1. اذهب لتبويب **"🌐 الذكاء اللغوي"**
2. انقر **"📝 استخراج النص"**
3. اختر طريقة التفريغ:
   - **Whisper**: دقة عالية (محلي)
   - **OpenAI**: دقة عالية (يحتاج API)
   - **Google**: سريع (يحتاج إنترنت)

### الترجمة

1. **اللغة المصدر**: اختر لغة الفيديو (أو "تلقائي")
2. **اللغة الهدف**: اختر اللغة المطلوبة
3. **خدمة الترجمة**:
   - **Google Translate**: مجاني
   - **DeepL**: جودة عالية (يحتاج API)
   - **Microsoft**: بديل جيد
4. انقر **"🔄 ترجمة"**

### الدبلجة (Text-to-Speech)

1. **نوع الصوت**: اختر صوت ذكر/أنثى
2. **سرعة الكلام**: اضبط السرعة
3. **مستوى الصوت**: اضبط القوة
4. **خدمة التحويل**:
   - **Google TTS**: مجاني وجيد
   - **ElevenLabs**: واقعي جداً (يحتاج API)
   - **Microsoft**: جودة متوسطة
5. انقر **"🎬 إنتاج الدبلجة"**

## ⚡ المعالجة الشاملة

لمعالجة كاملة للفيديو:

1. **أضف فيديو** (من الجهاز أو رابط)
2. **اضبط إعدادات القص** حسب الحاجة
3. **فعّل الميزات المطلوبة**:
   - ✅ المونتاج التلقائي
   - ✅ تتبع الوجوه
   - ✅ الترجمة والدبلجة
4. انقر **"🚀 بدء المعالجة"**
5. انتظر انتهاء العملية (تابع شريط التقدم)

## 📋 إدارة المشاريع

### حفظ مشروع
1. اذهب لتبويب **"📋 المشاريع"**
2. انقر **"💾 حفظ المشروع"**
3. أدخل اسم المشروع

### فتح مشروع محفوظ
1. اختر المشروع من القائمة
2. انقر **"📂 فتح مشروع"**

### تصدير مشروع
1. اختر المشروع
2. انقر **"📤 تصدير المشروع"**
3. اختر مكان الحفظ

## ⚙️ الإعدادات

### الإعدادات العامة
- **لغة التطبيق**: العربية/English
- **مجلد الإخراج**: مكان حفظ الفيديوهات المعالجة
- **جودة الإخراج الافتراضية**: الدقة المفضلة

### إعدادات الأداء
- **عدد المعالجات**: استخدم أكثر للسرعة
- **استخدام GPU**: تسريع بكرت الرسوميات
- **حجم الذاكرة المؤقتة**: للفيديوهات الكبيرة

### مفاتيح API
أدخل مفاتيح API للميزات المتقدمة:
- **OpenAI API Key**: للدبلجة وتفريغ عالي الجودة
- **ElevenLabs API Key**: للدبلجة الواقعية
- **DeepL API Key**: للترجمة عالية الجودة

## 💡 نصائح وحيل

### لأفضل النتائج

1. **جودة الفيديو الأصلي**:
   - استخدم فيديوهات عالية الجودة
   - تأكد من وضوح الصوت

2. **تتبع الوجوه**:
   - يعمل أفضل مع إضاءة جيدة
   - تجنب الحركة السريعة جداً

3. **الترجمة**:
   - كلام واضح يعطي ترجمة أفضل
   - راجع الترجمة قبل الدبلجة

4. **الأداء**:
   - أغلق البرامج الأخرى للسرعة
   - استخدم SSD للتخزين إن أمكن

### اختصارات مفيدة

- **Ctrl+O**: فتح ملف
- **Ctrl+S**: حفظ مشروع
- **Ctrl+N**: مشروع جديد
- **F5**: تحديث القائمة
- **Esc**: إلغاء العملية الحالية

## 🔧 حل المشاكل

### الفيديو لا يُحمّل
- تحقق من صحة الرابط
- تأكد من اتصال الإنترنت
- جرب رابط آخر

### المعالجة بطيئة
- قلل جودة الإخراج مؤقتاً
- أغلق البرامج الأخرى
- استخدم فيديو أصغر للاختبار

### خطأ في الترجمة
- تحقق من اتصال الإنترنت
- تأكد من صحة مفاتيح API
- جرب خدمة ترجمة أخرى

### الدبلجة لا تعمل
- تأكد من وجود نص مترجم
- تحقق من إعدادات الصوت
- جرب خدمة TTS أخرى

## 📁 مجلدات مهمة

- **output_videos/**: الفيديوهات المعالجة
- **temp_files/**: ملفات مؤقتة (يمكن حذفها)
- **logs/**: سجلات التطبيق للمشاكل
- **projects/**: المشاريع المحفوظة

## 🎯 أمثلة عملية

### مثال 1: قص فيديو YouTube
1. انسخ رابط فيديو YouTube
2. انقر "تحميل فيديو" والصق الرابط
3. اضبط مدة المقطع (مثلاً 30 ثانية)
4. اختر "مقاطع متساوية"
5. انقر "بدء المعالجة"

### مثال 2: ترجمة ودبلجة
1. أضف فيديو عربي
2. اختر "استخراج النص"
3. اضبط الترجمة: العربية → الإنجليزية
4. انقر "ترجمة"
5. اختر صوت إنجليزي للدبلجة
6. انقر "إنتاج الدبلجة"

### مثال 3: تتبع الوجوه
1. أضف فيديو به أشخاص
2. فعّل "تتبع الوجوه"
3. اختر "تتبع الوجه الرئيسي"
4. اضبط الحساسية حسب الحاجة
5. انقر "بدء المعالجة"

## 📞 الحصول على المساعدة

- **دليل التثبيت**: راجع INSTALL.md
- **الأسئلة الشائعة**: راجع GitHub Issues
- **الدعم المباشر**: انضم لـ Discord
- **الإبلاغ عن خطأ**: أنشئ Issue على GitHub

---

**استمتع بمعالجة الفيديوهات! 🎉**
