#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لجميع مشاكل الاستيراد
Complete Fix for All Import Issues
"""

import sys
import os
import re
from pathlib import Path

def fix_main_window_imports():
    """إصلاح استيرادات main_window.py"""
    print("🔧 إصلاح استيرادات main_window.py...")
    
    main_window_path = Path(__file__).parent / "gui" / "main_window.py"
    
    if not main_window_path.exists():
        print("  ❌ main_window.py غير موجود")
        return False
    
    try:
        # قراءة الملف
        with open(main_window_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن سطر الاستيراد الحالي
        import_pattern = r'from PyQt6\.QtWidgets import \((.*?)\)'
        match = re.search(import_pattern, content, re.DOTALL)
        
        if match:
            # قائمة شاملة لجميع الويدجت المطلوبة
            complete_imports = """from PyQt6.QtWidgets import (
        QAbstractItemView,
        QAction,
        QActionGroup,
        QApplication,
        QButtonGroup,
        QCalendarWidget,
        QCheckBox,
        QComboBox,
        QCommandLinkButton,
        QDateEdit,
        QDateTimeEdit,
        QDial,
        QDialog,
        QDialogButtonBox,
        QDoubleSpinBox,
        QFileDialog,
        QFontComboBox,
        QFormLayout,
        QFrame,
        QGraphicsItem,
        QGraphicsPixmapItem,
        QGraphicsScene,
        QGraphicsTextItem,
        QGraphicsView,
        QGridLayout,
        QGroupBox,
        QHBoxLayout,
        QHeaderView,
        QInputDialog,
        QKeySequenceEdit,
        QLCDNumber,
        QLabel,
        QLineEdit,
        QListWidget,
        QListWidgetItem,
        QMainWindow,
        QMenu,
        QMenuBar,
        QMessageBox,
        QPlainTextEdit,
        QProgressBar,
        QPushButton,
        QRadioButton,
        QRubberBand,
        QScrollArea,
        QScrollBar,
        QSeparator,
        QSizeGrip,
        QSizePolicy,
        QSlider,
        QSpinBox,
        QSplitter,
        QStackedWidget,
        QStatusBar,
        QTabWidget,
        QTableWidget,
        QTableWidgetItem,
        QTextBrowser,
        QTextEdit,
        QTimeEdit,
        QToolBar,
        QToolButton,
        QTreeWidget,
        QTreeWidgetItem,
        QVBoxLayout,
        QWidget
    )"""
            
            # استبدال الاستيراد القديم
            content = re.sub(import_pattern, complete_imports, content, flags=re.DOTALL)
            
            # كتابة الملف المحدث
            with open(main_window_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("  ✅ تم إصلاح استيرادات main_window.py")
            return True
        else:
            print("  ⚠️ لم يتم العثور على نمط الاستيراد")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في إصلاح main_window.py: {e}")
        return False

def fix_all_python_files():
    """إصلاح جميع ملفات Python في المشروع"""
    print("🔧 إصلاح جميع ملفات Python...")
    
    project_root = Path(__file__).parent
    python_files = list(project_root.rglob("*.py"))
    
    # قائمة الاستيرادات الشائعة المفقودة
    common_fixes = {
        # PyQt6 Widgets
        r'\bQLineEdit\b': 'QLineEdit',
        r'\bQScrollArea\b': 'QScrollArea',
        r'\bQSizePolicy\b': 'QSizePolicy',
        r'\bQDialog\b': 'QDialog',
        r'\bQDialogButtonBox\b': 'QDialogButtonBox',
        r'\bQFormLayout\b': 'QFormLayout',
        r'\bQStackedWidget\b': 'QStackedWidget',
        r'\bQToolBar\b': 'QToolBar',
        r'\bQMenuBar\b': 'QMenuBar',
        r'\bQStatusBar\b': 'QStatusBar',
        r'\bQAction\b': 'QAction',
        r'\bQMenu\b': 'QMenu',
        r'\bQTreeWidget\b': 'QTreeWidget',
        r'\bQTableWidget\b': 'QTableWidget',
        r'\bQTextBrowser\b': 'QTextBrowser',
        r'\bQPlainTextEdit\b': 'QPlainTextEdit',
        
        # PyQt6 Core
        r'\bQUrl\b': 'QUrl',
        r'\bQDir\b': 'QDir',
        r'\bQFile\b': 'QFile',
        r'\bQIODevice\b': 'QIODevice',
        r'\bQByteArray\b': 'QByteArray',
        r'\bQVariant\b': 'QVariant',
        r'\bQSettings\b': 'QSettings',
        r'\bQStandardPaths\b': 'QStandardPaths',
        
        # PyQt6 Gui
        r'\bQPixmap\b': 'QPixmap',
        r'\bQImage\b': 'QImage',
        r'\bQPainter\b': 'QPainter',
        r'\bQBrush\b': 'QBrush',
        r'\bQPen\b': 'QPen',
        r'\bQColor\b': 'QColor',
        r'\bQFont\b': 'QFont',
        r'\bQFontMetrics\b': 'QFontMetrics',
        r'\bQIcon\b': 'QIcon',
        r'\bQCursor\b': 'QCursor',
        r'\bQKeySequence\b': 'QKeySequence',
    }
    
    fixed_files = []
    
    for file_path in python_files:
        if file_path.name == __file__.split('/')[-1]:  # تجاهل هذا الملف
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # البحث عن الاستخدامات المفقودة
            missing_imports = set()
            
            for pattern, import_name in common_fixes.items():
                if re.search(pattern, content):
                    missing_imports.add(import_name)
            
            if missing_imports:
                # إضافة الاستيرادات المفقودة
                widget_imports = [imp for imp in missing_imports if imp.startswith('Q') and not imp in ['QUrl', 'QDir', 'QFile', 'QIODevice', 'QByteArray', 'QVariant', 'QSettings', 'QStandardPaths', 'QPixmap', 'QImage', 'QPainter', 'QBrush', 'QPen', 'QColor', 'QFont', 'QFontMetrics', 'QIcon', 'QCursor', 'QKeySequence']]
                core_imports = [imp for imp in missing_imports if imp in ['QUrl', 'QDir', 'QFile', 'QIODevice', 'QByteArray', 'QVariant', 'QSettings', 'QStandardPaths']]
                gui_imports = [imp for imp in missing_imports if imp in ['QPixmap', 'QImage', 'QPainter', 'QBrush', 'QPen', 'QColor', 'QFont', 'QFontMetrics', 'QIcon', 'QCursor', 'QKeySequence']]
                
                # البحث عن استيرادات PyQt6 الموجودة
                widget_import_pattern = r'from PyQt6\.QtWidgets import \((.*?)\)'
                core_import_pattern = r'from PyQt6\.QtCore import \((.*?)\)'
                gui_import_pattern = r'from PyQt6\.QtGui import \((.*?)\)'
                
                # إضافة الاستيرادات المفقودة
                if widget_imports:
                    widget_match = re.search(widget_import_pattern, content, re.DOTALL)
                    if widget_match:
                        existing_imports = [imp.strip() for imp in widget_match.group(1).split(',')]
                        all_imports = list(set(existing_imports + widget_imports))
                        all_imports.sort()
                        new_import = f"from PyQt6.QtWidgets import (
        QAbstractItemView,
        QAction,
        QActionGroup,
        QApplication,
        QButtonGroup,
        QCalendarWidget,
        QCheckBox,
        QComboBox,
        QCommandLinkButton,
        QDateEdit,
        QDateTimeEdit,
        QDial,
        QDialog,
        QDialogButtonBox,
        QDoubleSpinBox,
        QFileDialog,
        QFontComboBox,
        QFormLayout,
        QFrame,
        QGraphicsItem,
        QGraphicsPixmapItem,
        QGraphicsScene,
        QGraphicsTextItem,
        QGraphicsView,
        QGridLayout,
        QGroupBox,
        QHBoxLayout,
        QHeaderView,
        QInputDialog,
        QKeySequenceEdit,
        QLCDNumber,
        QLabel,
        QLineEdit,
        QListWidget,
        QListWidgetItem,
        QMainWindow,
        QMenu,
        QMenuBar,
        QMessageBox,
        QPlainTextEdit,
        QProgressBar,
        QPushButton,
        QRadioButton,
        QRubberBand,
        QScrollArea,
        QScrollBar,
        QSeparator,
        QSizeGrip,
        QSizePolicy,
        QSlider,
        QSpinBox,
        QSplitter,
        QStackedWidget,
        QStatusBar,
        QTabWidget,
        QTableWidget,
        QTableWidgetItem,
        QTextBrowser,
        QTextEdit,
        QTimeEdit,
        QToolBar,
        QToolButton,
        QTreeWidget,
        QTreeWidgetItem,
        QVBoxLayout,
        QWidget
    ) + "\n    )"
                        content = re.sub(widget_import_pattern, new_import, content, flags=re.DOTALL)
                
                if core_imports:
                    core_match = re.search(core_import_pattern, content, re.DOTALL)
                    if core_match:
                        existing_imports = [imp.strip() for imp in core_match.group(1).split(',')]
                        all_imports = list(set(existing_imports + core_imports))
                        all_imports.sort()
                        new_import = f"from PyQt6.QtCore import (
        QByteArray,
        QDir,
        QFile,
        QIODevice,
        QSettings,
        QStandardPaths,
        QUrl,
        QVariant,
        
        " + ",
        
        ".join(all_imports
    ) + "\n    )"
                        content = re.sub(core_import_pattern, new_import, content, flags=re.DOTALL)
                
                if gui_imports:
                    gui_match = re.search(gui_import_pattern, content, re.DOTALL)
                    if gui_match:
                        existing_imports = [imp.strip() for imp in gui_match.group(1).split(',')]
                        all_imports = list(set(existing_imports + gui_imports))
                        all_imports.sort()
                        new_import = f"from PyQt6.QtGui import (
        QBrush,
        QColor,
        QCursor,
        QFont,
        QFontMetrics,
        QIcon,
        QImage,
        QKeySequence,
        QPainter,
        QPen,
        QPixmap,
        
        " + ",
        
        ".join(all_imports
    ) + "\n    )"
                        content = re.sub(gui_import_pattern, new_import, content, flags=re.DOTALL)
                
                # كتابة الملف إذا تم تعديله
                if content != original_content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    fixed_files.append(str(file_path.relative_to(project_root)))
                    print(f"  ✅ تم إصلاح {file_path.relative_to(project_root)}")
        
        except Exception as e:
            print(f"  ❌ خطأ في معالجة {file_path.relative_to(project_root)}: {e}")
    
    return fixed_files

def create_complete_imports_template():
    """إنشاء قالب شامل للاستيرادات"""
    print("📝 إنشاء قالب شامل للاستيرادات...")
    
    template_content = '''# -*- coding: utf-8 -*-
"""
قالب شامل للاستيرادات - Complete Imports Template
استخدم هذا القالب لضمان استيراد جميع المكونات المطلوبة
"""

# استيرادات Python الأساسية
import sys
import os
import json
import sqlite3
import threading
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple
from datetime import datetime

# استيرادات PyQt6 الشاملة
try:
    # PyQt6 Widgets
    from PyQt6.QtWidgets import (
        QAbstractItemView,
        QAction,
        QActionGroup,
        QApplication,
        QButtonGroup,
        QCalendarWidget,
        QCheckBox,
        QComboBox,
        QCommandLinkButton,
        QDateEdit,
        QDateTimeEdit,
        QDial,
        QDialog,
        QDialogButtonBox,
        QDoubleSpinBox,
        QFileDialog,
        QFontComboBox,
        QFormLayout,
        QFrame,
        QGraphicsItem,
        QGraphicsPixmapItem,
        QGraphicsScene,
        QGraphicsTextItem,
        QGraphicsView,
        QGridLayout,
        QGroupBox,
        QHBoxLayout,
        QHeaderView,
        QInputDialog,
        QKeySequenceEdit,
        QLCDNumber,
        QLabel,
        QLineEdit,
        QListWidget,
        QListWidgetItem,
        QMainWindow,
        QMenu,
        QMenuBar,
        QMessageBox,
        QPlainTextEdit,
        QProgressBar,
        QPushButton,
        QRadioButton,
        QRubberBand,
        QScrollArea,
        QScrollBar,
        QSeparator,
        QSizeGrip,
        QSizePolicy,
        QSlider,
        QSpinBox,
        QSplitter,
        QStackedWidget,
        QStatusBar,
        QTabWidget,
        QTableWidget,
        QTableWidgetItem,
        QTextBrowser,
        QTextEdit,
        QTimeEdit,
        QToolBar,
        QToolButton,
        QTreeWidget,
        QTreeWidgetItem,
        QVBoxLayout,
        QWidget
    )
    
    # PyQt6 Core
    from PyQt6.QtCore import (
        QByteArray,
        QDir,
        QFile,
        QIODevice,
        QSettings,
        QStandardPaths,
        QUrl,
        QVariant,
        
        " + ",
        
        ".join(all_imports
    )
    
    # PyQt6 Gui
    from PyQt6.QtGui import (
        QBrush,
        QColor,
        QCursor,
        QFont,
        QFontMetrics,
        QIcon,
        QImage,
        QKeySequence,
        QPainter,
        QPen,
        QPixmap,
        
        " + ",
        
        ".join(all_imports
    )
    
    PYQT6_AVAILABLE = True
    print("✅ جميع استيرادات PyQt6 متاحة")
    
except ImportError as e:
    print(f"❌ خطأ في استيراد PyQt6: {e}")
    PYQT6_AVAILABLE = False
    
    # إنشاء فئات وهمية
    class QMainWindow: pass
    class QWidget: pass
    class QApplication: pass
    # ... إضافة المزيد حسب الحاجة

# استيرادات المكتبات الاختيارية
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("⚠️ requests غير متاح")

try:
    from PIL import Image
    PILLOW_AVAILABLE = True
except ImportError:
    PILLOW_AVAILABLE = False
    print("⚠️ Pillow غير متاح")

try:
    import moviepy
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("⚠️ MoviePy غير متاح")

try:
    import cv2
    OPENCV_AVAILABLE = True
except ImportError:
    OPENCV_AVAILABLE = False
    print("⚠️ OpenCV غير متاح")

# دالة للتحقق من توفر المكتبات
def check_all_imports():
    """فحص جميع الاستيرادات"""
    status = {
        'PyQt6': PYQT6_AVAILABLE,
        'requests': REQUESTS_AVAILABLE,
        'Pillow': PILLOW_AVAILABLE,
        'MoviePy': MOVIEPY_AVAILABLE,
        'OpenCV': OPENCV_AVAILABLE
    }
    
    print("📊 حالة الاستيرادات:")
    for lib, available in status.items():
        print(f"  {lib}: {'✅' if available else '❌'}")
    
    return status

if __name__ == "__main__":
    check_all_imports()
'''
    
    try:
        template_path = Path(__file__).parent / "utils" / "complete_imports.py"
        template_path.parent.mkdir(exist_ok=True)
        
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        print(f"  ✅ تم إنشاء قالب الاستيرادات في: {template_path}")
        return True
        
    except Exception as e:
        print(f"  ❌ فشل في إنشاء قالب الاستيرادات: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح الشامل"""
    print("🔧 إصلاح شامل لجميع مشاكل الاستيراد")
    print("="*50)
    
    # إصلاح main_window.py
    main_window_fixed = fix_main_window_imports()
    print()
    
    # إصلاح جميع ملفات Python
    fixed_files = fix_all_python_files()
    print()
    
    # إنشاء قالب شامل
    template_created = create_complete_imports_template()
    print()
    
    # النتيجة النهائية
    print("="*50)
    print("📊 النتائج:")
    print(f"  main_window.py: {'✅ تم إصلاحه' if main_window_fixed else '❌ لم يتم إصلاحه'}")
    print(f"  ملفات أخرى تم إصلاحها: {len(fixed_files)}")
    print(f"  قالب الاستيرادات: {'✅ تم إنشاؤه' if template_created else '❌ فشل'}")
    
    if fixed_files:
        print("\n📁 الملفات التي تم إصلاحها:")
        for file in fixed_files:
            print(f"  - {file}")
    
    success = main_window_fixed and template_created
    
    if success:
        print("\n🎉 تم إصلاح جميع مشاكل الاستيراد!")
        print("✅ يمكنك الآن تشغيل التطبيق: python main.py")
    else:
        print("\n⚠️ تم إصلاح معظم المشاكل")
        print("💡 قد تحتاج لمراجعة الملفات يدوياً")
    
    return success

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
