#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جميع الميزات - Test All Features
يختبر كل ميزة في التطبيق للتأكد من عملها
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_requirements():
    """اختبار المتطلبات الأساسية"""
    print("🔍 اختبار المتطلبات الأساسية...")
    
    tests = []
    
    # اختبار Python
    if sys.version_info >= (3, 8):
        tests.append(("Python 3.8+", True, f"✅ Python {sys.version_info.major}.{sys.version_info.minor}"))
    else:
        tests.append(("Python 3.8+", False, f"❌ Python {sys.version_info.major}.{sys.version_info.minor} (يتطلب 3.8+)"))
    
    # اختبار PyQt6
    try:
        import PyQt6
        tests.append(("PyQt6", True, f"✅ PyQt6 {PyQt6.QtCore.PYQT_VERSION_STR}"))
    except ImportError:
        tests.append(("PyQt6", False, "❌ PyQt6 غير مثبت"))
    
    # اختبار المكتبات الإضافية
    for lib in ["requests", "json", "pathlib"]:
        try:
            __import__(lib)
            tests.append((lib, True, f"✅ {lib}"))
        except ImportError:
            tests.append((lib, False, f"❌ {lib} غير متاح"))
    
    return tests

def test_gui_components():
    """اختبار مكونات الواجهة"""
    print("\n🖼️ اختبار مكونات الواجهة...")
    
    tests = []
    
    try:
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        tests.append(("QApplication", True, "✅ يمكن إنشاء QApplication"))
        
        # اختبار النافذة الرئيسية الكاملة
        try:
            from gui.main_window_complete import CompleteMainWindow
            window = CompleteMainWindow()
            tests.append(("CompleteMainWindow", True, "✅ النافذة الرئيسية الكاملة"))
            
            # اختبار المكونات الفرعية
            components = [
                ("video_list", "قائمة الفيديوهات"),
                ("video_url_input", "إدخال رابط الفيديو"),
                ("transcribed_text", "منطقة النص المفرغ"),
                ("translated_text", "منطقة النص المترجم"),
                ("completed_videos_table", "جدول الفيديوهات المنجزة"),
                ("processing_progress", "شريط تقدم المعالجة"),
                ("download_progress", "شريط تقدم التحميل")
            ]
            
            for attr, desc in components:
                if hasattr(window, attr):
                    tests.append((desc, True, f"✅ {desc}"))
                else:
                    tests.append((desc, False, f"❌ {desc} مفقود"))
            
        except ImportError as e:
            tests.append(("CompleteMainWindow", False, f"❌ فشل استيراد النافذة: {e}"))
        
    except ImportError as e:
        tests.append(("QApplication", False, f"❌ فشل إنشاء QApplication: {e}"))
    
    return tests

def test_video_processing_features():
    """اختبار ميزات معالجة الفيديو"""
    print("\n🎬 اختبار ميزات معالجة الفيديو...")
    
    tests = []
    
    try:
        from gui.main_window_complete import CompleteMainWindow
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        window = CompleteMainWindow()
        
        # اختبار وجود خيارات المعالجة
        if hasattr(window, 'processing_type'):
            processing_options = [
                "تقطيع تلقائي ذكي",
                "تقطيع حسب الوقت", 
                "تقطيع حسب المشاهد",
                "تقطيع حسب الصوت",
                "استخراج الإطارات",
                "ضغط الفيديو",
                "تحسين الجودة"
            ]
            
            available_options = [window.processing_type.itemText(i) 
                               for i in range(window.processing_type.count())]
            
            for option in processing_options:
                if option in available_options:
                    tests.append((option, True, f"✅ {option}"))
                else:
                    tests.append((option, False, f"❌ {option} غير متاح"))
        else:
            tests.append(("خيارات المعالجة", False, "❌ قائمة خيارات المعالجة مفقودة"))
        
        # اختبار إعدادات الجودة
        if hasattr(window, 'output_quality'):
            tests.append(("إعدادات الجودة", True, "✅ خيارات جودة الإخراج"))
        else:
            tests.append(("إعدادات الجودة", False, "❌ خيارات جودة الإخراج مفقودة"))
        
        # اختبار أزرار التحكم
        control_buttons = [
            ("start_processing_btn", "زر بدء المعالجة"),
            ("stop_processing_btn", "زر إيقاف المعالجة"),
            ("pause_processing_btn", "زر إيقاف مؤقت")
        ]
        
        for attr, desc in control_buttons:
            if hasattr(window, attr):
                tests.append((desc, True, f"✅ {desc}"))
            else:
                tests.append((desc, False, f"❌ {desc} مفقود"))
        
    except Exception as e:
        tests.append(("معالجة الفيديو", False, f"❌ خطأ في اختبار معالجة الفيديو: {e}"))
    
    return tests

def test_download_features():
    """اختبار ميزات التحميل"""
    print("\n⬇️ اختبار ميزات التحميل...")
    
    tests = []
    
    try:
        from gui.main_window_complete import CompleteMainWindow
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        window = CompleteMainWindow()
        
        # اختبار حقل إدخال الرابط
        if hasattr(window, 'video_url_input'):
            tests.append(("حقل إدخال الرابط", True, "✅ حقل إدخال رابط الفيديو"))
        else:
            tests.append(("حقل إدخال الرابط", False, "❌ حقل إدخال الرابط مفقود"))
        
        # اختبار خيارات جودة التحميل
        if hasattr(window, 'download_quality'):
            download_qualities = [
                "أفضل جودة متاحة",
                "1080p",
                "720p",
                "480p",
                "360p",
                "صوت فقط (MP3)"
            ]
            
            available_qualities = [window.download_quality.itemText(i) 
                                 for i in range(window.download_quality.count())]
            
            for quality in download_qualities:
                if quality in available_qualities:
                    tests.append((f"جودة {quality}", True, f"✅ {quality}"))
                else:
                    tests.append((f"جودة {quality}", False, f"❌ {quality} غير متاح"))
        else:
            tests.append(("خيارات جودة التحميل", False, "❌ خيارات جودة التحميل مفقودة"))
        
        # اختبار قائمة الفيديوهات المحملة
        if hasattr(window, 'downloaded_videos_list'):
            tests.append(("قائمة الفيديوهات المحملة", True, "✅ قائمة الفيديوهات المحملة"))
        else:
            tests.append(("قائمة الفيديوهات المحملة", False, "❌ قائمة الفيديوهات المحملة مفقودة"))
        
    except Exception as e:
        tests.append(("ميزات التحميل", False, f"❌ خطأ في اختبار التحميل: {e}"))
    
    return tests

def test_ai_features():
    """اختبار ميزات الذكاء الاصطناعي"""
    print("\n🧠 اختبار ميزات الذكاء الاصطناعي...")
    
    tests = []
    
    try:
        from gui.main_window_complete import CompleteMainWindow
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        window = CompleteMainWindow()
        
        # اختبار تفريغ الصوت
        transcription_components = [
            ("audio_file_path", "حقل مسار ملف الصوت"),
            ("audio_language", "خيارات لغة الصوت"),
            ("transcription_accuracy", "خيارات دقة التفريغ"),
            ("transcribed_text", "منطقة النص المفرغ")
        ]
        
        for attr, desc in transcription_components:
            if hasattr(window, attr):
                tests.append((desc, True, f"✅ {desc}"))
            else:
                tests.append((desc, False, f"❌ {desc} مفقود"))
        
        # اختبار الترجمة
        translation_components = [
            ("source_language", "لغة المصدر"),
            ("target_language", "لغة الهدف"),
            ("translated_text", "منطقة النص المترجم")
        ]
        
        for attr, desc in translation_components:
            if hasattr(window, attr):
                tests.append((desc, True, f"✅ {desc}"))
            else:
                tests.append((desc, False, f"❌ {desc} مفقود"))
        
        # اختبار الدبلجة
        dubbing_components = [
            ("voice_type", "نوع الصوت"),
            ("speech_speed", "سرعة الكلام")
        ]
        
        for attr, desc in dubbing_components:
            if hasattr(window, attr):
                tests.append((desc, True, f"✅ {desc}"))
            else:
                tests.append((desc, False, f"❌ {desc} مفقود"))
        
    except Exception as e:
        tests.append(("ميزات الذكاء الاصطناعي", False, f"❌ خطأ في اختبار الذكاء الاصطناعي: {e}"))
    
    return tests

def test_project_management():
    """اختبار إدارة المشاريع"""
    print("\n📊 اختبار إدارة المشاريع...")
    
    tests = []
    
    try:
        from gui.main_window_complete import CompleteMainWindow
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        window = CompleteMainWindow()
        
        # اختبار جدول الفيديوهات المنجزة
        if hasattr(window, 'completed_videos_table'):
            tests.append(("جدول الفيديوهات المنجزة", True, "✅ جدول الفيديوهات المنجزة"))
            
            # اختبار أعمدة الجدول
            expected_columns = ["اسم الملف", "تاريخ الإنجاز", "الحجم", "المدة", "الحالة"]
            actual_columns = [window.completed_videos_table.horizontalHeaderItem(i).text() 
                            for i in range(window.completed_videos_table.columnCount())]
            
            for col in expected_columns:
                if col in actual_columns:
                    tests.append((f"عمود {col}", True, f"✅ عمود {col}"))
                else:
                    tests.append((f"عمود {col}", False, f"❌ عمود {col} مفقود"))
        else:
            tests.append(("جدول الفيديوهات المنجزة", False, "❌ جدول الفيديوهات المنجزة مفقود"))
        
        # اختبار الإحصائيات
        stats_components = [
            ("total_processed_label", "إجمالي الفيديوهات المعالجة"),
            ("total_time_label", "إجمالي وقت المعالجة"),
            ("total_size_label", "إجمالي حجم الملفات"),
            ("last_processing_label", "آخر معالجة")
        ]
        
        for attr, desc in stats_components:
            if hasattr(window, attr):
                tests.append((desc, True, f"✅ {desc}"))
            else:
                tests.append((desc, False, f"❌ {desc} مفقود"))
        
    except Exception as e:
        tests.append(("إدارة المشاريع", False, f"❌ خطأ في اختبار إدارة المشاريع: {e}"))
    
    return tests

def test_settings():
    """اختبار الإعدادات"""
    print("\n⚙️ اختبار الإعدادات...")
    
    tests = []
    
    try:
        from gui.main_window_complete import CompleteMainWindow
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        window = CompleteMainWindow()
        
        # اختبار الإعدادات العامة
        general_settings = [
            ("output_dir_input", "مجلد الإخراج"),
            ("app_language", "لغة التطبيق"),
            ("app_theme", "موضوع التطبيق")
        ]
        
        for attr, desc in general_settings:
            if hasattr(window, attr):
                tests.append((desc, True, f"✅ {desc}"))
            else:
                tests.append((desc, False, f"❌ {desc} مفقود"))
        
        # اختبار إعدادات الأداء
        performance_settings = [
            ("cpu_cores", "عدد المعالجات"),
            ("use_gpu", "استخدام GPU"),
            ("cache_size", "حجم الذاكرة المؤقتة")
        ]
        
        for attr, desc in performance_settings:
            if hasattr(window, attr):
                tests.append((desc, True, f"✅ {desc}"))
            else:
                tests.append((desc, False, f"❌ {desc} مفقود"))
        
        # اختبار إعدادات الشبكة
        network_settings = [
            ("connection_timeout", "مهلة الاتصال"),
            ("retry_attempts", "عدد المحاولات")
        ]
        
        for attr, desc in network_settings:
            if hasattr(window, attr):
                tests.append((desc, True, f"✅ {desc}"))
            else:
                tests.append((desc, False, f"❌ {desc} مفقود"))
        
    except Exception as e:
        tests.append(("الإعدادات", False, f"❌ خطأ في اختبار الإعدادات: {e}"))
    
    return tests

def generate_test_report(all_tests):
    """إنشاء تقرير الاختبار"""
    print("\n" + "="*70)
    print("📊 تقرير اختبار جميع الميزات")
    print("="*70)
    
    total_tests = len(all_tests)
    passed_tests = len([test for test in all_tests if test[1]])
    failed_tests = total_tests - passed_tests
    
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"📈 النتائج الإجمالية:")
    print(f"  إجمالي الاختبارات: {total_tests}")
    print(f"  نجح: {passed_tests}")
    print(f"  فشل: {failed_tests}")
    print(f"  معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print(f"\n🎉 ممتاز! التطبيق يعمل بشكل مثالي")
        status = "ممتاز"
    elif success_rate >= 75:
        print(f"\n✅ جيد! معظم الميزات تعمل")
        status = "جيد"
    elif success_rate >= 50:
        print(f"\n⚠️ متوسط! بعض الميزات تحتاج إصلاح")
        status = "متوسط"
    else:
        print(f"\n❌ ضعيف! التطبيق يحتاج إصلاحات كثيرة")
        status = "ضعيف"
    
    # عرض الاختبارات الفاشلة
    failed_tests_list = [test for test in all_tests if not test[1]]
    if failed_tests_list:
        print(f"\n❌ الاختبارات الفاشلة ({len(failed_tests_list)}):")
        for test in failed_tests_list:
            print(f"  • {test[2]}")
    
    print("\n" + "="*70)
    
    return status, success_rate

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار شامل لجميع ميزات التطبيق")
    print("="*50)
    
    all_tests = []
    
    try:
        # تشغيل جميع الاختبارات
        all_tests.extend(test_basic_requirements())
        all_tests.extend(test_gui_components())
        all_tests.extend(test_video_processing_features())
        all_tests.extend(test_download_features())
        all_tests.extend(test_ai_features())
        all_tests.extend(test_project_management())
        all_tests.extend(test_settings())
        
        # إنشاء التقرير
        status, success_rate = generate_test_report(all_tests)
        
        # عرض التوصيات
        print("\n💡 التوصيات:")
        if success_rate >= 90:
            print("  🚀 التطبيق جاهز للاستخدام!")
            print("  ✨ جميع الميزات المطلوبة متوفرة")
        elif success_rate >= 75:
            print("  🔧 شغل: python fix_all_errors.py")
            print("  🚀 ثم: python main_complete.py")
        else:
            print("  🔧 شغل: python ultimate_fix.py")
            print("  🧪 ثم: python test_all_features.py")
            print("  🚀 أخيراً: python main_complete.py")
        
        return success_rate >= 75
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\nاضغط Enter للخروج...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ كارثي: {e}")
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
