#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الحل النهائي
Test Final Solution
"""

import sys
import ast
import traceback
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_syntax_check():
    """اختبار صحة الصيغة"""
    print("🔍 اختبار صحة الصيغة...")
    
    files_to_check = [
        "gui/__init__.py",
        "gui/main_window.py"
    ]
    
    all_valid = True
    
    for file_path in files_to_check:
        full_path = project_root / file_path
        if not full_path.exists():
            print(f"  ⚠️ {file_path} غير موجود")
            continue
        
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # اختبار الصيغة
            ast.parse(content)
            print(f"  ✅ {file_path}")
            
        except SyntaxError as e:
            print(f"  ❌ {file_path}: السطر {e.lineno}: {e.msg}")
            all_valid = False
        except Exception as e:
            print(f"  ❌ {file_path}: {e}")
            all_valid = False
    
    return all_valid

def test_imports():
    """اختبار الاستيرادات"""
    print("\n📦 اختبار الاستيرادات...")
    
    import_tests = [
        ("PyQt6.QtWidgets", "from PyQt6.QtWidgets import QApplication"),
        ("PyQt6.QtCore", "from PyQt6.QtCore import Qt"),
        ("gui", "import gui"),
        ("gui.main_window", "from gui.main_window import MainWindow"),
    ]
    
    all_passed = True
    
    for name, import_cmd in import_tests:
        try:
            exec(import_cmd)
            print(f"  ✅ {name}")
        except Exception as e:
            print(f"  ❌ {name}: {e}")
            all_passed = False
    
    return all_passed

def test_window_creation():
    """اختبار إنشاء النافذة"""
    print("\n🖼️ اختبار إنشاء النافذة...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from gui.main_window import MainWindow
        
        # إنشاء تطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("  ✅ تم إنشاء QApplication")
        
        # إنشاء النافذة
        window = MainWindow()
        print("  ✅ تم إنشاء MainWindow")
        
        # اختبار بعض الخصائص
        window.setWindowTitle("اختبار")
        window.resize(800, 600)
        print("  ✅ تم تعيين خصائص النافذة")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في إنشاء النافذة: {e}")
        traceback.print_exc()
        return False

def test_pyinstaller_compatibility():
    """اختبار التوافق مع PyInstaller"""
    print("\n📦 اختبار التوافق مع PyInstaller...")
    
    compatibility_checks = [
        ("استيرادات نسبية", "from .main_window import MainWindow"),
        ("استيرادات مطلقة", "from gui.main_window import MainWindow"),
        ("معالجة الأخطاء", "try/except blocks"),
        ("فئات وهمية", "Dummy classes for missing imports"),
    ]
    
    try:
        # فحص الملفات للتأكد من التوافق
        main_window_path = project_root / "gui" / "main_window.py"
        
        if main_window_path.exists():
            with open(main_window_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص وجود معالجة الأخطاء
            if "try:" in content and "except ImportError:" in content:
                print("  ✅ معالجة أخطاء الاستيراد")
            else:
                print("  ⚠️ معالجة أخطاء الاستيراد غير كاملة")
            
            # فحص وجود فئات وهمية
            if "class VideoEditorLogger:" in content:
                print("  ✅ فئات وهمية متاحة")
            else:
                print("  ⚠️ فئات وهمية غير متاحة")
            
            # فحص عدم وجود مسارات مطلقة
            if "C:\\" not in content and "/home/" not in content:
                print("  ✅ لا توجد مسارات مطلقة")
            else:
                print("  ⚠️ توجد مسارات مطلقة")
            
            return True
        else:
            print("  ❌ ملف main_window.py غير موجود")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في فحص التوافق: {e}")
        return False

def generate_final_report(syntax_ok, imports_ok, window_ok, pyinstaller_ok):
    """إنشاء تقرير نهائي"""
    print("\n" + "="*60)
    print("📊 التقرير النهائي")
    print("="*60)
    
    total_tests = 4
    passed_tests = sum([syntax_ok, imports_ok, window_ok, pyinstaller_ok])
    
    print(f"النتيجة الإجمالية: {passed_tests}/{total_tests}")
    print()
    
    print(f"صحة الصيغة: {'✅ نجح' if syntax_ok else '❌ فشل'}")
    print(f"الاستيرادات: {'✅ نجح' if imports_ok else '❌ فشل'}")
    print(f"إنشاء النافذة: {'✅ نجح' if window_ok else '❌ فشل'}")
    print(f"توافق PyInstaller: {'✅ نجح' if pyinstaller_ok else '❌ فشل'}")
    
    print("\n" + "="*60)
    
    if passed_tests == total_tests:
        print("🎉 جميع الاختبارات نجحت!")
        print("✅ المشكلة محلولة 100%")
        print("✅ التطبيق جاهز للتشغيل")
        print("✅ جاهز للتحويل إلى EXE")
        
        print("\n🚀 طرق التشغيل:")
        print("  python main.py")
        print("  python main_ultimate_safe.py")
        
        print("\n📦 للتحويل إلى EXE:")
        print("  pyinstaller --onefile --windowed main.py")
        
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت")
        
        if not syntax_ok:
            print("💡 لإصلاح أخطاء الصيغة: python fix_final_solution.py")
        
        if not imports_ok:
            print("💡 لإصلاح الاستيرادات: pip install PyQt6")
        
        if not window_ok:
            print("💡 لإصلاح النافذة: python fix_final_solution.py")
        
        if not pyinstaller_ok:
            print("💡 لإصلاح التوافق: python fix_final_solution.py")
        
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار الحل النهائي لمشكلة المسافات البادئة")
    print("="*60)
    
    # تشغيل جميع الاختبارات
    syntax_ok = test_syntax_check()
    imports_ok = test_imports()
    window_ok = test_window_creation()
    pyinstaller_ok = test_pyinstaller_compatibility()
    
    # إنشاء التقرير النهائي
    success = generate_final_report(syntax_ok, imports_ok, window_ok, pyinstaller_ok)
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        input("\nاضغط Enter للخروج...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
