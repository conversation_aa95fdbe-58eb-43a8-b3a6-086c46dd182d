#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي - نسخة مثالية
Main Application - Perfect Version
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """الدالة الرئيسية"""
    print("🎬 معالج الفيديوهات المتكامل")
    print("="*50)
    
    try:
        # 1. استيراد PyQt6
        print("📦 استيراد PyQt6...")
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        print("✅ PyQt6 متاح")
        
        # 2. إنشاء التطبيق
        print("🚀 إنشاء التطبيق...")
        app = QApplication(sys.argv)
        app.setApplicationName("معالج الفيديوهات المتكامل")
        
        # 3. إعداد RTL
        print("🔄 إعداد اتجاه RTL...")
        try:
            app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        except:
            try:
                app.setLayoutDirection(Qt.RightToLeft)
            except:
                app.setLayoutDirection(2)
        print("✅ تم إعداد RTL")
        
        # 4. إعداد الستايل
        app.setStyleSheet("""
            QMainWindow {
                background-color: #f8f9fa;
            }
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background-color: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background-color: #e9ecef;
                color: #495057;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
                color: white;
            }
        """)
        
        # 5. استيراد النافذة الرئيسية
        print("🖼️ استيراد النافذة الرئيسية...")
        from gui.main_window import MainWindow
        print("✅ تم استيراد MainWindow")
        
        # 6. إنشاء النافذة
        print("🏗️ إنشاء النافذة...")
        window = MainWindow()
        print("✅ تم إنشاء النافذة")
        
        # 7. عرض النافذة
        print("📺 عرض النافذة...")
        window.show()
        print("✅ تم عرض النافذة")
        
        print("🎉 تم تشغيل التطبيق بنجاح!")
        print("✨ التطبيق يعمل بسلاسة تامة!")
        
        # 8. تشغيل حلقة الأحداث
        return app.exec()
        
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        print("💡 الحل: pip install PyQt6")
        input("اضغط Enter للخروج...")
        return 1
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        input("اضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ كارثي: {e}")
        sys.exit(1)
