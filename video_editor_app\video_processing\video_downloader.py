# -*- coding: utf-8 -*-
"""
وحدة تحميل الفيديوهات
Video Downloader Module for Video Editor Application
"""

import os
import re
import requests
from pathlib import Path
from typing import dict, Optional, Callable
from urllib.parse import urlparse
import logging

try:
    import yt_dlp
    YT_DLP_AVAILABLE = True
except ImportError:
    YT_DLP_AVAILABLE = False
    print("⚠️ yt-dlp غير مثبت - تحميل الفيديوهات محدود")

try:
    from pytube import YouTube
    PYTUBE_AVAILABLE = True
except ImportError:
    PYTUBE_AVAILABLE = False
    print("⚠️ pytube غير مثبت - تحميل YouTube محدود")

from utils.logger import VideoEditorLogger

class VideoDownloader:
    """فئة تحميل الفيديوهات من مختلف المنصات"""
    
    def __init__(self, download_path: str = None):
        """
        تهيئة محمل الفيديوهات
        
        Args:
            download_path: مسار التحميل الافتراضي
        """
        self.logger = VideoEditorLogger(__name__)
        
        if download_path is None:
            download_path = Path(__file__).parent.parent / "temp_files"
        
        self.download_path = Path(download_path)
        self.download_path.mkdir(exist_ok=True)
        
        # إعدادات yt-dlp
        self.ydl_opts = {
            'outtmpl': str(self.download_path / '%(title)s.%(ext)s'),
            'format': 'best[height<=1080]',  # أفضل جودة حتى 1080p
            'noplaylist': True,
            'extractaudio': False,
        }
    
    def detect_platform(self, url: str) -> str:
        """
        تحديد منصة الفيديو من الرابط
        
        Args:
            url: رابط الفيديو
            
        Returns:
            str: اسم المنصة
        """
        url = url.lower()
        
        if 'youtube.com' in url or 'youtu.be' in url:
            return 'youtube'
        elif 'tiktok.com' in url:
            return 'tiktok'
        elif 'facebook.com' in url or 'fb.watch' in url:
            return 'facebook'
        elif 'instagram.com' in url:
            return 'instagram'
        elif 'twitter.com' in url or 'x.com' in url:
            return 'twitter'
        elif 'vimeo.com' in url:
            return 'vimeo'
        else:
            return 'unknown'
    
    def get_video_info(self, url: str) -> dict:
        """
        الحصول على معلومات الفيديو
        
        Args:
            url: رابط الفيديو
            
        Returns:
            dict: معلومات الفيديو
        """
        try:
            if not YT_DLP_AVAILABLE:
                raise Exception("مكتبة yt-dlp غير مثبتة")
            
            with yt_dlp.YoutubeDL({'quiet': True}) as ydl:
                info = ydl.extract_info(url, download=False)
                
                return {
                    'title': info.get('title', 'Unknown'),
                    'duration': info.get('duration', 0),
                    'uploader': info.get('uploader', 'Unknown'),
                    'view_count': info.get('view_count', 0),
                    'upload_date': info.get('upload_date', ''),
                    'description': info.get('description', ''),
                    'thumbnail': info.get('thumbnail', ''),
                    'formats': len(info.get('formats', [])),
                    'platform': self.detect_platform(url)
                }
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات الفيديو: {str(e)}")
            return {}
    
    def download_video(self, url: str, progress_callback: Callable = None, 
                      quality: str = 'best') -> dict:
        """
        تحميل فيديو من رابط
        
        Args:
            url: رابط الفيديو
            progress_callback: دالة تحديث التقدم
            quality: جودة التحميل
            
        Returns:
            dict: معلومات التحميل
        """
        try:
            platform = self.detect_platform(url)
            self.logger.info(f"بدء تحميل فيديو من {platform}: {url}")
            
            # إعداد خيارات التحميل حسب الجودة
            opts = self.ydl_opts.copy()
            
            if quality == 'high':
                opts['format'] = 'best[height<=1080]'
            elif quality == 'medium':
                opts['format'] = 'best[height<=720]'
            elif quality == 'low':
                opts['format'] = 'best[height<=480]'
            else:
                opts['format'] = 'best'
            
            # إضافة callback للتقدم
            if progress_callback:
                opts['progress_hooks'] = [self._progress_hook(progress_callback)]
            
            # تحميل باستخدام yt-dlp
            if YT_DLP_AVAILABLE:
                return self._download_with_ytdlp(url, opts)
            elif PYTUBE_AVAILABLE and platform == 'youtube':
                return self._download_with_pytube(url, progress_callback)
            else:
                raise Exception("لا توجد مكتبة تحميل متاحة")
                
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الفيديو: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'file_path': None
            }
    
    def _download_with_ytdlp(self, url: str, opts: dict) -> dict:
        """تحميل باستخدام yt-dlp"""
        try:
            with yt_dlp.YoutubeDL(opts) as ydl:
                # الحصول على معلومات الفيديو أولاً
                info = ydl.extract_info(url, download=False)
                title = info.get('title', 'video')
                
                # تنظيف اسم الملف
                safe_title = re.sub(r'[<>:"/\\|?*]', '_', title)
                
                # تحديث مسار الإخراج
                opts['outtmpl'] = str(self.download_path / f'{safe_title}.%(ext)s')
                
                # تحميل الفيديو
                ydl.params.update(opts)
                ydl.download([url])
                
                # البحث عن الملف المحمل
                downloaded_file = None
                for file in self.download_path.glob(f'{safe_title}.*'):
                    if file.suffix in ['.mp4', '.mkv', '.webm', '.avi']:
                        downloaded_file = file
                        break
                
                if downloaded_file and downloaded_file.exists():
                    self.logger.info(f"تم تحميل الفيديو بنجاح: {downloaded_file}")
                    return {
                        'success': True,
                        'file_path': str(downloaded_file),
                        'title': title,
                        'platform': self.detect_platform(url),
                        'file_size': downloaded_file.stat().st_size
                    }
                else:
                    raise Exception("لم يتم العثور على الملف المحمل")
                    
        except Exception as e:
            raise Exception(f"خطأ في yt-dlp: {str(e)}")
    
    def _download_with_pytube(self, url: str, progress_callback: Callable = None) -> dict:
        """تحميل من YouTube باستخدام pytube"""
        try:
            yt = YouTube(url)
            
            # اختيار أفضل جودة متاحة
            stream = yt.streams.filter(progressive=True, file_extension='mp4').order_by('resolution').desc().first()
            
            if not stream:
                stream = yt.streams.filter(adaptive=True, file_extension='mp4').order_by('resolution').desc().first()
            
            if not stream:
                raise Exception("لا توجد تدفقات متاحة للتحميل")
            
            # تنظيف اسم الملف
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', yt.title)
            filename = f"{safe_title}.mp4"
            
            # تحميل الفيديو
            downloaded_file = stream.download(
                output_path=str(self.download_path),
                filename=filename
            )
            
            self.logger.info(f"تم تحميل الفيديو بنجاح: {downloaded_file}")
            
            return {
                'success': True,
                'file_path': downloaded_file,
                'title': yt.title,
                'platform': 'youtube',
                'file_size': Path(downloaded_file).stat().st_size
            }
            
        except Exception as e:
            raise Exception(f"خطأ في pytube: {str(e)}")
    
    def _progress_hook(self, callback: Callable):
        """إنشاء hook للتقدم"""
        def hook(d):
            if d['status'] == 'downloading':
                if 'total_bytes' in d:
                    percent = (d['downloaded_bytes'] / d['total_bytes']) * 100
                    callback(int(percent))
                elif 'total_bytes_estimate' in d:
                    percent = (d['downloaded_bytes'] / d['total_bytes_estimate']) * 100
                    callback(int(percent))
            elif d['status'] == 'finished':
                callback(100)
        
        return hook
    
    def download_thumbnail(self, url: str) -> Optional[str]:
        """
        تحميل صورة مصغرة للفيديو
        
        Args:
            url: رابط الفيديو
            
        Returns:
            str: مسار الصورة المصغرة
        """
        try:
            info = self.get_video_info(url)
            thumbnail_url = info.get('thumbnail')
            
            if not thumbnail_url:
                return None
            
            # تحميل الصورة
            response = requests.get(thumbnail_url, timeout=10)
            response.raise_for_status()
            
            # حفظ الصورة
            safe_title = re.sub(r'[<>:"/\\|?*]', '_', info.get('title', 'thumbnail'))
            thumbnail_path = self.download_path / f"{safe_title}_thumbnail.jpg"
            
            with open(thumbnail_path, 'wb') as f:
                f.write(response.content)
            
            self.logger.info(f"تم تحميل الصورة المصغرة: {thumbnail_path}")
            return str(thumbnail_path)
            
        except Exception as e:
            self.logger.error(f"خطأ في تحميل الصورة المصغرة: {str(e)}")
            return None
    
    def validate_url(self, url: str) -> bool:
        """
        التحقق من صحة الرابط
        
        Args:
            url: الرابط للتحقق منه
            
        Returns:
            bool: True إذا كان الرابط صحيح
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except:
            return False
    
    def get_supported_platforms(self) -> list:
        """الحصول على قائمة المنصات المدعومة"""
        platforms = []
        
        if YT_DLP_AVAILABLE:
            platforms.extend([
                'YouTube', 'TikTok', 'Facebook', 'Instagram', 
                'Twitter', 'Vimeo', 'Dailymotion'
            ])
        elif PYTUBE_AVAILABLE:
            platforms.append('YouTube')
        
        return platforms
