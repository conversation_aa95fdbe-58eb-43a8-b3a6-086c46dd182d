#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل ونهائي لجميع مشاكل المشروع
Complete and Final Fix for All Project Issues
"""

import sys
import os
import subprocess
import re
from pathlib import Path

def install_essential_packages():
    """تثبيت الحزم الأساسية"""
    print("📦 تثبيت الحزم الأساسية...")
    
    packages = [
        "PyQt6>=6.6.0",
        "requests>=2.31.0",
        "Pillow>=10.0.0",
        "moviepy>=1.0.0",
        "opencv-python>=4.8.0",
        "sqlalchemy>=2.0.0",
        "tqdm>=4.66.0",
    ]
    
    for package in packages:
        try:
            print(f"  تثبيت {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--upgrade", "--quiet"
            ])
            print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"  ⚠️ فشل في تثبيت {package}")

def fix_all_typing_issues():
    """إصلاح جميع مشاكل الـ typing"""
    print("🔧 إصلاح مشاكل الـ typing...")
    
    project_root = Path(__file__).parent
    python_files = list(project_root.rglob("*.py"))
    
    # قائمة الاستبدالات
    replacements = [
        (r'\bDict\b', 'dict'),
        (r'\bList\b', 'list'),
        (r'\bTuple\b', 'tuple'),
        (r'\bSet\b', 'set'),
        (r'-> Dict\[', '-> dict['),
        (r'-> List\[', '-> list['),
        (r': Dict\[', ': dict['),
        (r': List\[', ': list['),
    ]
    
    fixed_files = 0
    for file_path in python_files:
        if file_path.name == Path(__file__).name:
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # تطبيق الاستبدالات
            for pattern, replacement in replacements:
                content = re.sub(pattern, replacement, content)
            
            # كتابة الملف إذا تم تعديله
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                fixed_files += 1
                print(f"  ✅ تم إصلاح {file_path.relative_to(project_root)}")
        
        except Exception as e:
            print(f"  ❌ خطأ في {file_path.relative_to(project_root)}: {e}")
    
    print(f"  📊 تم إصلاح {fixed_files} ملف")

def fix_pyqt6_imports():
    """إصلاح جميع استيرادات PyQt6"""
    print("🖥️ إصلاح استيرادات PyQt6...")
    
    # إصلاح main_window.py
    main_window_path = Path(__file__).parent / "gui" / "main_window.py"
    
    if main_window_path.exists():
        try:
            with open(main_window_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التأكد من وجود جميع الاستيرادات المطلوبة
            required_widgets = [
                'QMainWindow', 'QWidget', 'QVBoxLayout', 'QHBoxLayout', 'QGridLayout',
                'QTabWidget', 'QLabel', 'QPushButton', 'QProgressBar', 'QTextEdit',
                'QFileDialog', 'QMessageBox', 'QSplitter', 'QListWidget', 'QGroupBox',
                'QComboBox', 'QSpinBox', 'QCheckBox', 'QSlider', 'QFrame', 'QInputDialog',
                'QLineEdit', 'QScrollArea', 'QSizePolicy', 'QApplication'
            ]
            
            # البحث عن استيراد QtWidgets
            widget_pattern = r'from PyQt6\.QtWidgets import \((.*?)\)'
            match = re.search(widget_pattern, content, re.DOTALL)
            
            if match:
                # إنشاء استيراد شامل
                new_import = f"""from PyQt6.QtWidgets import (
        {', '.join(required_widgets)}
    )"""
                
                content = re.sub(widget_pattern, new_import, content, flags=re.DOTALL)
                
                with open(main_window_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("  ✅ تم إصلاح استيرادات main_window.py")
            
        except Exception as e:
            print(f"  ❌ خطأ في إصلاح main_window.py: {e}")

def fix_rtl_issues():
    """إصلاح مشاكل RTL"""
    print("🔄 إصلاح مشاكل RTL...")
    
    main_path = Path(__file__).parent / "main.py"
    
    if main_path.exists():
        try:
            with open(main_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # التأكد من وجود دالة RTL آمنة
            if "def setup_rtl_layout(app):" in content:
                print("  ✅ دالة RTL موجودة")
            else:
                # إضافة دالة RTL آمنة
                rtl_function = '''
def setup_rtl_layout(app):
    """إعداد اتجاه RTL للغة العربية بشكل آمن"""
    rtl_methods = [
        (lambda: app.setLayoutDirection(Qt.LayoutDirection.RightToLeft), "Qt.LayoutDirection.RightToLeft"),
        (lambda: app.setLayoutDirection(Qt.RightToLeft), "Qt.RightToLeft"),
        (lambda: app.setLayoutDirection(2), "الرقم المباشر (2)"),
    ]
    
    for method, description in rtl_methods:
        try:
            method()
            print(f"✅ تم تعيين اتجاه RTL باستخدام {description}")
            return True
        except (AttributeError, TypeError, Exception) as e:
            print(f"⚠️ فشل {description}: {e}")
            continue
    
    print("❌ فشل في تعيين اتجاه RTL بجميع الطرق")
    return False
'''
                
                # إضافة الدالة قبل def setup_application
                content = content.replace("def setup_application():", rtl_function + "\ndef setup_application():")
                
                with open(main_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("  ✅ تم إضافة دالة RTL آمنة")
        
        except Exception as e:
            print(f"  ❌ خطأ في إصلاح RTL: {e}")

def create_safe_startup_script():
    """إنشاء سكريپت تشغيل آمن"""
    print("🚀 إنشاء سكريپت تشغيل آمن...")
    
    startup_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريپت تشغيل آمن للتطبيق
Safe Startup Script
"""

import sys
import subprocess
from pathlib import Path

def check_and_install_requirements():
    """فحص وتثبيت المتطلبات"""
    required_packages = [
        "PyQt6", "requests", "Pillow"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"📦 تثبيت الحزم المفقودة: {', '.join(missing_packages)}")
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            except subprocess.CalledProcessError:
                print(f"❌ فشل في تثبيت {package}")
                return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🎬 معالج الفيديوهات المتكامل - تشغيل آمن")
    print("="*50)
    
    # فحص وتثبيت المتطلبات
    if not check_and_install_requirements():
        print("❌ فشل في تثبيت المتطلبات")
        input("اضغط Enter للخروج...")
        return
    
    # تشغيل التطبيق
    try:
        import main
        main.main()
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
    
    try:
        startup_path = Path(__file__).parent / "start_safe.py"
        with open(startup_path, 'w', encoding='utf-8') as f:
            f.write(startup_script)
        
        print(f"  ✅ تم إنشاء سكريپت التشغيل الآمن: {startup_path}")
        
    except Exception as e:
        print(f"  ❌ فشل في إنشاء سكريپت التشغيل: {e}")

def run_final_test():
    """تشغيل اختبار نهائي"""
    print("🧪 تشغيل اختبار نهائي...")
    
    try:
        # اختبار الاستيرادات الأساسية
        from PyQt6.QtWidgets import QApplication, QMainWindow
        from PyQt6.QtCore import Qt
        print("  ✅ PyQt6 يعمل")
        
        # اختبار استيراد الوحدات
        from gui.main_window import MainWindow
        print("  ✅ MainWindow يعمل")
        
        from src.video_editor_core import VideoEditorCore
        print("  ✅ VideoEditorCore يعمل")
        
        # اختبار إنشاء تطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        window = MainWindow()
        print("  ✅ إنشاء النافذة الرئيسية يعمل")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح الشامل"""
    print("🔧 الإصلاح الشامل والنهائي لمعالج الفيديوهات المتكامل")
    print("="*60)
    
    # تشغيل جميع الإصلاحات
    try:
        # 1. تثبيت الحزم الأساسية
        install_essential_packages()
        print()
        
        # 2. إصلاح مشاكل الـ typing
        fix_all_typing_issues()
        print()
        
        # 3. إصلاح استيرادات PyQt6
        fix_pyqt6_imports()
        print()
        
        # 4. إصلاح مشاكل RTL
        fix_rtl_issues()
        print()
        
        # 5. إنشاء سكريپت تشغيل آمن
        create_safe_startup_script()
        print()
        
        # 6. اختبار نهائي
        test_passed = run_final_test()
        print()
        
        # النتيجة النهائية
        print("="*60)
        if test_passed:
            print("🎉 تم إصلاح جميع المشاكل بنجاح!")
            print("✅ التطبيق جاهز للتشغيل")
            print("\nطرق التشغيل:")
            print("  1. python main.py")
            print("  2. python start_safe.py")
            print("  3. python safe_run.py")
        else:
            print("⚠️ تم إصلاح معظم المشاكل")
            print("❌ قد تحتاج لمراجعة يدوية")
            print("\nجرب:")
            print("  pip install --upgrade PyQt6")
            print("  python test_final.py")
        
        return test_passed
        
    except Exception as e:
        print(f"❌ خطأ في عملية الإصلاح: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
