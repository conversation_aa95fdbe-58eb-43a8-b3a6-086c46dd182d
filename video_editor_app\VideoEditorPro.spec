# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['F:\\coode\\video_editor_app\\main.py'],
    pathex=[],
    binaries=[],
    datas=[('F:\\coode\\video_editor_app\\gui', 'gui'), ('F:\\coode\\video_editor_app\\src', 'src'), ('F:\\coode\\video_editor_app\\video_processing', 'video_processing'), ('F:\\coode\\video_editor_app\\language_ai', 'language_ai'), ('F:\\coode\\video_editor_app\\database', 'database'), ('F:\\coode\\video_editor_app\\utils', 'utils'), ('F:\\coode\\video_editor_app\\README.md', '.'), ('F:\\coode\\video_editor_app\\config.py', '.')],
    hiddenimports=['PyQt6.QtCore', 'PyQt6.QtGui', 'PyQt6.QtWidgets', 'moviepy.editor', 'cv2', 'whisper', 'googletrans', 'gtts', 'sqlalchemy', 'qdarkstyle', 'requests', 'pathlib', 'json', 'sqlite3', 'threading', 'logging', 'datetime', 'tempfile', 'subprocess'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'scipy', 'pandas', 'jupyter', 'IPython'],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='VideoEditorPro',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
