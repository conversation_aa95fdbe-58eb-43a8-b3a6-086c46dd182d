# -*- coding: utf-8 -*-
"""
وحدة معالجة الفيديوهات
Video Processing Module for Video Editor Application
"""

try:
    from .video_downloader import VideoDownloader
except ImportError as e:
    print(f"⚠️ خطأ في استيراد محمل الفيديو: {e}")
    class VideoDownloader:
        def __init__(self): pass
        def download_video(self, url, output_path): return None

try:
    from .video_processor import VideoProcessor
except ImportError as e:
    print(f"⚠️ خطأ في استيراد معالج الفيديو: {e}")
    class VideoProcessor:
        def __init__(self): pass
        def process_video(self, *args): return None

try:
    from .face_tracker import FaceTracker
except ImportError as e:
    print(f"⚠️ خطأ في استيراد متتبع الوجوه: {e}")
    class FaceTracker:
        def __init__(self): pass
        def track_faces(self, *args): return None

__all__ = ['VideoDownloader', 'VideoProcessor', 'FaceTracker']
