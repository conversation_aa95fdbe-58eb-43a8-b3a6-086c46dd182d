# -*- coding: utf-8 -*-
"""
النواة الرئيسية لمعالج الفيديوهات
Video Editor Core Module - Main Processing Engine
"""

import os
import threading
from pathlib import Path
from typing import Dict, List, Optional, Callable, Any
import logging
from datetime import datetime

# استيراد PyQt6 مع معالجة الأخطاء
try:
    from PyQt6.QtCore import QObject, pyqtSignal, QThread
    PYQT6_AVAILABLE = True
except ImportError:
    print("⚠️ PyQt6 غير متاح - الإشارات محدودة")
    PYQT6_AVAILABLE = False
    class QObject: pass
    class QThread: pass
    def pyqtSignal(*args): pass

# استيراد الوحدات مع معالجة الأخطاء
try:
    from database.db_manager import DatabaseManager
except ImportError:
    print("⚠️ قاعدة البيانات غير متاحة")
    class DatabaseManager:
        def __init__(self): pass
        def save_video_info(self, *args): pass

try:
    from video_processing.video_downloader import VideoDownloader
except ImportError:
    print("⚠️ محمل الفيديو غير متاح")
    class VideoDownloader:
        def __init__(self): pass
        def download_video(self, *args): return None

try:
    from video_processing.video_processor import VideoProcessor
except ImportError:
    print("⚠️ معالج الفيديو غير متاح")
    class VideoProcessor:
        def __init__(self): pass
        def process_video(self, *args): return None

try:
    from video_processing.face_tracker import FaceTracker
except ImportError:
    print("⚠️ متتبع الوجوه غير متاح")
    class FaceTracker:
        def __init__(self): pass
        def track_faces(self, *args): return None

try:
    from language_ai.speech_to_text import SpeechToText
except ImportError:
    print("⚠️ تفريغ الصوت غير متاح")
    class SpeechToText:
        def __init__(self): pass
        def transcribe(self, *args): return ""

try:
    from language_ai.translator import Translator
except ImportError:
    print("⚠️ المترجم غير متاح")
    class Translator:
        def __init__(self): pass
        def translate(self, *args): return ""

try:
    from language_ai.text_to_speech import TextToSpeech
except ImportError:
    print("⚠️ تحويل النص لصوت غير متاح")
    class TextToSpeech:
        def __init__(self): pass
        def synthesize(self, *args): return None

try:
    from utils.logger import VideoEditorLogger
except ImportError:
    print("⚠️ نظام السجلات غير متاح")
    class VideoEditorLogger:
        def __init__(self, name): pass
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")

class VideoEditorCore(QObject):
    """النواة الرئيسية لمعالج الفيديوهات"""
    
    # إشارات PyQt
    progress_updated = pyqtSignal(int)  # تحديث التقدم
    status_updated = pyqtSignal(str)    # تحديث الحالة
    operation_completed = pyqtSignal(str, str)  # انتهاء العملية (نوع العملية، النتيجة)
    error_occurred = pyqtSignal(str, str)       # حدوث خطأ (نوع العملية، رسالة الخطأ)
    
    def __init__(self, config: dict = None):
        """
        تهيئة النواة الرئيسية
        
        Args:
            config: إعدادات التطبيق
        """
        super().__init__()
        
        self.logger = VideoEditorLogger(__name__)
        self.config = config or {}
        
        # تهيئة قاعدة البيانات
        self.db_manager = DatabaseManager()
        
        # تهيئة وحدات المعالجة
        self.video_downloader = VideoDownloader()
        self.video_processor = VideoProcessor()
        self.face_tracker = FaceTracker()
        
        # تهيئة وحدات الذكاء اللغوي
        openai_key = self.config.get('openai_api_key')
        elevenlabs_key = self.config.get('elevenlabs_api_key')
        deepl_key = self.config.get('deepl_api_key')
        
        self.speech_to_text = SpeechToText(openai_key)
        self.translator = Translator(openai_key, deepl_key)
        self.text_to_speech = TextToSpeech(openai_key, elevenlabs_key)
        
        # متغيرات الحالة
        self.current_operation = None
        self.is_processing = False
        
        self.logger.info("تم تهيئة النواة الرئيسية بنجاح")
    
    def download_video(self, url: str, quality: str = 'best') -> dict:
        """
        تحميل فيديو من رابط
        
        Args:
            url: رابط الفيديو
            quality: جودة التحميل
            
        Returns:
            dict: معلومات التحميل
        """
        try:
            self.current_operation = "تحميل الفيديو"
            self.is_processing = True
            self.status_updated.emit("جاري تحميل الفيديو...")
            
            # تحميل الفيديو
            result = self.video_downloader.download_video(
                url, 
                progress_callback=self._emit_progress,
                quality=quality
            )
            
            if result.get('success'):
                # إضافة الفيديو إلى قاعدة البيانات
                video_data = {
                    'original_filename': Path(result['file_path']).name,
                    'file_path': result['file_path'],
                    'file_size': result.get('file_size', 0),
                    'source_type': 'download',
                    'source_url': url,
                    'format': Path(result['file_path']).suffix[1:]
                }
                
                # الحصول على معلومات الفيديو
                video_info = self.video_processor.get_video_info(result['file_path'])
                video_data.update({
                    'duration': video_info.get('duration', 0),
                    'resolution': f"{video_info.get('width', 0)}x{video_info.get('height', 0)}",
                    'fps': video_info.get('fps', 0)
                })
                
                video_id = self.db_manager.add_video(video_data)
                result['video_id'] = video_id
                
                self.operation_completed.emit("تحميل الفيديو", result['file_path'])
                self.logger.info(f"تم تحميل الفيديو بنجاح: {result['file_path']}")
            else:
                error_msg = result.get('error', 'خطأ غير معروف')
                self.error_occurred.emit("تحميل الفيديو", error_msg)
            
            return result
            
        except Exception as e:
            error_msg = f"خطأ في تحميل الفيديو: {str(e)}"
            self.error_occurred.emit("تحميل الفيديو", error_msg)
            self.logger.error(error_msg)
            return {'success': False, 'error': error_msg}
        finally:
            self.is_processing = False
    
    def process_video_complete(self, video_path: str, processing_options: dict) -> dict:
        """
        معالجة شاملة للفيديو (قص + ترجمة + دبلجة + مونتاج)
        
        Args:
            video_path: مسار الفيديو
            processing_options: خيارات المعالجة
            
        Returns:
            dict: نتائج المعالجة
        """
        try:
            self.current_operation = "معالجة شاملة"
            self.is_processing = True
            self.status_updated.emit("بدء المعالجة الشاملة...")
            
            results = {
                'original_video': video_path,
                'segments': [],
                'transcription': {},
                'translation': {},
                'dubbing_audio': '',
                'final_video': '',
                'success': False
            }
            
            # الخطوة 1: قص الفيديو (إذا كان مطلوباً)
            if processing_options.get('cut_video', False):
                self.status_updated.emit("جاري قص الفيديو...")
                self._emit_progress(10)
                
                segments = self.video_processor.cut_video_into_segments(
                    video_path,
                    processing_options.get('segment_duration', 30),
                    processing_options.get('cut_type', 'equal'),
                    lambda p: self._emit_progress(10 + int(p * 0.2))
                )
                results['segments'] = segments
            
            # الخطوة 2: تفريغ الصوت (إذا كان مطلوباً)
            if processing_options.get('extract_text', False):
                self.status_updated.emit("جاري تفريغ الصوت...")
                self._emit_progress(30)
                
                transcription = self.speech_to_text.transcribe_video(
                    video_path,
                    processing_options.get('stt_method', 'whisper'),
                    processing_options.get('source_language'),
                    lambda p: self._emit_progress(30 + int(p * 0.2))
                )
                results['transcription'] = transcription
            
            # الخطوة 3: الترجمة (إذا كانت مطلوبة)
            if processing_options.get('translate', False) and results['transcription']:
                self.status_updated.emit("جاري ترجمة النص...")
                self._emit_progress(50)
                
                segments = results['transcription'].get('segments', [])
                translated_segments = self.translator.translate_segments(
                    segments,
                    processing_options.get('target_language', 'ar'),
                    processing_options.get('source_language', 'auto'),
                    processing_options.get('translation_service', 'google'),
                    lambda p: self._emit_progress(50 + int(p * 0.15))
                )
                results['translation'] = {'segments': translated_segments}
            
            # الخطوة 4: الدبلجة (إذا كانت مطلوبة)
            if processing_options.get('create_dubbing', False) and results['translation']:
                self.status_updated.emit("جاري إنشاء الدبلجة...")
                self._emit_progress(65)
                
                segments = results['translation']['segments']
                dubbed_segments = self.text_to_speech.create_dubbing_for_segments(
                    segments,
                    processing_options.get('tts_service', 'gtts'),
                    processing_options.get('target_language', 'ar'),
                    processing_options.get('voice_settings', {}),
                    lambda p: self._emit_progress(65 + int(p * 0.2))
                )
                
                # دمج مقاطع الصوت
                output_dir = Path(video_path).parent / "output"
                output_dir.mkdir(exist_ok=True)
                
                dubbing_audio_path = output_dir / f"{Path(video_path).stem}_dubbing.wav"
                dubbing_audio = self.text_to_speech.combine_audio_segments(
                    dubbed_segments,
                    str(dubbing_audio_path)
                )
                results['dubbing_audio'] = dubbing_audio
            
            # الخطوة 5: المونتاج التلقائي (إذا كان مطلوباً)
            if processing_options.get('apply_montage', False):
                self.status_updated.emit("جاري تطبيق المونتاج...")
                self._emit_progress(85)
                
                montage_video = self.video_processor.apply_automatic_montage(
                    video_path,
                    processing_options.get('montage_options', {}),
                    lambda p: self._emit_progress(85 + int(p * 0.1))
                )
                
                # إضافة الدبلجة إلى الفيديو (إذا كانت متاحة)
                if results['dubbing_audio']:
                    output_dir = Path(video_path).parent / "output"
                    final_video_path = output_dir / f"{Path(video_path).stem}_final.mp4"
                    
                    final_video = self.text_to_speech.add_dubbing_to_video(
                        montage_video,
                        results['dubbing_audio'],
                        str(final_video_path),
                        processing_options.get('mix_with_original', False)
                    )
                    results['final_video'] = final_video
                else:
                    results['final_video'] = montage_video
            
            # الخطوة 6: تتبع الوجوه (إذا كان مطلوباً)
            if processing_options.get('track_faces', False):
                self.status_updated.emit("جاري تتبع الوجوه...")
                self._emit_progress(95)
                
                input_video = results['final_video'] or video_path
                tracked_video = self.face_tracker.track_faces_in_video(
                    input_video,
                    processing_options.get('face_tracking_options', {}),
                    lambda p: self._emit_progress(95 + int(p * 0.05))
                )
                
                if tracked_video:
                    results['final_video'] = tracked_video
            
            self._emit_progress(100)
            results['success'] = True
            
            # حفظ معلومات العملية في قاعدة البيانات
            self._save_operation_to_db(video_path, processing_options, results)
            
            self.operation_completed.emit("معالجة شاملة", results['final_video'] or video_path)
            self.status_updated.emit("تم إنجاز المعالجة الشاملة بنجاح")
            
            return results
            
        except Exception as e:
            error_msg = f"خطأ في المعالجة الشاملة: {str(e)}"
            self.error_occurred.emit("معالجة شاملة", error_msg)
            self.logger.error(error_msg)
            return {'success': False, 'error': error_msg}
        finally:
            self.is_processing = False
    
    def _save_operation_to_db(self, video_path: str, options: dict, results: dict):
        """حفظ معلومات العملية في قاعدة البيانات"""
        try:
            # البحث عن الفيديو في قاعدة البيانات
            videos = self.db_manager.get_videos()
            video_id = None
            
            for video in videos:
                if video['file_path'] == video_path:
                    video_id = video['id']
                    break
            
            if video_id:
                operation_data = {
                    'video_id': video_id,
                    'operation_type': 'complete_processing',
                    'operation_data': {
                        'options': options,
                        'results': {
                            'segments_count': len(results.get('segments', [])),
                            'transcription_available': bool(results.get('transcription')),
                            'translation_available': bool(results.get('translation')),
                            'dubbing_available': bool(results.get('dubbing_audio')),
                            'final_video': results.get('final_video', '')
                        }
                    },
                    'status': 'completed' if results['success'] else 'failed',
                    'output_path': results.get('final_video', '')
                }
                
                self.db_manager.add_operation(operation_data)
                
        except Exception as e:
            self.logger.error(f"خطأ في حفظ العملية: {str(e)}")
    
    def _emit_progress(self, progress: int):
        """إرسال إشارة تحديث التقدم"""
        self.progress_updated.emit(progress)
    
    def get_video_info(self, video_path: str) -> dict:
        """الحصول على معلومات الفيديو"""
        return self.video_processor.get_video_info(video_path)
    
    def get_supported_platforms(self) -> List[str]:
        """الحصول على المنصات المدعومة للتحميل"""
        return self.video_downloader.get_supported_platforms()
    
    def get_available_voices(self, service: str = 'gtts') -> List[dict]:
        """الحصول على الأصوات المتاحة"""
        return self.text_to_speech.get_available_voices(service)
    
    def cancel_current_operation(self):
        """إلغاء العملية الحالية"""
        if self.is_processing:
            self.status_updated.emit("جاري إلغاء العملية...")
            # يمكن إضافة منطق إلغاء أكثر تفصيلاً هنا
            self.is_processing = False
            self.status_updated.emit("تم إلغاء العملية")

class VideoProcessingThread(QThread):
    """خيط منفصل لمعالجة الفيديوهات"""
    
    def __init__(self, core: VideoEditorCore, operation: str, *args, **kwargs):
        super().__init__()
        self.core = core
        self.operation = operation
        self.args = args
        self.kwargs = kwargs
    
    def run(self):
        """تشغيل العملية في خيط منفصل"""
        try:
            if self.operation == "download_video":
                self.core.download_video(*self.args, **self.kwargs)
            elif self.operation == "process_video_complete":
                self.core.process_video_complete(*self.args, **self.kwargs)
            # يمكن إضافة عمليات أخرى هنا
                
        except Exception as e:
            self.core.error_occurred.emit(self.operation, str(e))
