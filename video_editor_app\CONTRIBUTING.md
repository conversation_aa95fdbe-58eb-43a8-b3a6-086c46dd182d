# دليل المساهمة - Contributing Guide

نرحب بمساهماتكم في تطوير معالج الفيديوهات المتكامل! 🎉

## 🌟 كيفية المساهمة

### 1. الإبلاغ عن الأخطاء (Bug Reports)

إذا وجدت خطأ في التطبيق:

1. **تحقق من Issues الموجودة** للتأكد من عدم الإبلاغ عن نفس الخطأ
2. **أنشئ Issue جديد** مع المعلومات التالية:
   - وصف واضح للخطأ
   - خطوات إعادة إنتاج الخطأ
   - النتيجة المتوقعة والفعلية
   - معلومات النظام (OS, Python version, etc.)
   - لقطات شاشة إن أمكن

### 2. اقتراح ميزات جديدة (Feature Requests)

لاقتراح ميزة جديدة:

1. **تحقق من Issues الموجودة** للتأكد من عدم اقتراح نفس الميزة
2. **أنشئ Issue جديد** مع:
   - وصف مفصل للميزة المقترحة
   - سبب الحاجة لهذه الميزة
   - أمثلة على الاستخدام
   - تصور للتنفيذ (إن أمكن)

### 3. المساهمة بالكود (Code Contributions)

#### إعداد بيئة التطوير

```bash
# 1. Fork المشروع على GitHub
# 2. Clone المشروع محلياً
git clone https://github.com/your-username/video-editor-app.git
cd video-editor-app

# 3. إنشاء بيئة افتراضية
python -m venv venv
source venv/bin/activate  # Linux/macOS
# أو
venv\Scripts\activate     # Windows

# 4. تثبيت المتطلبات
pip install -r requirements.txt

# 5. تثبيت أدوات التطوير
pip install -r requirements-dev.txt  # إذا كان متوفراً
```

#### سير العمل (Workflow)

1. **إنشاء فرع جديد**:
```bash
git checkout -b feature/اسم-الميزة
# أو
git checkout -b bugfix/اسم-الإصلاح
```

2. **إجراء التغييرات**:
   - اتبع معايير الكود المذكورة أدناه
   - أضف اختبارات للكود الجديد
   - تأكد من عمل الاختبارات الموجودة

3. **اختبار التغييرات**:
```bash
python test_app.py
```

4. **Commit التغييرات**:
```bash
git add .
git commit -m "وصف واضح للتغييرات"
```

5. **Push الفرع**:
```bash
git push origin feature/اسم-الميزة
```

6. **إنشاء Pull Request**:
   - اذهب إلى GitHub وأنشئ Pull Request
   - أضف وصفاً مفصلاً للتغييرات
   - اربط بـ Issues ذات الصلة

## 📝 معايير الكود

### Python Style Guide

نتبع [PEP 8](https://pep8.org/) مع بعض التعديلات:

```python
# استخدم أسماء متغيرات واضحة
video_processor = VideoProcessor()
user_input = get_user_input()

# أضف تعليقات باللغة العربية للوضوح
def process_video(video_path: str) -> str:
    """
    معالجة الفيديو وإرجاع مسار الملف المعالج
    
    Args:
        video_path: مسار الفيديو المراد معالجته
        
    Returns:
        str: مسار الفيديو المعالج
    """
    # بدء معالجة الفيديو
    result = self.core.process_video(video_path)
    return result
```

### هيكل الملفات

```
video_editor_app/
├── gui/                    # واجهة المستخدم
├── src/                    # النواة الرئيسية
├── video_processing/       # معالجة الفيديو
├── language_ai/           # الذكاء اللغوي
├── database/              # قاعدة البيانات
├── utils/                 # أدوات مساعدة
├── tests/                 # الاختبارات
└── docs/                  # التوثيق
```

### التوثيق

- أضف docstrings لجميع الدوال والفئات
- استخدم type hints
- أضف تعليقات للكود المعقد
- حدث README.md عند الحاجة

### الاختبارات

```python
import unittest
from video_processing.video_processor import VideoProcessor

class TestVideoProcessor(unittest.TestCase):
    def setUp(self):
        self.processor = VideoProcessor()
    
    def test_video_info_extraction(self):
        """اختبار استخراج معلومات الفيديو"""
        # كود الاختبار هنا
        pass
```

## 🔍 مراجعة الكود

### قائمة المراجعة

قبل إرسال Pull Request، تأكد من:

- [ ] الكود يتبع معايير المشروع
- [ ] جميع الاختبارات تعمل
- [ ] أضفت اختبارات للكود الجديد
- [ ] التوثيق محدث
- [ ] لا توجد تحذيرات أو أخطاء
- [ ] الكود محسن للأداء

### عملية المراجعة

1. **المراجعة التلقائية**: GitHub Actions سيقوم بفحص الكود
2. **مراجعة الفريق**: أحد أعضاء الفريق سيراجع الكود
3. **التعديلات**: قد نطلب تعديلات قبل الدمج
4. **الدمج**: بعد الموافقة سيتم دمج الكود

## 🎯 أولويات التطوير

### مطلوب بشدة
- تحسين الأداء للفيديوهات الكبيرة
- دعم المزيد من تنسيقات الفيديو
- تحسين دقة تتبع الوجوه
- إضافة المزيد من اللغات للواجهة

### مرحب به
- تحسين واجهة المستخدم
- إضافة اختبارات جديدة
- تحسين التوثيق
- إصلاح الأخطاء الصغيرة

### مشاريع كبيرة
- دعم معالجة الفيديو بالذكاء الاصطناعي
- تطبيق ويب مصاحب
- دعم البث المباشر
- تطبيق موبايل

## 🌍 الترجمة والتوطين

نرحب بالمساهمة في ترجمة التطبيق:

1. **إضافة لغة جديدة**:
   - أنشئ ملف ترجمة في `locales/`
   - اتبع تنسيق JSON الموجود
   - اختبر الترجمة في الواجهة

2. **تحسين الترجمات الموجودة**:
   - راجع ملفات الترجمة
   - أصلح الأخطاء اللغوية
   - أضف ترجمات مفقودة

## 📞 التواصل

### قنوات التواصل

- **GitHub Issues**: للأخطاء والاقتراحات
- **GitHub Discussions**: للنقاشات العامة
- **Discord**: [رابط الخادم] للدردشة المباشرة
- **البريد الإلكتروني**: <EMAIL>

### آداب التواصل

- كن محترماً ومهذباً
- استخدم لغة واضحة ومفهومة
- ابحث قبل السؤال
- ساعد الآخرين عند الإمكان

## 🏆 الاعتراف بالمساهمين

جميع المساهمين سيتم ذكرهم في:

- ملف CONTRIBUTORS.md
- صفحة About في التطبيق
- ملاحظات الإصدار
- README.md

## 📋 قائمة المهام للمبتدئين

إذا كنت جديداً في المساهمة، ابدأ بهذه المهام:

- [ ] إصلاح أخطاء إملائية في التوثيق
- [ ] إضافة تعليقات للكود
- [ ] كتابة اختبارات بسيطة
- [ ] تحسين رسائل الخطأ
- [ ] إضافة أمثلة للاستخدام

## 🔄 دورة الإصدار

- **إصدارات صغيرة**: كل أسبوعين (إصلاحات وتحسينات)
- **إصدارات متوسطة**: كل شهر (ميزات جديدة)
- **إصدارات كبيرة**: كل 3-6 أشهر (تغييرات جوهرية)

## 📚 موارد مفيدة

- [Python Documentation](https://docs.python.org/)
- [PyQt6 Documentation](https://doc.qt.io/qtforpython/)
- [MoviePy Documentation](https://moviepy.readthedocs.io/)
- [OpenCV Documentation](https://docs.opencv.org/)

---

شكراً لك على اهتمامك بالمساهمة في معالج الفيديوهات المتكامل! 🙏

كل مساهمة، مهما كانت صغيرة، تساعد في تحسين التطبيق للجميع.
