#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحديث وإصلاح شامل - Complete Update and Fix
يحدث جميع المكتبات ويصلح كل المشاكل
"""

import sys
import os
import subprocess
import shutil
import time
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_header(title):
    """طباعة عنوان جميل"""
    print("\n" + "="*60)
    print(f"🔧 {title}")
    print("="*60)

def print_step(step, description):
    """طباعة خطوة"""
    print(f"\n📌 الخطوة {step}: {description}")
    print("-" * 40)

def update_pip():
    """تحديث pip"""
    print("🔄 تحديث pip...")
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✅ تم تحديث pip")
        return True
    except:
        print("⚠️ فشل في تحديث pip")
        return False

def install_latest_packages():
    """تثبيت أحدث إصدارات المكتبات"""
    print("📦 تثبيت أحدث إصدارات المكتبات...")
    
    packages = [
        "PyQt6>=6.7.0",
        "requests>=2.32.0",
        "Pillow>=10.4.0",
        "opencv-python>=4.10.0",
        "numpy>=1.26.0",
        "matplotlib>=3.8.0",
        "psutil>=5.9.0"
    ]
    
    success_count = 0
    for package in packages:
        try:
            print(f"  📥 تثبيت {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, 
                "--upgrade", "--force-reinstall"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"  ✅ {package}")
            success_count += 1
        except:
            print(f"  ❌ فشل {package}")
    
    print(f"\n📊 تم تثبيت {success_count}/{len(packages)} مكتبة")
    return success_count == len(packages)

def create_modern_gui():
    """إنشاء واجهة حديثة وجميلة"""
    print("🎨 إنشاء واجهة حديثة وجميلة...")
    
    modern_gui_content = '''# -*- coding: utf-8 -*-
"""
الواجهة الحديثة والجميلة - Modern Beautiful GUI
"""

import sys
import os
import json
import threading
import time
from pathlib import Path
from urllib.parse import urlparse

try:
    from PyQt6.QtWidgets import *
    from PyQt6.QtCore import *
    from PyQt6.QtGui import *
    PYQT6_AVAILABLE = True
except ImportError as e:
    print(f"❌ خطأ في استيراد PyQt6: {e}")
    PYQT6_AVAILABLE = False
    raise

class ModernButton(QPushButton):
    """زر حديث مع تأثيرات جميلة"""
    
    def __init__(self, text, icon=None, color="primary"):
        super().__init__(text)
        self.color = color
        self.setup_style()
        
        if icon:
            self.setIcon(QIcon(icon))
            self.setIconSize(QSize(20, 20))
    
    def setup_style(self):
        """إعداد ستايل الزر"""
        colors = {
            "primary": {"bg": "#4CAF50", "hover": "#45a049", "pressed": "#3d8b40"},
            "secondary": {"bg": "#2196F3", "hover": "#1976D2", "pressed": "#1565C0"},
            "success": {"bg": "#28a745", "hover": "#218838", "pressed": "#1e7e34"},
            "warning": {"bg": "#ffc107", "hover": "#e0a800", "pressed": "#d39e00"},
            "danger": {"bg": "#dc3545", "hover": "#c82333", "pressed": "#bd2130"},
            "info": {"bg": "#17a2b8", "hover": "#138496", "pressed": "#117a8b"}
        }
        
        color_set = colors.get(self.color, colors["primary"])
        
        self.setStyleSheet(f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color_set["bg"]}, stop:1 {color_set["hover"]});
                color: white;
                border: none;
                border-radius: 12px;
                padding: 12px 24px;
                font-weight: bold;
                font-size: 13px;
                min-height: 20px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {color_set["hover"]}, stop:1 {color_set["pressed"]});
                transform: translateY(-2px);
            }}
            QPushButton:pressed {{
                background: {color_set["pressed"]};
                transform: translateY(0px);
            }}
            QPushButton:disabled {{
                background: #cccccc;
                color: #666666;
            }}
        """)

class ModernCard(QFrame):
    """بطاقة حديثة مع ظلال"""
    
    def __init__(self, title="", content_widget=None):
        super().__init__()
        self.setup_ui(title, content_widget)
    
    def setup_ui(self, title, content_widget):
        """إعداد واجهة البطاقة"""
        self.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 15px;
                border: 1px solid #e0e0e0;
            }
        """)
        
        # إضافة تأثير الظل
        shadow = QGraphicsDropShadowEffect()
        shadow.setBlurRadius(20)
        shadow.setColor(QColor(0, 0, 0, 30))
        shadow.setOffset(0, 5)
        self.setGraphicsEffect(shadow)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)
        
        if title:
            title_label = QLabel(title)
            title_label.setStyleSheet("""
                QLabel {
                    font-size: 18px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }
            """)
            layout.addWidget(title_label)
        
        if content_widget:
            layout.addWidget(content_widget)

class ModernProgressBar(QProgressBar):
    """شريط تقدم حديث"""
    
    def __init__(self):
        super().__init__()
        self.setup_style()
    
    def setup_style(self):
        """إعداد ستايل شريط التقدم"""
        self.setStyleSheet("""
            QProgressBar {
                border: none;
                border-radius: 10px;
                text-align: center;
                font-weight: bold;
                font-size: 12px;
                height: 20px;
                background-color: #f0f0f0;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:0.5 #66BB6A, stop:1 #4CAF50);
                border-radius: 10px;
                margin: 2px;
            }
        """)

class ModernInput(QLineEdit):
    """حقل إدخال حديث"""
    
    def __init__(self, placeholder=""):
        super().__init__()
        self.setPlaceholderText(placeholder)
        self.setup_style()
    
    def setup_style(self):
        """إعداد ستايل حقل الإدخال"""
        self.setStyleSheet("""
            QLineEdit {
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 12px 15px;
                font-size: 13px;
                background-color: white;
                selection-background-color: #4CAF50;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
                outline: none;
            }
            QLineEdit:hover {
                border-color: #66BB6A;
            }
        """)

class ModernComboBox(QComboBox):
    """قائمة منسدلة حديثة"""
    
    def __init__(self):
        super().__init__()
        self.setup_style()
    
    def setup_style(self):
        """إعداد ستايل القائمة المنسدلة"""
        self.setStyleSheet("""
            QComboBox {
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 10px 15px;
                font-size: 13px;
                background-color: white;
                min-width: 150px;
            }
            QComboBox:focus {
                border-color: #4CAF50;
            }
            QComboBox:hover {
                border-color: #66BB6A;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #666;
                margin-right: 10px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                background-color: white;
                selection-background-color: #4CAF50;
                selection-color: white;
            }
        """)

class ModernListWidget(QListWidget):
    """قائمة حديثة"""
    
    def __init__(self):
        super().__init__()
        self.setup_style()
    
    def setup_style(self):
        """إعداد ستايل القائمة"""
        self.setStyleSheet("""
            QListWidget {
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                background-color: white;
                padding: 5px;
            }
            QListWidget::item {
                border-radius: 8px;
                padding: 12px;
                margin: 2px;
                border-bottom: 1px solid #f0f0f0;
            }
            QListWidget::item:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
            }
            QListWidget::item:hover {
                background-color: #f8f9fa;
                border: 1px solid #4CAF50;
            }
        """)

class ModernTableWidget(QTableWidget):
    """جدول حديث"""
    
    def __init__(self):
        super().__init__()
        self.setup_style()
    
    def setup_style(self):
        """إعداد ستايل الجدول"""
        self.setStyleSheet("""
            QTableWidget {
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                background-color: white;
                gridline-color: #f0f0f0;
                selection-background-color: #4CAF50;
            }
            QTableWidget::item {
                padding: 12px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTableWidget::item:selected {
                background-color: #4CAF50;
                color: white;
            }
            QTableWidget::item:hover {
                background-color: #f8f9fa;
            }
            QHeaderView::section {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                padding: 12px;
                border: none;
                border-bottom: 2px solid #4CAF50;
                font-weight: bold;
                color: #2c3e50;
            }
        """)

class ModernTextEdit(QTextEdit):
    """منطقة نص حديثة"""
    
    def __init__(self, placeholder=""):
        super().__init__()
        self.setPlaceholderText(placeholder)
        self.setup_style()
    
    def setup_style(self):
        """إعداد ستايل منطقة النص"""
        self.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e0e0e0;
                border-radius: 10px;
                padding: 15px;
                font-size: 13px;
                background-color: white;
                selection-background-color: #4CAF50;
                line-height: 1.5;
            }
            QTextEdit:focus {
                border-color: #4CAF50;
            }
        """)
'''
    
    try:
        modern_gui_path = project_root / "gui" / "modern_components.py"
        with open(modern_gui_path, 'w', encoding='utf-8') as f:
            f.write(modern_gui_content)
        print("✅ تم إنشاء المكونات الحديثة")
        return True
    except Exception as e:
        print(f"❌ فشل في إنشاء المكونات الحديثة: {e}")
        return False

def create_beautiful_main_window():
    """إنشاء النافذة الرئيسية الجميلة"""
    print("🎨 إنشاء النافذة الرئيسية الجميلة...")

    try:
        # التحقق من وجود النافذة الجميلة
        beautiful_window_path = project_root / "gui" / "beautiful_main_window.py"
        if beautiful_window_path.exists():
            print("✅ النافذة الجميلة موجودة")
            return True
        else:
            print("⚠️ النافذة الجميلة غير موجودة - سيتم إنشاؤها")
            return True
    except Exception as e:
        print(f"❌ خطأ في فحص النافذة الجميلة: {e}")
        return False

def fix_all_imports():
    """إصلاح جميع الاستيرادات"""
    print("🔧 إصلاح جميع الاستيرادات...")
    
    # إصلاح gui/__init__.py
    gui_init_content = '''# -*- coding: utf-8 -*-
"""
وحدة واجهة المستخدم الرسومية المحدثة
Updated GUI Module
"""

try:
    from .beautiful_main_window import BeautifulMainWindow as MainWindow
    print("✅ تم استيراد BeautifulMainWindow")
except ImportError:
    try:
        from .main_window_complete import CompleteMainWindow as MainWindow
        print("✅ تم استيراد CompleteMainWindow")
    except ImportError:
        try:
            from .main_window import MainWindow
            print("✅ تم استيراد MainWindow الأصلي")
        except ImportError:
            print("❌ فشل في استيراد أي نافذة رئيسية")
            
            # إنشاء نافذة طوارئ حديثة
            from PyQt6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QLabel
            from PyQt6.QtCore import Qt
            
            class MainWindow(QMainWindow):
                def __init__(self):
                    super().__init__()
                    self.setWindowTitle("معالج الفيديوهات - الإصدار المحدث")
                    self.setGeometry(100, 100, 1000, 700)
                    
                    # إعداد الستايل الحديث
                    self.setStyleSheet("""
                        QMainWindow {
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #f8f9fa, stop:1 #e9ecef);
                        }
                    """)
                    
                    central_widget = QWidget()
                    self.setCentralWidget(central_widget)
                    
                    layout = QVBoxLayout(central_widget)
                    
                    label = QLabel("🎬 معالج الفيديوهات المتكامل - الإصدار المحدث")
                    label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    label.setStyleSheet("""
                        QLabel {
                            font-size: 24px;
                            font-weight: bold;
                            color: #2c3e50;
                            padding: 50px;
                            background-color: white;
                            border-radius: 15px;
                            margin: 20px;
                        }
                    """)
                    layout.addWidget(label)

__all__ = ['MainWindow']
'''
    
    try:
        gui_init_path = project_root / "gui" / "__init__.py"
        with open(gui_init_path, 'w', encoding='utf-8') as f:
            f.write(gui_init_content)
        print("✅ تم إصلاح gui/__init__.py")
        return True
    except Exception as e:
        print(f"❌ فشل في إصلاح الاستيرادات: {e}")
        return False

def create_output_structure():
    """إنشاء هيكل مجلدات الإخراج"""
    print("📁 إنشاء هيكل مجلدات الإخراج...")
    
    base_dir = Path.home() / "VideoEditor_Pro_Output"
    directories = [
        base_dir,
        base_dir / "processed_videos",
        base_dir / "downloads",
        base_dir / "transcriptions", 
        base_dir / "translations",
        base_dir / "dubbing",
        base_dir / "projects",
        base_dir / "temp",
        base_dir / "exports"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"  ✅ {directory.name}")
    
    return str(base_dir)

def test_updated_system():
    """اختبار النظام المحدث"""
    print("🧪 اختبار النظام المحدث...")
    
    tests = []
    
    # اختبار Python
    tests.append(("Python", sys.version_info >= (3, 8), f"Python {sys.version_info.major}.{sys.version_info.minor}"))
    
    # اختبار المكتبات
    libraries = ["PyQt6", "requests", "PIL", "numpy"]
    for lib in libraries:
        try:
            __import__(lib)
            tests.append((lib, True, f"✅ {lib}"))
        except ImportError:
            tests.append((lib, False, f"❌ {lib}"))
    
    # اختبار الواجهة
    try:
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        tests.append(("QApplication", True, "✅ QApplication"))
    except:
        tests.append(("QApplication", False, "❌ QApplication"))
    
    # عرض النتائج
    passed = sum(1 for test in tests if test[1])
    total = len(tests)
    
    print(f"\n📊 نتائج الاختبار: {passed}/{total} نجح")
    
    for test in tests:
        status = "✅" if test[1] else "❌"
        print(f"  {status} {test[2]}")
    
    return passed == total

def main():
    """الدالة الرئيسية للتحديث والإصلاح"""
    print_header("تحديث وإصلاح شامل لمعالج الفيديوهات")
    
    try:
        print_step(1, "تحديث pip")
        update_pip()
        
        print_step(2, "تثبيت أحدث إصدارات المكتبات")
        install_latest_packages()
        
        print_step(3, "إنشاء المكونات الحديثة")
        create_modern_gui()
        
        print_step(4, "إصلاح الاستيرادات")
        fix_all_imports()
        
        print_step(5, "إنشاء هيكل مجلدات الإخراج")
        output_dir = create_output_structure()
        
        print_step(6, "اختبار النظام المحدث")
        test_success = test_updated_system()
        
        print_header("النتيجة النهائية")
        
        if test_success:
            print("🎉 تم التحديث والإصلاح بنجاح!")
            print("✅ جميع المكتبات محدثة")
            print("✅ جميع المكونات تعمل")
            print("✅ الواجهة جاهزة")
            print(f"✅ مجلد الإخراج: {output_dir}")
            
            print("\n🚀 التطبيق جاهز للتشغيل!")
            print("📌 استخدم: python main_beautiful.py")
            
            return True
        else:
            print("⚠️ بعض المشاكل لا تزال موجودة")
            print("💡 جرب تشغيل السكريپت مرة أخرى")
            return False
            
    except Exception as e:
        print(f"\n❌ خطأ في التحديث: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n{'🎉 اضغط Enter للمتابعة...' if success else '❌ اضغط Enter للخروج...'}")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحديث")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ كارثي: {e}")
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
