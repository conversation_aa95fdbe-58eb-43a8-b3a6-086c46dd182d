# -*- coding: utf-8 -*-
"""
وحدة الذكاء اللغوي
Language AI Module for Video Editor Application
"""

try:
    from .speech_to_text import SpeechToText
except ImportError as e:
    print(f"⚠️ خطأ في استيراد تفريغ الصوت: {e}")
    class SpeechToText:
        def __init__(self): pass
        def transcribe(self, *args): return ""

try:
    from .translator import Translator
except ImportError as e:
    print(f"⚠️ خطأ في استيراد المترجم: {e}")
    class Translator:
        def __init__(self): pass
        def translate(self, *args): return ""

try:
    from .text_to_speech import TextToSpeech
except ImportError as e:
    print(f"⚠️ خطأ في استيراد تحويل النص لصوت: {e}")
    class TextToSpeech:
        def __init__(self): pass
        def synthesize(self, *args): return None

__all__ = ['SpeechToText', 'Translator', 'TextToSpeech']
