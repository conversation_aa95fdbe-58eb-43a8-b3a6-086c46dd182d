# -*- coding: utf-8 -*-
"""
إصلاح RTL مخصص
Custom RTL Fix
"""

def setup_rtl_safe(app):
    """إعداد RTL آمن ومتوافق مع جميع إصدارات PyQt6"""
    
    # قائمة الطرق المختلفة للوصول لـ RightToLeft
    rtl_attempts = [
        # الطريقة الحديثة (PyQt6 6.5+)
        lambda: getattr(__import__('PyQt6.QtCore', fromlist=['Qt']).Qt.LayoutDirection, 'RightToLeft', None),
        
        # الطريقة القديمة (PyQt6 < 6.5)
        lambda: getattr(__import__('PyQt6.QtCore', fromlist=['Qt']).Qt, 'RightToLeft', None),
        
        # الرقم المباشر (دائماً يعمل)
        lambda: 2,
    ]
    
    for i, attempt in enumerate(rtl_attempts):
        try:
            rtl_value = attempt()
            if rtl_value is not None:
                app.setLayoutDirection(rtl_value)
                print(f"✅ RTL تم تعيينه بالطريقة {i+1} (القيمة: {rtl_value})")
                return True
        except Exception as e:
            print(f"⚠️ الطريقة {i+1} فشلت: {e}")
            continue
    
    print("❌ فشل في تعيين RTL بجميع الطرق")
    return False

# للاستخدام في main.py
if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    app = QApplication([])
    setup_rtl_safe(app)
