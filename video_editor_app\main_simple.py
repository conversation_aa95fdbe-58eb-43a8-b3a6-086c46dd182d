#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة من التطبيق الرئيسي
Simplified Version of Main Application
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_simple_app():
    """إنشاء تطبيق بسيط"""
    print("🚀 إنشاء تطبيق بسيط...")
    
    try:
        from PyQt6.QtWidgets import (
            QApplication, QMainWindow, QWidget, QVBoxLayout, 
            QHBoxLayout, QLabel, QPushButton, QTextEdit,
            QTabWidget, QMessageBox
        )
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("معالج الفيديوهات - نسخة مبسطة")
        
        # إعداد RTL بشكل آمن
        try:
            app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        except:
            try:
                app.setLayoutDirection(Qt.RightToLeft)
            except:
                app.setLayoutDirection(2)
        
        # إنشاء النافذة الرئيسية
        window = QMainWindow()
        window.setWindowTitle("معالج الفيديوهات المتكامل - نسخة مبسطة")
        window.resize(800, 600)
        
        # إنشاء الواجهة
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # عنوان
        title = QLabel("معالج الفيديوهات المتكامل")
        title.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        title.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title)
        
        # تبويبات بسيطة
        tabs = QTabWidget()
        layout.addWidget(tabs)
        
        # تبويب الترحيب
        welcome_tab = QWidget()
        welcome_layout = QVBoxLayout(welcome_tab)
        
        welcome_text = QTextEdit()
        welcome_text.setHtml("""
        <div dir="rtl" style="text-align: center;">
        <h2>مرحباً بك في معالج الفيديوهات المتكامل</h2>
        <p>هذه نسخة مبسطة للاختبار</p>
        <p>الميزات المتاحة:</p>
        <ul>
        <li>واجهة مستخدم عربية</li>
        <li>دعم RTL</li>
        <li>تبويبات منظمة</li>
        </ul>
        </div>
        """)
        welcome_text.setReadOnly(True)
        welcome_layout.addWidget(welcome_text)
        
        tabs.addTab(welcome_tab, "الترحيب")
        
        # تبويب الإعدادات
        settings_tab = QWidget()
        settings_layout = QVBoxLayout(settings_tab)
        
        settings_label = QLabel("إعدادات التطبيق")
        settings_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        settings_layout.addWidget(settings_label)
        
        test_button = QPushButton("اختبار الاستجابة")
        test_button.clicked.connect(lambda: QMessageBox.information(
            window, "اختبار", "التطبيق يستجيب بشكل صحيح!"
        ))
        settings_layout.addWidget(test_button)
        
        tabs.addTab(settings_tab, "الإعدادات")
        
        # شريط الحالة
        status_bar = window.statusBar()
        status_bar.showMessage("التطبيق جاهز - نسخة مبسطة")
        
        # عرض النافذة
        window.show()
        
        print("✅ تم إنشاء التطبيق البسيط بنجاح")
        return app, window
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء التطبيق البسيط: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def main():
    """الدالة الرئيسية"""
    print("🎬 معالج الفيديوهات المتكامل - نسخة مبسطة")
    print("="*50)
    
    try:
        # إنشاء التطبيق
        app, window = create_simple_app()
        
        if app is None:
            print("❌ فشل في إنشاء التطبيق")
            return False
        
        print("🚀 تشغيل التطبيق...")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
