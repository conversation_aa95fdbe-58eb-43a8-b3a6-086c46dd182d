#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريپت تشغيل آمن للتطبيق
Safe Run Script for Video Editor Application
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_essential_packages():
    """تثبيت الحزم الأساسية"""
    essential_packages = [
        "PyQt6",
        "requests", 
        "Pillow",
        "pathlib2; python_version < '3.4'"
    ]
    
    print("📦 تثبيت الحزم الأساسية...")
    
    for package in essential_packages:
        try:
            print(f"  تثبيت {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet"
            ])
            print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"  ⚠️ فشل في تثبيت {package}")

def install_video_packages():
    """تثبيت حزم معالجة الفيديو"""
    video_packages = [
        "moviepy",
        "opencv-python"
    ]
    
    print("🎬 تثبيت حزم معالجة الفيديو...")
    
    for package in video_packages:
        try:
            print(f"  تثبيت {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet"
            ])
            print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"  ⚠️ فشل في تثبيت {package} - سيتم تخطيه")

def install_optional_packages():
    """تثبيت الحزم الاختيارية"""
    optional_packages = [
        "qdarkstyle",
        "tqdm",
        "psutil"
    ]
    
    print("⚙️ تثبيت الحزم الاختيارية...")
    
    for package in optional_packages:
        try:
            print(f"  تثبيت {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet"
            ])
            print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"  ⚠️ فشل في تثبيت {package} - سيتم تخطيه")

def test_imports():
    """اختبار استيراد الحزم الأساسية"""
    print("🧪 اختبار الحزم...")
    
    tests = [
        ("PyQt6", "from PyQt6.QtWidgets import QApplication"),
        ("requests", "import requests"),
        ("Pillow", "from PIL import Image"),
        ("moviepy", "import moviepy"),
        ("opencv", "import cv2"),
    ]
    
    results = {}
    
    for name, import_cmd in tests:
        try:
            exec(import_cmd)
            print(f"  ✅ {name}")
            results[name] = True
        except ImportError:
            print(f"  ❌ {name}")
            results[name] = False
    
    return results

def run_application():
    """تشغيل التطبيق"""
    print("🚀 تشغيل التطبيق...")
    
    main_script = Path(__file__).parent / "main.py"
    
    if not main_script.exists():
        print("❌ ملف main.py غير موجود")
        return False
    
    try:
        subprocess.run([sys.executable, str(main_script)], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🎬 معالج الفيديوهات المتكامل - تشغيل آمن")
    print("=" * 60)
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    print()
    
    # تثبيت الحزم
    install_essential_packages()
    print()
    
    install_video_packages()
    print()
    
    install_optional_packages()
    print()
    
    # اختبار الاستيراد
    results = test_imports()
    print()
    
    # التحقق من الحد الأدنى للمتطلبات
    essential_available = results.get('PyQt6', False) and results.get('requests', False)
    
    if not essential_available:
        print("❌ الحزم الأساسية غير متوفرة")
        print("يرجى تثبيت PyQt6 و requests يدوياً:")
        print("pip install PyQt6 requests")
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    print("✅ الحد الأدنى من المتطلبات متوفر")
    
    if not results.get('moviepy', False):
        print("⚠️ MoviePy غير متوفر - معالجة الفيديو محدودة")
    
    if not results.get('opencv', False):
        print("⚠️ OpenCV غير متوفر - معالجة الصور محدودة")
    
    print()
    
    # تشغيل التطبيق
    if run_application():
        print("✅ تم إغلاق التطبيق بنجاح")
    else:
        print("❌ حدث خطأ في التطبيق")
        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    main()
