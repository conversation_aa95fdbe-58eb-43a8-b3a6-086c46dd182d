# -*- coding: utf-8 -*-
"""
وحدة تحويل الكلام إلى نص
Speech-to-Text Module for Video Editor Application
"""

import os
import tempfile
from pathlib import Path
from typing import dict, Optional, Callable
import logging

try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False
    print("⚠️ Whisper غير مثبت - تفريغ الصوت محدود")

try:
    import speech_recognition as sr
    SPEECH_RECOGNITION_AVAILABLE = True
except ImportError:
    SPEECH_RECOGNITION_AVAILABLE = False
    print("⚠️ SpeechRecognition غير مثبت - تفريغ الصوت محدود")

try:
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("⚠️ MoviePy غير مثبت - استخراج الصوت محدود")

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️ OpenAI غير مثبت - تفريغ متقدم محدود")

from utils.logger import VideoEditorLogger

class SpeechToText:
    """فئة تحويل الكلام إلى نص"""
    
    def __init__(self, api_key: str = None):
        """
        تهيئة محول الكلام إلى نص
        
        Args:
            api_key: مفتاح OpenAI API (اختياري)
        """
        self.logger = VideoEditorLogger(__name__)
        self.api_key = api_key
        
        # تحميل نموذج Whisper
        if WHISPER_AVAILABLE:
            try:
                self.whisper_model = whisper.load_model("base")
                self.logger.info("تم تحميل نموذج Whisper بنجاح")
            except Exception as e:
                self.logger.warning(f"فشل في تحميل نموذج Whisper: {str(e)}")
                self.whisper_model = None
        else:
            self.whisper_model = None
        
        # إعداد مُعرف الكلام
        if SPEECH_RECOGNITION_AVAILABLE:
            self.recognizer = sr.Recognizer()
        else:
            self.recognizer = None
        
        # إعداد OpenAI
        if OPENAI_AVAILABLE and api_key:
            openai.api_key = api_key
    
    def extract_audio_from_video(self, video_path: str) -> str:
        """
        استخراج الصوت من الفيديو
        
        Args:
            video_path: مسار الفيديو
            
        Returns:
            str: مسار ملف الصوت المستخرج
        """
        try:
            if not MOVIEPY_AVAILABLE:
                raise Exception("مكتبة MoviePy مطلوبة لاستخراج الصوت")
            
            self.logger.info(f"استخراج الصوت من: {video_path}")
            
            with VideoFileClip(video_path) as video:
                if video.audio is None:
                    raise Exception("الفيديو لا يحتوي على صوت")
                
                # إنشاء ملف مؤقت للصوت
                temp_audio = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
                temp_audio_path = temp_audio.name
                temp_audio.close()
                
                # استخراج الصوت
                video.audio.write_audiofile(
                    temp_audio_path,
                    verbose=False,
                    logger=None
                )
                
                self.logger.info(f"تم استخراج الصوت إلى: {temp_audio_path}")
                return temp_audio_path
                
        except Exception as e:
            self.logger.error(f"خطأ في استخراج الصوت: {str(e)}")
            return ""
    
    def transcribe_audio_whisper(self, audio_path: str, language: str = None,
                                progress_callback: Callable = None) -> dict:
        """
        تفريغ الصوت باستخدام Whisper
        
        Args:
            audio_path: مسار ملف الصوت
            language: لغة الصوت (اختياري)
            progress_callback: دالة تحديث التقدم
            
        Returns:
            dict: النص المفرغ مع التوقيتات
        """
        try:
            if not self.whisper_model:
                raise Exception("نموذج Whisper غير متاح")
            
            self.logger.operation_start("تفريغ الصوت بـ Whisper", audio_path)
            
            # تفريغ الصوت
            result = self.whisper_model.transcribe(
                audio_path,
                language=language,
                word_timestamps=True,
                verbose=False
            )
            
            # تنسيق النتائج
            transcription = {
                'text': result['text'],
                'language': result['language'],
                'segments': [],
                'words': []
            }
            
            # إضافة المقاطع مع التوقيتات
            for segment in result['segments']:
                transcription['segments'].append({
                    'start': segment['start'],
                    'end': segment['end'],
                    'text': segment['text'].strip()
                })
                
                # إضافة الكلمات إذا كانت متاحة
                if 'words' in segment:
                    for word in segment['words']:
                        transcription['words'].append({
                            'start': word['start'],
                            'end': word['end'],
                            'word': word['word'].strip()
                        })
            
            if progress_callback:
                progress_callback(100)
            
            self.logger.operation_complete("تفريغ الصوت بـ Whisper", audio_path, 0)
            return transcription
            
        except Exception as e:
            self.logger.operation_error("تفريغ الصوت بـ Whisper", audio_path, str(e))
            return {}
    
    def transcribe_audio_openai(self, audio_path: str, language: str = None,
                               progress_callback: Callable = None) -> dict:
        """
        تفريغ الصوت باستخدام OpenAI API
        
        Args:
            audio_path: مسار ملف الصوت
            language: لغة الصوت (اختياري)
            progress_callback: دالة تحديث التقدم
            
        Returns:
            dict: النص المفرغ
        """
        try:
            if not OPENAI_AVAILABLE or not self.api_key:
                raise Exception("OpenAI API غير متاح أو مفتاح API غير محدد")
            
            self.logger.operation_start("تفريغ الصوت بـ OpenAI", audio_path)
            
            with open(audio_path, 'rb') as audio_file:
                # استخدام Whisper API من OpenAI
                response = openai.Audio.transcribe(
                    model="whisper-1",
                    file=audio_file,
                    language=language,
                    response_format="verbose_json",
                    timestamp_granularities=["segment", "word"]
                )
            
            # تنسيق النتائج
            transcription = {
                'text': response['text'],
                'language': response.get('language', 'unknown'),
                'segments': [],
                'words': []
            }
            
            # إضافة المقاطع
            if 'segments' in response:
                for segment in response['segments']:
                    transcription['segments'].append({
                        'start': segment['start'],
                        'end': segment['end'],
                        'text': segment['text'].strip()
                    })
            
            # إضافة الكلمات
            if 'words' in response:
                for word in response['words']:
                    transcription['words'].append({
                        'start': word['start'],
                        'end': word['end'],
                        'word': word['word'].strip()
                    })
            
            if progress_callback:
                progress_callback(100)
            
            self.logger.operation_complete("تفريغ الصوت بـ OpenAI", audio_path, 0)
            return transcription
            
        except Exception as e:
            self.logger.operation_error("تفريغ الصوت بـ OpenAI", audio_path, str(e))
            return {}
    
    def transcribe_audio_google(self, audio_path: str, language: str = 'ar-SA',
                               progress_callback: Callable = None) -> dict:
        """
        تفريغ الصوت باستخدام Google Speech Recognition
        
        Args:
            audio_path: مسار ملف الصوت
            language: لغة الصوت
            progress_callback: دالة تحديث التقدم
            
        Returns:
            dict: النص المفرغ
        """
        try:
            if not self.recognizer:
                raise Exception("مكتبة SpeechRecognition غير متاحة")
            
            self.logger.operation_start("تفريغ الصوت بـ Google", audio_path)
            
            # تحميل ملف الصوت
            with sr.AudioFile(audio_path) as source:
                # تقليل الضوضاء
                self.recognizer.adjust_for_ambient_noise(source)
                
                # تسجيل الصوت
                audio_data = self.recognizer.record(source)
            
            # تفريغ الصوت
            text = self.recognizer.recognize_google(
                audio_data,
                language=language,
                show_all=False
            )
            
            transcription = {
                'text': text,
                'language': language,
                'segments': [{'start': 0, 'end': 0, 'text': text}],
                'words': []
            }
            
            if progress_callback:
                progress_callback(100)
            
            self.logger.operation_complete("تفريغ الصوت بـ Google", audio_path, 0)
            return transcription
            
        except sr.UnknownValueError:
            self.logger.error("لم يتمكن Google من فهم الصوت")
            return {}
        except sr.RequestError as e:
            self.logger.error(f"خطأ في طلب Google Speech Recognition: {str(e)}")
            return {}
        except Exception as e:
            self.logger.operation_error("تفريغ الصوت بـ Google", audio_path, str(e))
            return {}
    
    def transcribe_video(self, video_path: str, method: str = 'whisper',
                        language: str = None, progress_callback: Callable = None) -> dict:
        """
        تفريغ الفيديو (استخراج الصوت ثم تفريغه)
        
        Args:
            video_path: مسار الفيديو
            method: طريقة التفريغ ('whisper', 'openai', 'google')
            language: لغة الصوت
            progress_callback: دالة تحديث التقدم
            
        Returns:
            dict: النص المفرغ مع التوقيتات
        """
        try:
            self.logger.info(f"بدء تفريغ الفيديو: {video_path}")
            
            # استخراج الصوت
            if progress_callback:
                progress_callback(10)
            
            audio_path = self.extract_audio_from_video(video_path)
            if not audio_path:
                raise Exception("فشل في استخراج الصوت من الفيديو")
            
            if progress_callback:
                progress_callback(30)
            
            # تفريغ الصوت حسب الطريقة المحددة
            transcription = {}
            
            if method == 'whisper':
                transcription = self.transcribe_audio_whisper(
                    audio_path, language, 
                    lambda p: progress_callback(30 + int(p * 0.7)) if progress_callback else None
                )
            elif method == 'openai':
                transcription = self.transcribe_audio_openai(
                    audio_path, language,
                    lambda p: progress_callback(30 + int(p * 0.7)) if progress_callback else None
                )
            elif method == 'google':
                transcription = self.transcribe_audio_google(
                    audio_path, language or 'ar-SA',
                    lambda p: progress_callback(30 + int(p * 0.7)) if progress_callback else None
                )
            
            # تنظيف الملف المؤقت
            try:
                os.unlink(audio_path)
            except:
                pass
            
            if progress_callback:
                progress_callback(100)
            
            return transcription
            
        except Exception as e:
            self.logger.error(f"خطأ في تفريغ الفيديو: {str(e)}")
            return {}
    
    def save_transcription(self, transcription: dict, output_path: str, 
                          format_type: str = 'txt') -> bool:
        """
        حفظ التفريغ في ملف
        
        Args:
            transcription: النص المفرغ
            output_path: مسار الحفظ
            format_type: نوع الملف ('txt', 'srt', 'vtt')
            
        Returns:
            bool: True إذا تم الحفظ بنجاح
        """
        try:
            output_path = Path(output_path)
            
            if format_type == 'txt':
                self._save_as_txt(transcription, output_path)
            elif format_type == 'srt':
                self._save_as_srt(transcription, output_path)
            elif format_type == 'vtt':
                self._save_as_vtt(transcription, output_path)
            else:
                raise Exception(f"نوع الملف غير مدعوم: {format_type}")
            
            self.logger.info(f"تم حفظ التفريغ في: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ التفريغ: {str(e)}")
            return False
    
    def _save_as_txt(self, transcription: dict, output_path: Path):
        """حفظ كملف نصي"""
        with open(output_path.with_suffix('.txt'), 'w', encoding='utf-8') as f:
            f.write(transcription.get('text', ''))
    
    def _save_as_srt(self, transcription: dict, output_path: Path):
        """حفظ كملف ترجمة SRT"""
        with open(output_path.with_suffix('.srt'), 'w', encoding='utf-8') as f:
            segments = transcription.get('segments', [])
            for i, segment in enumerate(segments, 1):
                start_time = self._seconds_to_srt_time(segment['start'])
                end_time = self._seconds_to_srt_time(segment['end'])
                
                f.write(f"{i}\n")
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{segment['text']}\n\n")
    
    def _save_as_vtt(self, transcription: dict, output_path: Path):
        """حفظ كملف ترجمة VTT"""
        with open(output_path.with_suffix('.vtt'), 'w', encoding='utf-8') as f:
            f.write("WEBVTT\n\n")
            
            segments = transcription.get('segments', [])
            for segment in segments:
                start_time = self._seconds_to_vtt_time(segment['start'])
                end_time = self._seconds_to_vtt_time(segment['end'])
                
                f.write(f"{start_time} --> {end_time}\n")
                f.write(f"{segment['text']}\n\n")
    
    def _seconds_to_srt_time(self, seconds: float) -> str:
        """تحويل الثواني إلى تنسيق SRT"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        millisecs = int((seconds % 1) * 1000)
        
        return f"{hours:02d}:{minutes:02d}:{secs:02d},{millisecs:03d}"
    
    def _seconds_to_vtt_time(self, seconds: float) -> str:
        """تحويل الثواني إلى تنسيق VTT"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = seconds % 60
        
        return f"{hours:02d}:{minutes:02d}:{secs:06.3f}"
