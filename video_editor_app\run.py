#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل سريع للتطبيق
Quick Run Script for Video Editor Application
"""

import sys
import os
import subprocess
from pathlib import Path

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 8):
        print("❌ يتطلب Python 3.8 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    return True

def check_requirements():
    """التحقق من المتطلبات وتثبيتها إذا لزم الأمر"""
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    if not requirements_file.exists():
        print("❌ ملف requirements.txt غير موجود")
        return False
    
    print("🔍 التحقق من المتطلبات...")
    
    # قراءة المتطلبات
    with open(requirements_file, 'r', encoding='utf-8') as f:
        requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    
    # التحقق من كل متطلب
    missing_packages = []
    optional_packages = ['qdarkstyle', 'face-recognition', 'dlib', 'mediapipe']

    for requirement in requirements:
        package_name = requirement.split('==')[0].split('>=')[0].split('<=')[0]
        try:
            # محاولة استيراد الحزمة
            if package_name == 'PyQt6':
                from PyQt6.QtWidgets import QApplication
            elif package_name == 'moviepy':
                import moviepy
            elif package_name == 'opencv-python':
                import cv2
            elif package_name == 'requests':
                import requests
            elif package_name == 'sqlalchemy':
                import sqlalchemy
            elif package_name in optional_packages:
                # الحزم الاختيارية - لا نوقف التطبيق إذا لم تكن موجودة
                try:
                    __import__(package_name.replace('-', '_'))
                except ImportError:
                    print(f"⚠️ الحزمة الاختيارية {package_name} غير مثبتة")
                    continue
            else:
                __import__(package_name.replace('-', '_'))
        except ImportError:
            if package_name not in optional_packages:
                missing_packages.append(requirement)
    
    if missing_packages:
        print(f"📦 يجب تثبيت {len(missing_packages)} حزمة...")
        
        response = input("هل تريد تثبيت المتطلبات تلقائياً؟ (y/n): ")
        if response.lower() in ['y', 'yes', 'نعم']:
            try:
                subprocess.check_call([
                    sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
                ])
                print("✅ تم تثبيت جميع المتطلبات")
                return True
            except subprocess.CalledProcessError:
                print("❌ فشل في تثبيت المتطلبات")
                print("يرجى تشغيل الأمر التالي يدوياً:")
                print(f"pip install -r {requirements_file}")
                return False
        else:
            print("⚠️ يجب تثبيت المتطلبات أولاً:")
            print(f"pip install -r {requirements_file}")
            return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def setup_environment():
    """إعداد البيئة"""
    print("🔧 إعداد البيئة...")
    
    # إنشاء المجلدات المطلوبة
    project_root = Path(__file__).parent
    
    directories = [
        "temp_files",
        "output_videos", 
        "logs",
        "assets"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(exist_ok=True)
    
    print("✅ تم إعداد البيئة")

def run_application():
    """تشغيل التطبيق"""
    print("🚀 تشغيل التطبيق...")
    
    main_script = Path(__file__).parent / "main.py"
    
    if not main_script.exists():
        print("❌ ملف main.py غير موجود")
        return False
    
    try:
        # تشغيل التطبيق
        subprocess.run([sys.executable, str(main_script)], check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🎬 معالج الفيديوهات المتكامل - Video Editor Pro")
    print("=" * 60)
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # التحقق من المتطلبات
    if not check_requirements():
        input("اضغط Enter للخروج...")
        sys.exit(1)
    
    # إعداد البيئة
    setup_environment()
    
    # تشغيل التطبيق
    if run_application():
        print("✅ تم إغلاق التطبيق بنجاح")
    else:
        print("❌ حدث خطأ في التطبيق")
        input("اضغط Enter للخروج...")
        sys.exit(1)

if __name__ == "__main__":
    main()
