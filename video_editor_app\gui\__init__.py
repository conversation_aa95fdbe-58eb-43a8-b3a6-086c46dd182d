# -*- coding: utf-8 -*-
"""
وحدة واجهة المستخدم الرسومية
GUI Module
"""

try:
    from .main_window import MainWindow
    print("✅ تم استيراد MainWindow")
except ImportError as e:
    print(f"⚠️ خطأ في استيراد MainWindow: {e}")
    
    # إنشاء MainWindow وهمي
    from PyQt6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QLabel
    
    class MainWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("معالج الفيديوهات - وضع الطوارئ")
            self.setGeometry(100, 100, 800, 600)
            
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            label = QLabel("التطبيق يعمل في وضع الطوارئ")
            layout.addWidget(label)

__all__ = ['MainWindow']
