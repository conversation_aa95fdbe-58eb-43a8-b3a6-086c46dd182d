# -*- coding: utf-8 -*-
"""
وحدة الواجهة الرسومية
GUI Module for Video Editor Application
"""

try:
    from .main_window import MainWindow
    __all__ = ['MainWindow']
except ImportError as e:
    print(f"⚠️ خطأ في استيراد واجهة المستخدم: {e}")

    # إنشاء فئة وهمية
    class MainWindow:
        def __init__(self):
            raise ImportError("واجهة المستخدم غير متاحة")
        def show(self):
            pass

    __all__ = ['MainWindow']
