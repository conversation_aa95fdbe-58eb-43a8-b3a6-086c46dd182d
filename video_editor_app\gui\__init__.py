# -*- coding: utf-8 -*-
"""
وحدة واجهة المستخدم الرسومية
GUI Module
"""

try:
    from .main_window import MainWindow
    __all__ = ['MainWindow']
    print("✅ تم استيراد MainWindow بنجاح")
except ImportError as e:
    print(f"⚠️ خطأ في استيراد MainWindow: {e}")
    
    # إنشاء MainWindow وهمي آمن
    try:
        from PyQt6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
        from PyQt6.QtCore import Qt
        
        class MainWindow(QMainWindow):
            def __init__(self):
                super().__init__()
                self.setWindowTitle("معالج الفيديوهات المتكامل - وضع الطوارئ")
                self.setGeometry(100, 100, 800, 600)
                
                central_widget = QWidget()
                self.setCentralWidget(central_widget)
                
                layout = QVBoxLayout(central_widget)
                
                label = QLabel("التطبيق يعمل في وضع الطوارئ")
                label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                layout.addWidget(label)
                
                button = QPushButton("اختبار الاستجابة")
                button.clicked.connect(lambda: print("✅ التطبيق يستجيب!"))
                layout.addWidget(button)
        
        __all__ = ['MainWindow']
        print("✅ تم إنشاء MainWindow وهمي")
        
    except ImportError:
        print("❌ فشل في إنشاء MainWindow وهمي")
        
        # MainWindow أساسي جداً
        class MainWindow:
            def __init__(self):
                print("⚠️ MainWindow أساسي - PyQt6 غير متاح")
            def show(self):
                print("⚠️ لا يمكن عرض النافذة - PyQt6 غير متاح")
        
        __all__ = ['MainWindow']
