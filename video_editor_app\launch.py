#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل ذكي للتطبيق - Smart Application Launcher
يختار أفضل نسخة للتشغيل
"""

import sys
import subprocess
import time
from pathlib import Path

def try_launch_version(script_name, description, timeout=15):
    """محاولة تشغيل نسخة معينة"""
    print(f"🚀 محاولة تشغيل {description}...")
    
    try:
        # تشغيل مع timeout
        process = subprocess.Popen([sys.executable, script_name])
        
        # انتظار قصير للتحقق من التشغيل
        time.sleep(3)
        
        if process.poll() is None:
            # العملية ما زالت تعمل
            print(f"✅ {description} يعمل بنجاح!")
            process.wait()  # انتظار انتهاء العملية
            return True
        else:
            print(f"❌ {description} توقف مبكراً")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في {description}: {e}")
        return False

def main():
    """الدالة الرئيسية للمشغل الذكي"""
    print("🎬 مشغل ذكي لمعالج الفيديوهات المتكامل")
    print("="*50)
    
    # قائمة النسخ مرتبة حسب الأولوية
    versions = [
        ("main_safe.py", "النسخة الآمنة"),
        ("main_fixed.py", "النسخة المحسنة"),
        ("main_simple.py", "النسخة البسيطة"),
        ("main.py", "النسخة الأصلية"),
    ]
    
    for script, description in versions:
        script_path = Path(script)
        if script_path.exists():
            if try_launch_version(script, description):
                return True
        else:
            print(f"⚠️ {script} غير موجود")
    
    print("❌ فشل في تشغيل جميع النسخ")
    print("💡 جرب:")
    print("  1. pip install --upgrade PyQt6")
    print("  2. python debug_freeze.py")
    print("  3. python fix_freeze_final.py")
    
    return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
