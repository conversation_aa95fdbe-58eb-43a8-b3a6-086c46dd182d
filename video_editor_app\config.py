# -*- coding: utf-8 -*-
"""
ملف إعدادات التطبيق
Configuration File for Video Editor Application
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, List, Optional, Union

class Config:
    """فئة إدارة إعدادات التطبيق"""
    
    def __init__(self, config_file: str = None):
        """
        تهيئة الإعدادات
        
        Args:
            config_file: مسار ملف الإعدادات
        """
        if config_file is None:
            config_file = Path(__file__).parent / "config.json"
        
        self.config_file = Path(config_file)
        self.settings = self._load_default_settings()
        
        # تحميل الإعدادات المحفوظة
        if self.config_file.exists():
            self.load_settings()
    
    def _load_default_settings(self) -> dict[str, Any]:
        """تحميل الإعدادات الافتراضية"""
        return {
            # إعدادات عامة
            "app": {
                "language": "ar",
                "theme": "dark",
                "auto_save": True,
                "check_updates": True
            },
            
            # مسارات الملفات
            "paths": {
                "output_directory": str(Path.home() / "VideoEditor" / "Output"),
                "temp_directory": str(Path.home() / "VideoEditor" / "Temp"),
                "projects_directory": str(Path.home() / "VideoEditor" / "Projects")
            },
            
            # إعدادات الفيديو
            "video": {
                "default_quality": "1080p",
                "default_format": "mp4",
                "default_fps": 30,
                "segment_duration": 30,
                "cut_type": "equal"
            },
            
            # إعدادات الصوت
            "audio": {
                "default_format": "wav",
                "sample_rate": 44100,
                "channels": 2,
                "bitrate": 192
            },
            
            # إعدادات الترجمة
            "translation": {
                "default_service": "google",
                "source_language": "auto",
                "target_language": "ar",
                "batch_size": 10
            },
            
            # إعدادات TTS
            "tts": {
                "default_service": "gtts",
                "default_voice": "ar",
                "speech_speed": 100,
                "voice_volume": 80
            },
            
            # إعدادات تتبع الوجوه
            "face_tracking": {
                "enabled": False,
                "method": "mediapipe",
                "tracking_type": "main_face",
                "sensitivity": 5
            },
            
            # إعدادات المونتاج
            "montage": {
                "add_intro": False,
                "add_outro": False,
                "add_transitions": True,
                "add_music": False,
                "filter_type": "none"
            },
            
            # إعدادات الأداء
            "performance": {
                "cpu_cores": 4,
                "use_gpu": False,
                "cache_size": 1024,
                "max_memory": 4096
            },
            
            # مفاتيح API
            "api_keys": {
                "openai": "",
                "elevenlabs": "",
                "deepl": "",
                "google_cloud": ""
            },
            
            # إعدادات قاعدة البيانات
            "database": {
                "type": "sqlite",
                "path": str(Path(__file__).parent / "video_editor.db"),
                "backup_enabled": True,
                "backup_interval": 24  # ساعات
            },
            
            # إعدادات السجلات
            "logging": {
                "level": "INFO",
                "max_file_size": 10,  # MB
                "backup_count": 5,
                "console_output": True
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        الحصول على قيمة إعداد
        
        Args:
            key: مفتاح الإعداد (يمكن استخدام النقاط للوصول للمستويات العميقة)
            default: القيمة الافتراضية
            
        Returns:
            Any: قيمة الإعداد
        """
        keys = key.split('.')
        value = self.settings
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any):
        """
        تعيين قيمة إعداد
        
        Args:
            key: مفتاح الإعداد
            value: القيمة الجديدة
        """
        keys = key.split('.')
        current = self.settings
        
        # الوصول إلى المستوى الأخير
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # تعيين القيمة
        current[keys[-1]] = value
    
    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                saved_settings = json.load(f)
            
            # دمج الإعدادات المحفوظة مع الافتراضية
            self._merge_settings(self.settings, saved_settings)
            
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {str(e)}")
    
    def save_settings(self):
        """حفظ الإعدادات في الملف"""
        try:
            # إنشاء المجلد إذا لم يكن موجوداً
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {str(e)}")
            return False
    
    def _merge_settings(self, default: dict, saved: dict):
        """دمج الإعدادات المحفوظة مع الافتراضية"""
        for key, value in saved.items():
            if key in default:
                if isinstance(value, dict) and isinstance(default[key], dict):
                    self._merge_settings(default[key], value)
                else:
                    default[key] = value
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات إلى القيم الافتراضية"""
        self.settings = self._load_default_settings()
    
    def export_settings(self, export_path: str) -> bool:
        """
        تصدير الإعدادات إلى ملف
        
        Args:
            export_path: مسار التصدير
            
        Returns:
            bool: True إذا تم التصدير بنجاح
        """
        try:
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(self.settings, f, indent=2, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"خطأ في تصدير الإعدادات: {str(e)}")
            return False
    
    def import_settings(self, import_path: str) -> bool:
        """
        استيراد الإعدادات من ملف
        
        Args:
            import_path: مسار الاستيراد
            
        Returns:
            bool: True إذا تم الاستيراد بنجاح
        """
        try:
            with open(import_path, 'r', encoding='utf-8') as f:
                imported_settings = json.load(f)
            
            # التحقق من صحة الإعدادات المستوردة
            if self._validate_settings(imported_settings):
                self.settings = imported_settings
                return True
            else:
                print("الإعدادات المستوردة غير صحيحة")
                return False
                
        except Exception as e:
            print(f"خطأ في استيراد الإعدادات: {str(e)}")
            return False
    
    def _validate_settings(self, settings: dict) -> bool:
        """التحقق من صحة الإعدادات"""
        required_sections = ['app', 'paths', 'video', 'audio']
        
        for section in required_sections:
            if section not in settings:
                return False
        
        return True
    
    def create_directories(self):
        """إنشاء المجلدات المطلوبة"""
        directories = [
            self.get('paths.output_directory'),
            self.get('paths.temp_directory'),
            self.get('paths.projects_directory')
        ]
        
        for directory in directories:
            if directory:
                Path(directory).mkdir(parents=True, exist_ok=True)
    
    def get_api_key(self, service: str) -> str:
        """
        الحصول على مفتاح API لخدمة معينة
        
        Args:
            service: اسم الخدمة
            
        Returns:
            str: مفتاح API
        """
        return self.get(f'api_keys.{service}', '')
    
    def set_api_key(self, service: str, api_key: str):
        """
        تعيين مفتاح API لخدمة معينة
        
        Args:
            service: اسم الخدمة
            api_key: مفتاح API
        """
        self.set(f'api_keys.{service}', api_key)

# إنشاء مثيل عام للإعدادات
config = Config()
