# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للتطبيق
Main Window for Video Editor Application
"""

import sys
import os
from pathlib import Path
from typing import dict, list, Optional, Any, Union

# استيراد PyQt6 مع معالجة الأخطاء
try:
    from PyQt6.QtWidgets import (
        QAbstractItemView,
        QAction,
        QActionGroup,
        QApplication,
        QButtonGroup,
        QCalendarWidget,
        QCheckBox,
        QComboBox,
        QCommandLinkButton,
        QDateEdit,
        QDateTimeEdit,
        QDial,
        QDialog,
        QDialogButtonBox,
        QDoubleSpinBox,
        QFileDialog,
        QFontComboBox,
        QFormLayout,
        QFrame,
        QGraphicsItem,
        QGraphicsPixmapItem,
        QGraphicsScene,
        QGraphicsTextItem,
        QGraphicsView,
        QGridLayout,
        QGroupBox,
        QHBoxLayout,
        QHeaderView,
        QInputDialog,
        QKeySequenceEdit,
        QLCDNumber,
        QLabel,
        QLineEdit,
        QListWidget,
        QListWidgetItem,
        QMainWindow,
        QMenu,
        QMenuBar,
        QMessageBox,
        QPlainTextEdit,
        QProgressBar,
        QPushButton,
        QRadioButton,
        QRubberBand,
        QScrollArea,
        QScrollBar,
        QSeparator,
        QSizeGrip,
        QSizePolicy,
        QSlider,
        QSpinBox,
        QSplitter,
        QStackedWidget,
        QStatusBar,
        QTabWidget,
        QTableWidget,
        QTableWidgetItem,
        QTextBrowser,
        QTextEdit,
        QTimeEdit,
        QToolBar,
        QToolButton,
        QTreeWidget,
        QTreeWidgetItem,
        QVBoxLayout,
        QWidget
    )
    from PyQt6.QtCore import (
        QByteArray,
        QDir,
        QEvent,
        QEventLoop,
        QFile,
        QIODevice,
        QLocale,
        QMutex,
        QObject,
        QPoint,
        QRect,
        QRectF,
        QSettings,
        QSize,
        QStandardPaths,
        QThread,
        QTimer,
        QTranslator,
        QUrl,
        QVariant,
        QWaitCondition,
        Qt,
        pyqtSignal
    )
    from PyQt6.QtGui import (
        QAction,
        QBrush,
        QColor,
        QCursor,
        QDoubleValidator,
        QFont,
        QFontMetrics,
        QIcon,
        QImage,
        QIntValidator,
        QKeySequence,
        QPainter,
        QPalette,
        QPen,
        QPixmap,
        QRegularExpressionValidator,
        QShortcut,
        QTextCursor,
        QTextDocument,
        QValidator
    )
    PYQT6_AVAILABLE = True
except ImportError as e:
    print(f"❌ خطأ في استيراد PyQt6: {e}")
    PYQT6_AVAILABLE = False
    raise

# استيراد الوحدات الأخرى مع معالجة الأخطاء
try:
    from database.db_manager import DatabaseManager
except ImportError:
    print("⚠️ خطأ في استيراد قاعدة البيانات")
    DatabaseManager = None

try:
    from utils.logger import VideoEditorLogger
except ImportError:
    print("⚠️ خطأ في استيراد نظام السجلات")
    class VideoEditorLogger:
        def __init__(self, name):
            self.name = name
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")

try:
    from src.video_editor_core import VideoEditorCore, VideoProcessingThread
except ImportError:
    print("⚠️ خطأ في استيراد النواة الرئيسية")
    class VideoEditorCore:
        def __init__(self):
            self.progress_updated = lambda x: None
            self.status_updated = lambda x: None
            self.operation_completed = lambda x, y: None
            self.error_occurred = lambda x, y: None
        def get_video_info(self, path): return {}
        def get_supported_platforms(self): return []
        def get_available_voices(self, service): return []
        def download_video(self, *args): return {'success': False}
        def process_video_complete(self, *args): return {'success': False}

    class VideoProcessingThread:
        def __init__(self, *args):
            pass
        def start(self): pass

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        """تهيئة آمنة للنافذة الرئيسية"""
        super().__init__()

        print("🔧 بدء تهيئة النافذة الرئيسية...")

        # متغيرات الحالة الأساسية
        self.current_video_path = None
        self.processing_thread = None
        self.db_manager = None
        self.logger = None
        self.core = None

        try:
            # تهيئة آمنة لقاعدة البيانات
            print("💾 تهيئة قاعدة البيانات...")
            if DatabaseManager:
                self.db_manager = DatabaseManager()
                print("✅ تم تهيئة قاعدة البيانات")
            else:
                print("⚠️ قاعدة البيانات غير متاحة")
        except Exception as e:
            print(f"⚠️ خطأ في قاعدة البيانات: {e}")
            self.db_manager = None

        try:
            # تهيئة آمنة للسجلات
            print("📝 تهيئة نظام السجلات...")
            if VideoEditorLogger:
                self.logger = VideoEditorLogger(__name__)
                print("✅ تم تهيئة نظام السجلات")
            else:
                print("⚠️ نظام السجلات غير متاح")
        except Exception as e:
            print(f"⚠️ خطأ في نظام السجلات: {e}")
            self.logger = None

        try:
            # تهيئة آمنة للنواة
            print("🧠 تهيئة النواة الرئيسية...")
            if VideoEditorCore:
                self.core = VideoEditorCore()
                print("✅ تم تهيئة النواة")

                # ربط الإشارات بشكل آمن
                try:
                    self.setup_core_connections()
                    print("✅ تم ربط إشارات النواة")
                except Exception as e:
                    print(f"⚠️ خطأ في ربط الإشارات: {e}")
            else:
                print("⚠️ النواة غير متاحة")
        except Exception as e:
            print(f"⚠️ خطأ في النواة: {e}")
            self.core = None

        # إعداد الواجهة
        try:
            print("🖼️ إعداد الواجهة...")
            self.init_ui()
            print("✅ تم إعداد الواجهة")
        except Exception as e:
            print(f"❌ خطأ في إعداد الواجهة: {e}")
            self.setup_emergency_ui()

        # ربط الاتصالات
        try:
            print("🔗 ربط الاتصالات...")
            self.setup_connections()
            print("✅ تم ربط الاتصالات")
        except Exception as e:
            print(f"⚠️ خطأ في ربط الاتصالات: {e}")

        print("🎉 تم إنجاز تهيئة النافذة الرئيسية")

    def setup_emergency_ui(self):
        """إعداد واجهة الطوارئ"""
        print("🚨 تشغيل واجهة الطوارئ...")

        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)

        # رسالة الخطأ
        error_label = QLabel("⚠️ حدث خطأ في تهيئة الواجهة الرئيسية")
        error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        error_label.setStyleSheet("""
            QLabel {
                color: red;
                font-size: 18px;
                font-weight: bold;
                padding: 20px;
            }
        """)
        layout.addWidget(error_label)

        # رسالة الحالة
        status_label = QLabel("التطبيق يعمل في وضع الطوارئ")
        status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(status_label)

        # زر إعادة المحاولة
        retry_btn = QPushButton("🔄 إعادة المحاولة")
        retry_btn.clicked.connect(self.retry_initialization)
        layout.addWidget(retry_btn)

        # زر الإغلاق
        close_btn = QPushButton("❌ إغلاق التطبيق")
        close_btn.clicked.connect(self.close)
        layout.addWidget(close_btn)

    def retry_initialization(self):
        """إعادة محاولة التهيئة"""
        try:
            self.init_ui()
            QMessageBox.information(self, "نجح", "تم إعادة تهيئة الواجهة بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في إعادة التهيئة: {e}")

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("معالج الفيديوهات المتكامل - Video Editor Pro")
        self.setGeometry(100, 100, 1400, 900)
        
        # إعداد الخط العربي
        font = QFont("Arial", 10)
        self.setFont(font)
        
        # إنشاء الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # شريط العنوان
        self.create_header(main_layout)
        
        # المحتوى الرئيسي
        self.create_main_content(main_layout)
        
        # شريط الحالة
        self.create_status_bar(main_layout)
        
    def create_header(self, parent_layout):
        """إنشاء شريط العنوان"""
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        header_frame.setMaximumHeight(80)
        
        header_layout = QHBoxLayout(header_frame)
        
        # عنوان التطبيق
        title_label = QLabel("🎬 معالج الفيديوهات المتكامل")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2196F3;
                padding: 10px;
            }
        """)
        
        # أزرار سريعة
        quick_buttons_layout = QHBoxLayout()
        
        self.btn_new_project = QPushButton("🆕 مشروع جديد")
        self.btn_open_file = QPushButton("📁 فتح ملف")
        self.btn_download_video = QPushButton("⬇️ تحميل فيديو")
        self.btn_settings = QPushButton("⚙️ الإعدادات")
        
        for btn in [self.btn_new_project, self.btn_open_file, 
                   self.btn_download_video, self.btn_settings]:
            btn.setMinimumHeight(40)
            btn.setStyleSheet("""
                QPushButton {
                    background-color: #4CAF50;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 5px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #45a049;
                }
                QPushButton:pressed {
                    background-color: #3d8b40;
                }
            """)
            quick_buttons_layout.addWidget(btn)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        header_layout.addLayout(quick_buttons_layout)
        
        parent_layout.addWidget(header_frame)
    
    def create_main_content(self, parent_layout):
        """إنشاء المحتوى الرئيسي"""
        # إنشاء التبويبات
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.TabPosition.North)
        
        # تبويب معالجة الفيديوهات
        self.create_video_processing_tab()
        
        # تبويب الذكاء اللغوي
        self.create_language_ai_tab()
        
        # تبويب المشاريع
        self.create_projects_tab()
        
        # تبويب الإعدادات
        self.create_settings_tab()
        
        parent_layout.addWidget(self.tab_widget)
    
    def create_video_processing_tab(self):
        """إنشاء تبويب معالجة الفيديوهات"""
        video_tab = QWidget()
        layout = QHBoxLayout(video_tab)
        
        # الجانب الأيسر - قائمة الفيديوهات
        left_panel = self.create_video_list_panel()
        
        # الجانب الأيمن - أدوات المعالجة
        right_panel = self.create_processing_tools_panel()
        
        # تقسيم النافذة
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([400, 800])
        
        layout.addWidget(splitter)
        
        self.tab_widget.addTab(video_tab, "🎬 معالجة الفيديوهات")
    
    def create_video_list_panel(self):
        """إنشاء لوحة قائمة الفيديوهات"""
        panel = QGroupBox("قائمة الفيديوهات")
        layout = QVBoxLayout(panel)
        
        # قائمة الفيديوهات
        self.video_list = QListWidget()
        self.video_list.setMinimumWidth(350)
        
        # أزرار إدارة الفيديوهات
        buttons_layout = QHBoxLayout()
        
        self.btn_add_video = QPushButton("➕ إضافة")
        self.btn_remove_video = QPushButton("🗑️ حذف")
        self.btn_refresh_list = QPushButton("🔄 تحديث")
        
        for btn in [self.btn_add_video, self.btn_remove_video, self.btn_refresh_list]:
            btn.setMinimumHeight(35)
            buttons_layout.addWidget(btn)
        
        layout.addWidget(self.video_list)
        layout.addLayout(buttons_layout)
        
        return panel
    
    def create_processing_tools_panel(self):
        """إنشاء لوحة أدوات المعالجة"""
        panel = QGroupBox("أدوات المعالجة")
        layout = QVBoxLayout(panel)
        
        # أدوات القص
        cutting_group = self.create_cutting_tools()
        
        # أدوات المونتاج
        montage_group = self.create_montage_tools()
        
        # أدوات تتبع الوجوه
        face_tracking_group = self.create_face_tracking_tools()
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        # زر المعالجة
        self.btn_process = QPushButton("🚀 بدء المعالجة")
        self.btn_process.setMinimumHeight(50)
        self.btn_process.setStyleSheet("""
            QPushButton {
                background-color: #FF5722;
                color: white;
                border: none;
                padding: 12px;
                border-radius: 8px;
                font-size: 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E64A19;
            }
        """)
        
        layout.addWidget(cutting_group)
        layout.addWidget(montage_group)
        layout.addWidget(face_tracking_group)
        layout.addStretch()
        layout.addWidget(self.progress_bar)
        layout.addWidget(self.btn_process)
        
        return panel
    
    def create_cutting_tools(self):
        """إنشاء أدوات القص"""
        group = QGroupBox("⚡ قص الفيديوهات")
        layout = QGridLayout(group)
        
        # مدة المقطع
        layout.addWidget(QLabel("مدة المقطع (ثانية):"), 0, 0)
        self.spin_segment_duration = QSpinBox()
        self.spin_segment_duration.setRange(5, 3600)
        self.spin_segment_duration.setValue(30)
        layout.addWidget(self.spin_segment_duration, 0, 1)
        
        # نوع القص
        layout.addWidget(QLabel("نوع القص:"), 1, 0)
        self.combo_cut_type = QComboBox()
        self.combo_cut_type.addItems([
            "تلقائي (ذكي)",
            "مقاطع متساوية",
            "حسب المشاهد",
            "حسب الصوت"
        ])
        layout.addWidget(self.combo_cut_type, 1, 1)
        
        # جودة الإخراج
        layout.addWidget(QLabel("جودة الإخراج:"), 2, 0)
        self.combo_output_quality = QComboBox()
        self.combo_output_quality.addItems([
            "عالية جداً (4K)",
            "عالية (1080p)",
            "متوسطة (720p)",
            "منخفضة (480p)"
        ])
        self.combo_output_quality.setCurrentText("عالية (1080p)")
        layout.addWidget(self.combo_output_quality, 2, 1)
        
        return group

    def create_montage_tools(self):
        """إنشاء أدوات المونتاج"""
        group = QGroupBox("🎨 المونتاج التلقائي")
        layout = QGridLayout(group)

        # إضافة مقدمة
        self.check_add_intro = QCheckBox("إضافة مقدمة")
        layout.addWidget(self.check_add_intro, 0, 0)

        # إضافة خاتمة
        self.check_add_outro = QCheckBox("إضافة خاتمة")
        layout.addWidget(self.check_add_outro, 0, 1)

        # إضافة انتقالات
        self.check_add_transitions = QCheckBox("إضافة انتقالات")
        self.check_add_transitions.setChecked(True)
        layout.addWidget(self.check_add_transitions, 1, 0)

        # إضافة موسيقى
        self.check_add_music = QCheckBox("إضافة موسيقى خلفية")
        layout.addWidget(self.check_add_music, 1, 1)

        # نوع الفلتر
        layout.addWidget(QLabel("الفلاتر:"), 2, 0)
        self.combo_filters = QComboBox()
        self.combo_filters.addItems([
            "بدون فلاتر",
            "تحسين الألوان",
            "أبيض وأسود",
            "سينمائي",
            "مشرق ونابض"
        ])
        layout.addWidget(self.combo_filters, 2, 1)

        return group

    def create_face_tracking_tools(self):
        """إنشاء أدوات تتبع الوجوه"""
        group = QGroupBox("👤 تتبع الوجوه")
        layout = QGridLayout(group)

        # تفعيل تتبع الوجوه
        self.check_face_tracking = QCheckBox("تفعيل تتبع الوجوه")
        layout.addWidget(self.check_face_tracking, 0, 0, 1, 2)

        # نوع التتبع
        layout.addWidget(QLabel("نوع التتبع:"), 1, 0)
        self.combo_tracking_type = QComboBox()
        self.combo_tracking_type.addItems([
            "تتبع الوجه الرئيسي",
            "تتبع جميع الوجوه",
            "تتبع أكبر وجه"
        ])
        layout.addWidget(self.combo_tracking_type, 1, 1)

        # حساسية التتبع
        layout.addWidget(QLabel("حساسية التتبع:"), 2, 0)
        self.slider_tracking_sensitivity = QSlider(Qt.Orientation.Horizontal)
        self.slider_tracking_sensitivity.setRange(1, 10)
        self.slider_tracking_sensitivity.setValue(5)
        layout.addWidget(self.slider_tracking_sensitivity, 2, 1)

        return group

    def create_language_ai_tab(self):
        """إنشاء تبويب الذكاء اللغوي"""
        ai_tab = QWidget()
        layout = QHBoxLayout(ai_tab)

        # الجانب الأيسر - إعدادات الترجمة
        left_panel = self.create_translation_panel()

        # الجانب الأيمن - إعدادات الدبلجة
        right_panel = self.create_dubbing_panel()

        # تقسيم النافذة
        splitter = QSplitter(Qt.Orientation.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([600, 600])

        layout.addWidget(splitter)

        self.tab_widget.addTab(ai_tab, "🌐 الذكاء اللغوي")

    def create_translation_panel(self):
        """إنشاء لوحة الترجمة"""
        panel = QGroupBox("🔤 الترجمة التلقائية")
        layout = QVBoxLayout(panel)

        # إعدادات الترجمة
        settings_layout = QGridLayout()

        # اللغة المصدر
        settings_layout.addWidget(QLabel("اللغة المصدر:"), 0, 0)
        self.combo_source_lang = QComboBox()
        self.combo_source_lang.addItems([
            "تلقائي", "العربية", "الإنجليزية", "الفرنسية",
            "الألمانية", "الإسبانية", "الإيطالية", "الروسية"
        ])
        settings_layout.addWidget(self.combo_source_lang, 0, 1)

        # اللغة الهدف
        settings_layout.addWidget(QLabel("اللغة الهدف:"), 1, 0)
        self.combo_target_lang = QComboBox()
        self.combo_target_lang.addItems([
            "العربية", "الإنجليزية", "الفرنسية", "الألمانية",
            "الإسبانية", "الإيطالية", "الروسية", "اليابانية"
        ])
        settings_layout.addWidget(self.combo_target_lang, 1, 1)

        # خدمة الترجمة
        settings_layout.addWidget(QLabel("خدمة الترجمة:"), 2, 0)
        self.combo_translation_service = QComboBox()
        self.combo_translation_service.addItems([
            "Google Translate",
            "DeepL",
            "Microsoft Translator"
        ])
        settings_layout.addWidget(self.combo_translation_service, 2, 1)

        # منطقة النص المترجم
        self.text_translated = QTextEdit()
        self.text_translated.setPlaceholderText("النص المترجم سيظهر هنا...")
        self.text_translated.setMaximumHeight(200)

        # أزرار الترجمة
        buttons_layout = QHBoxLayout()
        self.btn_extract_text = QPushButton("📝 استخراج النص")
        self.btn_translate = QPushButton("🔄 ترجمة")
        self.btn_save_translation = QPushButton("💾 حفظ الترجمة")

        for btn in [self.btn_extract_text, self.btn_translate, self.btn_save_translation]:
            btn.setMinimumHeight(40)
            buttons_layout.addWidget(btn)

        layout.addLayout(settings_layout)
        layout.addWidget(QLabel("النص المترجم:"))
        layout.addWidget(self.text_translated)
        layout.addLayout(buttons_layout)

        return panel

    def create_dubbing_panel(self):
        """إنشاء لوحة الدبلجة"""
        panel = QGroupBox("🎤 الدبلجة التلقائية")
        layout = QVBoxLayout(panel)

        # إعدادات الصوت
        voice_settings_layout = QGridLayout()

        # نوع الصوت
        voice_settings_layout.addWidget(QLabel("نوع الصوت:"), 0, 0)
        self.combo_voice_type = QComboBox()
        self.combo_voice_type.addItems([
            "ذكر - طبيعي",
            "أنثى - طبيعية",
            "ذكر - رسمي",
            "أنثى - رسمية"
        ])
        voice_settings_layout.addWidget(self.combo_voice_type, 0, 1)

        # سرعة الكلام
        voice_settings_layout.addWidget(QLabel("سرعة الكلام:"), 1, 0)
        self.slider_speech_speed = QSlider(Qt.Orientation.Horizontal)
        self.slider_speech_speed.setRange(50, 200)
        self.slider_speech_speed.setValue(100)
        voice_settings_layout.addWidget(self.slider_speech_speed, 1, 1)

        # مستوى الصوت
        voice_settings_layout.addWidget(QLabel("مستوى الصوت:"), 2, 0)
        self.slider_voice_volume = QSlider(Qt.Orientation.Horizontal)
        self.slider_voice_volume.setRange(0, 100)
        self.slider_voice_volume.setValue(80)
        voice_settings_layout.addWidget(self.slider_voice_volume, 2, 1)

        # خدمة TTS
        voice_settings_layout.addWidget(QLabel("خدمة التحويل:"), 3, 0)
        self.combo_tts_service = QComboBox()
        self.combo_tts_service.addItems([
            "Google TTS",
            "ElevenLabs",
            "Microsoft Speech"
        ])
        voice_settings_layout.addWidget(self.combo_tts_service, 3, 1)

        # معاينة الصوت
        preview_layout = QHBoxLayout()
        self.btn_preview_voice = QPushButton("🔊 معاينة الصوت")
        self.btn_generate_dubbing = QPushButton("🎬 إنتاج الدبلجة")

        for btn in [self.btn_preview_voice, self.btn_generate_dubbing]:
            btn.setMinimumHeight(40)
            preview_layout.addWidget(btn)

        layout.addLayout(voice_settings_layout)
        layout.addStretch()
        layout.addLayout(preview_layout)

        return panel

    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        projects_tab = QWidget()
        layout = QVBoxLayout(projects_tab)

        # شريط أدوات المشاريع
        toolbar_layout = QHBoxLayout()

        self.btn_new_project_tab = QPushButton("📁 مشروع جديد")
        self.btn_open_project = QPushButton("📂 فتح مشروع")
        self.btn_save_project = QPushButton("💾 حفظ المشروع")
        self.btn_export_project = QPushButton("📤 تصدير المشروع")

        for btn in [self.btn_new_project_tab, self.btn_open_project,
                   self.btn_save_project, self.btn_export_project]:
            btn.setMinimumHeight(40)
            toolbar_layout.addWidget(btn)

        toolbar_layout.addStretch()

        # قائمة المشاريع
        self.projects_list = QListWidget()
        self.projects_list.setMinimumHeight(300)

        # معلومات المشروع المحدد
        project_info_group = QGroupBox("معلومات المشروع")
        project_info_layout = QGridLayout(project_info_group)

        project_info_layout.addWidget(QLabel("اسم المشروع:"), 0, 0)
        self.label_project_name = QLabel("لم يتم تحديد مشروع")
        project_info_layout.addWidget(self.label_project_name, 0, 1)

        project_info_layout.addWidget(QLabel("تاريخ الإنشاء:"), 1, 0)
        self.label_project_date = QLabel("-")
        project_info_layout.addWidget(self.label_project_date, 1, 1)

        project_info_layout.addWidget(QLabel("عدد الفيديوهات:"), 2, 0)
        self.label_video_count = QLabel("0")
        project_info_layout.addWidget(self.label_video_count, 2, 1)

        project_info_layout.addWidget(QLabel("الحالة:"), 3, 0)
        self.label_project_status = QLabel("غير نشط")
        project_info_layout.addWidget(self.label_project_status, 3, 1)

        layout.addLayout(toolbar_layout)
        layout.addWidget(QLabel("المشاريع المحفوظة:"))
        layout.addWidget(self.projects_list)
        layout.addWidget(project_info_group)

        self.tab_widget.addTab(projects_tab, "📋 المشاريع")

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        settings_tab = QWidget()
        layout = QVBoxLayout(settings_tab)

        # إعدادات عامة
        general_group = QGroupBox("⚙️ الإعدادات العامة")
        general_layout = QGridLayout(general_group)

        # لغة التطبيق
        general_layout.addWidget(QLabel("لغة التطبيق:"), 0, 0)
        self.combo_app_language = QComboBox()
        self.combo_app_language.addItems(["العربية", "English"])
        general_layout.addWidget(self.combo_app_language, 0, 1)

        # مجلد الإخراج الافتراضي
        general_layout.addWidget(QLabel("مجلد الإخراج:"), 1, 0)
        self.btn_output_folder = QPushButton("اختيار المجلد...")
        general_layout.addWidget(self.btn_output_folder, 1, 1)

        # جودة الإخراج الافتراضية
        general_layout.addWidget(QLabel("جودة الإخراج الافتراضية:"), 2, 0)
        self.combo_default_quality = QComboBox()
        self.combo_default_quality.addItems([
            "عالية جداً (4K)", "عالية (1080p)",
            "متوسطة (720p)", "منخفضة (480p)"
        ])
        self.combo_default_quality.setCurrentText("عالية (1080p)")
        general_layout.addWidget(self.combo_default_quality, 2, 1)

        # إعدادات الأداء
        performance_group = QGroupBox("🚀 إعدادات الأداء")
        performance_layout = QGridLayout(performance_group)

        # عدد المعالجات
        performance_layout.addWidget(QLabel("عدد المعالجات المستخدمة:"), 0, 0)
        self.spin_cpu_cores = QSpinBox()
        self.spin_cpu_cores.setRange(1, 16)
        self.spin_cpu_cores.setValue(4)
        performance_layout.addWidget(self.spin_cpu_cores, 0, 1)

        # استخدام GPU
        self.check_use_gpu = QCheckBox("استخدام كرت الرسوميات (GPU)")
        performance_layout.addWidget(self.check_use_gpu, 1, 0, 1, 2)

        # ذاكرة التخزين المؤقت
        performance_layout.addWidget(QLabel("حجم الذاكرة المؤقتة (MB):"), 2, 0)
        self.spin_cache_size = QSpinBox()
        self.spin_cache_size.setRange(100, 8192)
        self.spin_cache_size.setValue(1024)
        performance_layout.addWidget(self.spin_cache_size, 2, 1)

        # إعدادات API
        api_group = QGroupBox("🔑 إعدادات API")
        api_layout = QGridLayout(api_group)

        # OpenAI API Key
        api_layout.addWidget(QLabel("OpenAI API Key:"), 0, 0)
        self.line_openai_key = QLineEdit()
        self.line_openai_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.line_openai_key.setPlaceholderText("أدخل مفتاح OpenAI API...")
        api_layout.addWidget(self.line_openai_key, 0, 1)

        # ElevenLabs API Key
        api_layout.addWidget(QLabel("ElevenLabs API Key:"), 1, 0)
        self.line_elevenlabs_key = QLineEdit()
        self.line_elevenlabs_key.setEchoMode(QLineEdit.EchoMode.Password)
        self.line_elevenlabs_key.setPlaceholderText("أدخل مفتاح ElevenLabs API...")
        api_layout.addWidget(self.line_elevenlabs_key, 1, 1)

        # أزرار الإعدادات
        settings_buttons_layout = QHBoxLayout()
        self.btn_save_settings = QPushButton("💾 حفظ الإعدادات")
        self.btn_reset_settings = QPushButton("🔄 إعادة تعيين")
        self.btn_export_settings = QPushButton("📤 تصدير الإعدادات")

        for btn in [self.btn_save_settings, self.btn_reset_settings, self.btn_export_settings]:
            btn.setMinimumHeight(40)
            settings_buttons_layout.addWidget(btn)

        layout.addWidget(general_group)
        layout.addWidget(performance_group)
        layout.addWidget(api_group)
        layout.addStretch()
        layout.addLayout(settings_buttons_layout)

        self.tab_widget.addTab(settings_tab, "⚙️ الإعدادات")

    def create_status_bar(self, parent_layout):
        """إنشاء شريط الحالة"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.Shape.StyledPanel)
        status_frame.setMaximumHeight(40)

        status_layout = QHBoxLayout(status_frame)

        # حالة التطبيق
        self.status_label = QLabel("جاهز للاستخدام")
        self.status_label.setStyleSheet("color: #4CAF50; font-weight: bold;")

        # شريط التقدم العام
        self.main_progress_bar = QProgressBar()
        self.main_progress_bar.setVisible(False)
        self.main_progress_bar.setMaximumWidth(200)

        # معلومات النظام
        self.system_info_label = QLabel("الذاكرة: 0% | المعالج: 0%")

        status_layout.addWidget(self.status_label)
        status_layout.addStretch()
        status_layout.addWidget(self.main_progress_bar)
        status_layout.addWidget(self.system_info_label)

        parent_layout.addWidget(status_frame)

        # تحديث معلومات النظام كل ثانية
        self.system_timer = QTimer()
        self.system_timer.timeout.connect(self.update_system_info)
        self.system_timer.start(1000)

    def setup_connections(self):
        """إعداد الاتصالات بين العناصر"""
        # أزرار الشريط العلوي
        self.btn_new_project.clicked.connect(self.new_project)
        self.btn_open_file.clicked.connect(self.open_file)
        self.btn_download_video.clicked.connect(self.download_video)
        self.btn_settings.clicked.connect(lambda: self.tab_widget.setCurrentIndex(3))

        # أزرار معالجة الفيديوهات
        self.btn_add_video.clicked.connect(self.add_video)
        self.btn_remove_video.clicked.connect(self.remove_video)
        self.btn_refresh_list.clicked.connect(self.refresh_video_list)
        self.btn_process.clicked.connect(self.start_processing)

        # أزرار الذكاء اللغوي
        self.btn_extract_text.clicked.connect(self.extract_text)
        self.btn_translate.clicked.connect(self.translate_text)
        self.btn_save_translation.clicked.connect(self.save_translation)
        self.btn_preview_voice.clicked.connect(self.preview_voice)
        self.btn_generate_dubbing.clicked.connect(self.generate_dubbing)

        # أزرار المشاريع
        self.btn_new_project_tab.clicked.connect(self.new_project)
        self.btn_open_project.clicked.connect(self.open_project)
        self.btn_save_project.clicked.connect(self.save_project)
        self.btn_export_project.clicked.connect(self.export_project)

        # أزرار الإعدادات
        self.btn_output_folder.clicked.connect(self.choose_output_folder)
        self.btn_save_settings.clicked.connect(self.save_settings)
        self.btn_reset_settings.clicked.connect(self.reset_settings)
        self.btn_export_settings.clicked.connect(self.export_settings)

        # قائمة الفيديوهات
        self.video_list.itemSelectionChanged.connect(self.on_video_selected)

        # قائمة المشاريع
        self.projects_list.itemSelectionChanged.connect(self.on_project_selected)

    def setup_core_connections(self):
        """إعداد الاتصالات مع النواة الرئيسية"""
        self.core.progress_updated.connect(self.update_progress)
        self.core.status_updated.connect(self.update_status)
        self.core.operation_completed.connect(self.on_operation_completed)
        self.core.error_occurred.connect(self.on_error_occurred)

    # ===== دوال معالجة الأحداث =====

    def new_project(self):
        """إنشاء مشروع جديد"""
        self.logger.info("إنشاء مشروع جديد")
        self.status_label.setText("تم إنشاء مشروع جديد")
        self.video_list.clear()

    def open_file(self):
        """فتح ملف فيديو"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "اختيار ملف فيديو",
            "",
            "ملفات الفيديو (*.mp4 *.avi *.mov *.mkv *.wmv);;جميع الملفات (*)"
        )

        if file_path:
            self.add_video_to_list(file_path)
            self.logger.info(f"تم فتح الملف: {file_path}")

    def download_video(self):
        """تحميل فيديو من رابط"""
        from PyQt6.QtWidgets import QInputDialog

        url, ok = QInputDialog.getText(
            self,
            "تحميل فيديو",
            "أدخل رابط الفيديو:",
            text="https://"
        )

        if ok and url.strip():
            # بدء تحميل الفيديو في خيط منفصل
            self.processing_thread = VideoProcessingThread(
                self.core,
                "download_video",
                url.strip(),
                'best'
            )
            self.processing_thread.start()

            self.btn_download_video.setEnabled(False)
            self.main_progress_bar.setVisible(True)
            self.logger.info(f"بدء تحميل فيديو من: {url}")

    def add_video(self):
        """إضافة فيديو جديد"""
        self.open_file()

    def remove_video(self):
        """حذف فيديو من القائمة"""
        current_item = self.video_list.currentItem()
        if current_item:
            self.video_list.takeItem(self.video_list.row(current_item))
            self.status_label.setText("تم حذف الفيديو من القائمة")
            self.logger.info("تم حذف فيديو من القائمة")

    def refresh_video_list(self):
        """تحديث قائمة الفيديوهات"""
        self.status_label.setText("تم تحديث قائمة الفيديوهات")
        self.logger.info("تحديث قائمة الفيديوهات")

    def start_processing(self):
        """بدء معالجة الفيديوهات"""
        if self.video_list.count() == 0:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة فيديو واحد على الأقل للمعالجة")
            return

        if not self.current_video_path:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فيديو للمعالجة")
            return

        # جمع خيارات المعالجة من الواجهة
        processing_options = {
            'cut_video': True,
            'segment_duration': self.spin_segment_duration.value(),
            'cut_type': self._get_cut_type(),
            'extract_text': True,
            'stt_method': 'whisper',
            'translate': True,
            'source_language': self._get_source_language(),
            'target_language': self._get_target_language(),
            'translation_service': self._get_translation_service(),
            'create_dubbing': True,
            'tts_service': self._get_tts_service(),
            'voice_settings': self._get_voice_settings(),
            'apply_montage': self.check_add_intro.isChecked() or self.check_add_outro.isChecked(),
            'montage_options': self._get_montage_options(),
            'track_faces': self.check_face_tracking.isChecked(),
            'face_tracking_options': self._get_face_tracking_options()
        }

        # بدء المعالجة في خيط منفصل
        self.processing_thread = VideoProcessingThread(
            self.core,
            "process_video_complete",
            self.current_video_path,
            processing_options
        )
        self.processing_thread.start()

        self.btn_process.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.logger.info("بدء المعالجة الشاملة للفيديو")

    def extract_text(self):
        """استخراج النص من الفيديو"""
        if self.video_list.count() == 0:
            QMessageBox.warning(self, "تحذير", "يرجى تحديد فيديو أولاً")
            return

        self.status_label.setText("جاري استخراج النص...")
        self.logger.info("بدء استخراج النص من الفيديو")

    def translate_text(self):
        """ترجمة النص"""
        self.status_label.setText("جاري ترجمة النص...")
        self.logger.info("بدء ترجمة النص")

    def save_translation(self):
        """حفظ الترجمة"""
        self.status_label.setText("تم حفظ الترجمة")
        self.logger.info("تم حفظ الترجمة")

    def preview_voice(self):
        """معاينة الصوت"""
        self.status_label.setText("معاينة الصوت...")
        self.logger.info("معاينة الصوت")

    def generate_dubbing(self):
        """إنتاج الدبلجة"""
        self.status_label.setText("جاري إنتاج الدبلجة...")
        self.logger.info("بدء إنتاج الدبلجة")

    def open_project(self):
        """فتح مشروع محفوظ"""
        self.status_label.setText("فتح مشروع...")
        self.logger.info("فتح مشروع محفوظ")

    def save_project(self):
        """حفظ المشروع الحالي"""
        self.status_label.setText("تم حفظ المشروع")
        self.logger.info("حفظ المشروع الحالي")

    def export_project(self):
        """تصدير المشروع"""
        self.status_label.setText("تصدير المشروع...")
        self.logger.info("تصدير المشروع")

    def choose_output_folder(self):
        """اختيار مجلد الإخراج"""
        folder = QFileDialog.getExistingDirectory(self, "اختيار مجلد الإخراج")
        if folder:
            self.btn_output_folder.setText(folder)
            self.logger.info(f"تم اختيار مجلد الإخراج: {folder}")

    def save_settings(self):
        """حفظ الإعدادات"""
        self.status_label.setText("تم حفظ الإعدادات")
        self.logger.info("حفظ الإعدادات")

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self, "تأكيد",
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.status_label.setText("تم إعادة تعيين الإعدادات")
            self.logger.info("إعادة تعيين الإعدادات")

    def export_settings(self):
        """تصدير الإعدادات"""
        self.status_label.setText("تصدير الإعدادات...")
        self.logger.info("تصدير الإعدادات")

    def on_video_selected(self):
        """عند تحديد فيديو من القائمة"""
        current_item = self.video_list.currentItem()
        if current_item:
            self.status_label.setText(f"تم تحديد: {current_item.text()}")

    def on_project_selected(self):
        """عند تحديد مشروع من القائمة"""
        current_item = self.projects_list.currentItem()
        if current_item:
            self.label_project_name.setText(current_item.text())
            self.status_label.setText(f"تم تحديد المشروع: {current_item.text()}")

    def add_video_to_list(self, file_path: str):
        """إضافة فيديو إلى القائمة"""
        file_name = Path(file_path).name
        self.video_list.addItem(f"📹 {file_name}")
        self.current_video_path = file_path
        self.status_label.setText(f"تم إضافة: {file_name}")

        # الحصول على معلومات الفيديو
        video_info = self.core.get_video_info(file_path)
        if video_info:
            duration = video_info.get('duration', 0)
            resolution = f"{video_info.get('width', 0)}x{video_info.get('height', 0)}"
            self.status_label.setText(f"تم إضافة: {file_name} - المدة: {duration:.1f}ث - الدقة: {resolution}")

    def update_system_info(self):
        """تحديث معلومات النظام"""
        try:
            import psutil
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            self.system_info_label.setText(f"الذاكرة: {memory_percent:.1f}% | المعالج: {cpu_percent:.1f}%")
        except ImportError:
            self.system_info_label.setText("معلومات النظام غير متاحة")

    def closeEvent(self, event):
        """عند إغلاق التطبيق"""
        reply = QMessageBox.question(
            self, "تأكيد الإغلاق",
            "هل أنت متأكد من إغلاق التطبيق؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.logger.info("إغلاق التطبيق")
            event.accept()
        else:
            event.ignore()

    # ===== دوال مساعدة لجمع الإعدادات =====

    def _get_cut_type(self) -> str:
        """الحصول على نوع القص المحدد"""
        cut_types = {
            "تلقائي (ذكي)": "smart",
            "مقاطع متساوية": "equal",
            "حسب المشاهد": "scene",
            "حسب الصوت": "audio"
        }
        return cut_types.get(self.combo_cut_type.currentText(), "equal")

    def _get_source_language(self) -> str:
        """الحصول على اللغة المصدر"""
        lang_map = {
            "تلقائي": "auto",
            "العربية": "ar",
            "الإنجليزية": "en",
            "الفرنسية": "fr",
            "الألمانية": "de",
            "الإسبانية": "es",
            "الإيطالية": "it",
            "الروسية": "ru"
        }
        return lang_map.get(self.combo_source_lang.currentText(), "auto")

    def _get_target_language(self) -> str:
        """الحصول على اللغة الهدف"""
        lang_map = {
            "العربية": "ar",
            "الإنجليزية": "en",
            "الفرنسية": "fr",
            "الألمانية": "de",
            "الإسبانية": "es",
            "الإيطالية": "it",
            "الروسية": "ru",
            "اليابانية": "ja"
        }
        return lang_map.get(self.combo_target_lang.currentText(), "ar")

    def _get_translation_service(self) -> str:
        """الحصول على خدمة الترجمة"""
        service_map = {
            "Google Translate": "google",
            "DeepL": "deepl",
            "Microsoft Translator": "microsoft"
        }
        return service_map.get(self.combo_translation_service.currentText(), "google")

    def _get_tts_service(self) -> str:
        """الحصول على خدمة TTS"""
        service_map = {
            "Google TTS": "gtts",
            "ElevenLabs": "elevenlabs",
            "Microsoft Speech": "microsoft"
        }
        return service_map.get(self.combo_tts_service.currentText(), "gtts")

    def _get_voice_settings(self) -> dict:
        """الحصول على إعدادات الصوت"""
        return {
            'speed': self.slider_speech_speed.value() / 100.0,
            'volume': self.slider_voice_volume.value() / 100.0,
            'voice_type': self.combo_voice_type.currentText()
        }

    def _get_montage_options(self) -> dict:
        """الحصول على خيارات المونتاج"""
        return {
            'add_intro': self.check_add_intro.isChecked(),
            'add_outro': self.check_add_outro.isChecked(),
            'add_transitions': self.check_add_transitions.isChecked(),
            'add_music': self.check_add_music.isChecked(),
            'filter_type': self.combo_filters.currentText()
        }

    def _get_face_tracking_options(self) -> dict:
        """الحصول على خيارات تتبع الوجوه"""
        tracking_types = {
            "تتبع الوجه الرئيسي": "main_face",
            "تتبع جميع الوجوه": "all_faces",
            "تتبع أكبر وجه": "largest_face"
        }

        return {
            'tracking_type': tracking_types.get(self.combo_tracking_type.currentText(), "main_face"),
            'sensitivity': self.slider_tracking_sensitivity.value(),
            'method': 'mediapipe'
        }

    # ===== دوال معالجة إشارات النواة =====

    def update_progress(self, progress: int):
        """تحديث شريط التقدم"""
        self.progress_bar.setValue(progress)
        self.main_progress_bar.setValue(progress)

    def update_status(self, status: str):
        """تحديث حالة التطبيق"""
        self.status_label.setText(status)

    def on_operation_completed(self, operation_type: str, result: str):
        """عند انتهاء العملية بنجاح"""
        self.btn_process.setEnabled(True)
        self.btn_download_video.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.main_progress_bar.setVisible(False)

        if operation_type == "تحميل الفيديو":
            self.add_video_to_list(result)
            QMessageBox.information(self, "نجح التحميل", f"تم تحميل الفيديو بنجاح:\n{Path(result).name}")
        elif operation_type == "معالجة شاملة":
            QMessageBox.information(self, "نجحت المعالجة", f"تم إنجاز المعالجة بنجاح:\n{Path(result).name}")

        self.status_label.setText(f"تم إنجاز {operation_type} بنجاح")
        self.logger.info(f"انتهت العملية: {operation_type}")

    def on_error_occurred(self, operation_type: str, error_message: str):
        """عند حدوث خطأ في العملية"""
        self.btn_process.setEnabled(True)
        self.btn_download_video.setEnabled(True)
        self.progress_bar.setVisible(False)
        self.main_progress_bar.setVisible(False)

        QMessageBox.critical(self, f"خطأ في {operation_type}", error_message)
        self.status_label.setText(f"خطأ في {operation_type}")
        self.logger.error(f"خطأ في {operation_type}: {error_message}")
