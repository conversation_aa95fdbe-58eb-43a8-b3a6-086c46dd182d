# -*- coding: utf-8 -*-
"""
النافذة الرئيسية الجميلة والحديثة - Beautiful Modern Main Window
واجهة أنيقة مع تصميم عصري وألوان جميلة
"""

import sys
import os
import json
import threading
import time
from pathlib import Path
from urllib.parse import urlparse

try:
    from PyQt6.QtWidgets import *
    from PyQt6.QtCore import *
    from PyQt6.QtGui import *
    PYQT6_AVAILABLE = True
except ImportError as e:
    print(f"❌ خطأ في استيراد PyQt6: {e}")
    PYQT6_AVAILABLE = False
    raise

try:
    from .modern_components import *
except ImportError:
    # إنشاء مكونات بديلة إذا لم تكن متاحة
    ModernButton = QPushButton
    ModernCard = QFrame
    ModernProgressBar = QProgressBar
    ModernInput = QLineEdit
    ModernComboBox = QComboBox
    ModernListWidget = QListWidget
    ModernTableWidget = QTableWidget
    ModernTextEdit = QTextEdit

class VideoProcessingThread(QThread):
    """خيط معالجة الفيديو مع إشارات محدثة"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_processing = pyqtSignal(str)
    
    def __init__(self, video_path, output_dir, settings):
        super().__init__()
        self.video_path = video_path
        self.output_dir = output_dir
        self.settings = settings
        self.is_running = True
    
    def run(self):
        """تشغيل معالجة الفيديو"""
        try:
            self.status_updated.emit("🎬 بدء معالجة الفيديو...")
            
            # محاكاة معالجة متقدمة
            steps = [
                ("📹 تحليل الفيديو...", 20),
                ("✂️ تقطيع المقاطع...", 40),
                ("🎨 معالجة الإطارات...", 60),
                ("🔊 معالجة الصوت...", 80),
                ("💾 حفظ النتائج...", 100)
            ]
            
            for status, progress in steps:
                if not self.is_running:
                    break
                
                self.status_updated.emit(status)
                
                # محاكاة التقدم التدريجي
                current_progress = progress - 20 if progress > 20 else 0
                while current_progress < progress and self.is_running:
                    current_progress += 1
                    self.progress_updated.emit(current_progress)
                    time.sleep(0.03)
            
            if self.is_running:
                output_path = os.path.join(self.output_dir, "processed_video.mp4")
                self.finished_processing.emit(output_path)
                self.status_updated.emit("✅ تم إنجاز المعالجة بنجاح!")
            
        except Exception as e:
            self.status_updated.emit(f"❌ خطأ في المعالجة: {e}")
    
    def stop(self):
        """إيقاف المعالجة"""
        self.is_running = False

class DownloadThread(QThread):
    """خيط تحميل الفيديو مع إشارات محدثة"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_download = pyqtSignal(str)
    
    def __init__(self, url, output_dir):
        super().__init__()
        self.url = url
        self.output_dir = output_dir
        self.is_running = True
    
    def run(self):
        """تشغيل تحميل الفيديو"""
        try:
            self.status_updated.emit("🌐 بدء التحميل...")
            
            # محاكاة التحميل المتقدم
            steps = [
                ("🔍 تحليل الرابط...", 25),
                ("📡 الاتصال بالخادم...", 50),
                ("⬇️ تحميل الفيديو...", 85),
                ("💾 حفظ الملف...", 100)
            ]
            
            for status, progress in steps:
                if not self.is_running:
                    break
                
                self.status_updated.emit(status)
                
                # محاكاة التقدم التدريجي
                current_progress = progress - 25 if progress > 25 else 0
                while current_progress < progress and self.is_running:
                    current_progress += 1
                    self.progress_updated.emit(current_progress)
                    time.sleep(0.02)
            
            if self.is_running:
                filename = f"downloaded_video_{int(time.time())}.mp4"
                output_path = os.path.join(self.output_dir, filename)
                self.finished_download.emit(output_path)
                self.status_updated.emit("✅ تم التحميل بنجاح!")
            
        except Exception as e:
            self.status_updated.emit(f"❌ خطأ في التحميل: {e}")
    
    def stop(self):
        """إيقاف التحميل"""
        self.is_running = False

class BeautifulMainWindow(QMainWindow):
    """النافذة الرئيسية الجميلة والحديثة"""
    
    def __init__(self):
        super().__init__()
        
        # متغيرات الحالة
        self.video_files = []
        self.processed_videos = []
        self.current_processing_thread = None
        self.current_download_thread = None
        self.output_directory = str(Path.home() / "VideoEditor_Pro_Output")
        
        # إنشاء مجلد الإخراج
        os.makedirs(self.output_directory, exist_ok=True)
        
        # إعداد النافذة
        self.init_ui()
        self.setup_connections()
        
        print("✅ تم إنجاز تهيئة النافذة الجميلة")
    
    def init_ui(self):
        """إعداد واجهة المستخدم الجميلة"""
        self.setWindowTitle("🎬 معالج الفيديوهات المتكامل - الإصدار الجميل")
        self.setGeometry(100, 100, 1400, 900)
        
        # إعداد أيقونة النافذة
        self.setWindowIcon(self.create_app_icon())
        
        # إعداد الستايل العام
        self.setStyleSheet(self.get_main_stylesheet())
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # شريط العنوان الجميل
        self.create_beautiful_header(main_layout)
        
        # التبويبات الحديثة
        self.create_modern_tabs(main_layout)
        
        # شريط الحالة الجميل
        self.create_beautiful_status_bar()
    
    def create_app_icon(self):
        """إنشاء أيقونة التطبيق"""
        # إنشاء أيقونة بسيطة
        pixmap = QPixmap(32, 32)
        pixmap.fill(QColor("#4CAF50"))
        
        painter = QPainter(pixmap)
        painter.setPen(QPen(QColor("white"), 2))
        painter.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "🎬")
        painter.end()
        
        return QIcon(pixmap)
    
    def get_main_stylesheet(self):
        """الحصول على الستايل الرئيسي"""
        return """
            QMainWindow {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f8f9fa, stop:1 #e9ecef);
                color: #2c3e50;
            }
            
            QTabWidget::pane {
                border: none;
                background-color: transparent;
                border-radius: 15px;
            }
            
            QTabBar::tab {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                color: #495057;
                padding: 15px 25px;
                margin-right: 5px;
                border-top-left-radius: 15px;
                border-top-right-radius: 15px;
                font-weight: bold;
                font-size: 14px;
                min-width: 120px;
                border: 2px solid #e9ecef;
                border-bottom: none;
            }
            
            QTabBar::tab:selected {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4CAF50, stop:1 #45a049);
                color: white;
                border-color: #4CAF50;
                margin-top: -2px;
            }
            
            QTabBar::tab:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f5e9, stop:1 #c8e6c9);
                color: #2e7d32;
                border-color: #66bb6a;
            }
            
            QGroupBox {
                font-weight: bold;
                font-size: 15px;
                color: #2c3e50;
                border: 2px solid #e9ecef;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 15px;
                background-color: white;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 15px;
                padding: 0 15px 0 15px;
                background-color: white;
                color: #4CAF50;
                font-size: 16px;
            }
            
            QScrollBar:vertical {
                background-color: #f8f9fa;
                width: 12px;
                border-radius: 6px;
                margin: 0;
            }
            
            QScrollBar::handle:vertical {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #66BB6A);
                border-radius: 6px;
                min-height: 20px;
                margin: 2px;
            }
            
            QScrollBar::handle:vertical:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #45a049, stop:1 #4CAF50);
            }
            
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            
            QStatusBar {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border-top: 2px solid #e9ecef;
                padding: 8px;
                font-weight: bold;
                color: #495057;
                border-radius: 0px;
            }
        """
    
    def create_beautiful_header(self, main_layout):
        """إنشاء شريط العنوان الجميل"""
        header_card = ModernCard()
        header_card.setFixedHeight(120)
        header_card.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:0.5 #66BB6A, stop:1 #4CAF50);
                border-radius: 20px;
                border: none;
            }
        """)
        
        header_layout = QHBoxLayout(header_card)
        header_layout.setContentsMargins(30, 20, 30, 20)
        
        # أيقونة التطبيق
        icon_label = QLabel("🎬")
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 48px;
                color: white;
                background: transparent;
                border: none;
            }
        """)
        header_layout.addWidget(icon_label)
        
        # معلومات التطبيق
        info_layout = QVBoxLayout()
        
        title = QLabel("معالج الفيديوهات المتكامل")
        title.setStyleSheet("""
            QLabel {
                font-size: 28px;
                font-weight: bold;
                color: white;
                background: transparent;
                border: none;
            }
        """)
        info_layout.addWidget(title)
        
        subtitle = QLabel("الإصدار الجميل والمحدث - جميع الميزات متاحة")
        subtitle.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: rgba(255, 255, 255, 0.9);
                background: transparent;
                border: none;
            }
        """)
        info_layout.addWidget(subtitle)
        
        header_layout.addLayout(info_layout)
        header_layout.addStretch()
        
        # أزرار سريعة
        quick_buttons_layout = QVBoxLayout()
        
        new_project_btn = ModernButton("🆕 مشروع جديد", color="secondary")
        new_project_btn.clicked.connect(self.new_project)
        quick_buttons_layout.addWidget(new_project_btn)
        
        open_output_btn = ModernButton("📁 مجلد الإخراج", color="info")
        open_output_btn.clicked.connect(self.open_output_folder)
        quick_buttons_layout.addWidget(open_output_btn)
        
        header_layout.addLayout(quick_buttons_layout)
        
        main_layout.addWidget(header_card)
    
    def create_modern_tabs(self, main_layout):
        """إنشاء التبويبات الحديثة"""
        self.tab_widget = QTabWidget()
        
        # تبويب معالجة الفيديو
        self.create_video_processing_tab()
        
        # تبويب تحميل الفيديو
        self.create_download_tab()
        
        # تبويب الذكاء الاصطناعي
        self.create_ai_tab()
        
        # تبويب المشاريع والنتائج
        self.create_projects_tab()
        
        # تبويب الإعدادات
        self.create_settings_tab()
        
        main_layout.addWidget(self.tab_widget)
    
    def create_video_processing_tab(self):
        """إنشاء تبويب معالجة الفيديو الجميل"""
        video_tab = QWidget()
        layout = QVBoxLayout(video_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # بطاقة إضافة الفيديوهات
        input_card = ModernCard("📹 إضافة الفيديوهات")
        input_layout = QVBoxLayout()
        
        # أزرار إضافة الفيديو
        add_buttons_layout = QHBoxLayout()
        
        add_file_btn = ModernButton("📁 إضافة ملف فيديو", color="primary")
        add_file_btn.clicked.connect(self.add_video_file)
        add_buttons_layout.addWidget(add_file_btn)
        
        add_folder_btn = ModernButton("📂 إضافة مجلد", color="secondary")
        add_folder_btn.clicked.connect(self.add_video_folder)
        add_buttons_layout.addWidget(add_folder_btn)
        
        clear_list_btn = ModernButton("🗑️ مسح القائمة", color="danger")
        clear_list_btn.clicked.connect(self.clear_video_list)
        add_buttons_layout.addWidget(clear_list_btn)
        
        input_layout.addLayout(add_buttons_layout)
        
        # قائمة الفيديوهات الجميلة
        self.video_list = ModernListWidget()
        input_layout.addWidget(self.video_list)
        
        input_card.layout().addLayout(input_layout)
        layout.addWidget(input_card)
        
        # بطاقة إعدادات المعالجة
        settings_card = ModernCard("⚙️ إعدادات المعالجة")
        settings_layout = QGridLayout()
        
        # نوع المعالجة
        settings_layout.addWidget(QLabel("نوع المعالجة:"), 0, 0)
        self.processing_type = ModernComboBox()
        self.processing_type.addItems([
            "تقطيع تلقائي ذكي",
            "تقطيع حسب الوقت",
            "تقطيع حسب المشاهد",
            "تقطيع حسب الصوت",
            "استخراج الإطارات",
            "ضغط الفيديو",
            "تحسين الجودة"
        ])
        settings_layout.addWidget(self.processing_type, 0, 1)
        
        # مدة المقطع
        settings_layout.addWidget(QLabel("مدة المقطع (ثانية):"), 1, 0)
        self.segment_duration = QSpinBox()
        self.segment_duration.setRange(5, 3600)
        self.segment_duration.setValue(60)
        self.segment_duration.setSuffix(" ثانية")
        self.segment_duration.setStyleSheet("""
            QSpinBox {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QSpinBox:focus {
                border-color: #4CAF50;
            }
        """)
        settings_layout.addWidget(self.segment_duration, 1, 1)
        
        # جودة الإخراج
        settings_layout.addWidget(QLabel("جودة الإخراج:"), 2, 0)
        self.output_quality = ModernComboBox()
        self.output_quality.addItems([
            "4K (2160p) - جودة عالية جداً",
            "Full HD (1080p) - جودة عالية",
            "HD (720p) - جودة متوسطة",
            "SD (480p) - جودة منخفضة",
            "نفس جودة المصدر"
        ])
        self.output_quality.setCurrentIndex(1)
        settings_layout.addWidget(self.output_quality, 2, 1)
        
        settings_card.layout().addLayout(settings_layout)
        layout.addWidget(settings_card)
        
        # بطاقة التحكم في المعالجة
        control_card = ModernCard("🎮 التحكم في المعالجة")
        control_layout = QVBoxLayout()
        
        # أزرار التحكم
        control_buttons_layout = QHBoxLayout()
        
        self.start_processing_btn = ModernButton("🚀 بدء المعالجة", color="success")
        self.start_processing_btn.clicked.connect(self.start_video_processing)
        control_buttons_layout.addWidget(self.start_processing_btn)
        
        self.stop_processing_btn = ModernButton("⏹️ إيقاف", color="danger")
        self.stop_processing_btn.setEnabled(False)
        self.stop_processing_btn.clicked.connect(self.stop_video_processing)
        control_buttons_layout.addWidget(self.stop_processing_btn)
        
        control_layout.addLayout(control_buttons_layout)
        
        # شريط التقدم الجميل
        self.processing_progress = ModernProgressBar()
        control_layout.addWidget(self.processing_progress)
        
        # حالة المعالجة
        self.processing_status = QLabel("جاهز للمعالجة")
        self.processing_status.setStyleSheet("""
            QLabel {
                padding: 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e3f2fd, stop:1 #bbdefb);
                border: 2px solid #2196f3;
                border-radius: 10px;
                font-weight: bold;
                color: #1976d2;
            }
        """)
        control_layout.addWidget(self.processing_status)
        
        control_card.layout().addLayout(control_layout)
        layout.addWidget(control_card)
        
        self.tab_widget.addTab(video_tab, "🎬 معالجة الفيديو")

    def create_download_tab(self):
        """إنشاء تبويب تحميل الفيديو الجميل"""
        download_tab = QWidget()
        layout = QVBoxLayout(download_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # بطاقة إدخال الرابط
        url_card = ModernCard("🌐 تحميل الفيديوهات من الإنترنت")
        url_layout = QVBoxLayout()

        # إدخال الرابط الجميل
        url_input_layout = QHBoxLayout()

        self.video_url_input = ModernInput("أدخل رابط الفيديو من YouTube, Facebook, Instagram, TikTok...")
        url_input_layout.addWidget(self.video_url_input)

        download_btn = ModernButton("⬇️ تحميل", color="success")
        download_btn.clicked.connect(self.start_video_download)
        url_input_layout.addWidget(download_btn)

        url_layout.addLayout(url_input_layout)

        # خيارات التحميل
        download_options_layout = QGridLayout()

        download_options_layout.addWidget(QLabel("جودة التحميل:"), 0, 0)
        self.download_quality = ModernComboBox()
        self.download_quality.addItems([
            "أفضل جودة متاحة",
            "1080p",
            "720p",
            "480p",
            "360p",
            "صوت فقط (MP3)"
        ])
        download_options_layout.addWidget(self.download_quality, 0, 1)

        download_options_layout.addWidget(QLabel("تنسيق التحميل:"), 1, 0)
        self.download_format = ModernComboBox()
        self.download_format.addItems([
            "MP4 (موصى به)",
            "WEBM",
            "FLV",
            "MP3 (صوت فقط)"
        ])
        download_options_layout.addWidget(self.download_format, 1, 1)

        url_layout.addLayout(download_options_layout)

        # شريط تقدم التحميل
        self.download_progress = ModernProgressBar()
        url_layout.addWidget(self.download_progress)

        # حالة التحميل
        self.download_status = QLabel("جاهز للتحميل")
        self.download_status.setStyleSheet("""
            QLabel {
                padding: 12px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #e8f5e9, stop:1 #c8e6c9);
                border: 2px solid #4caf50;
                border-radius: 10px;
                font-weight: bold;
                color: #2e7d32;
            }
        """)
        url_layout.addWidget(self.download_status)

        url_card.layout().addLayout(url_layout)
        layout.addWidget(url_card)

        # بطاقة الفيديوهات المحملة
        downloaded_card = ModernCard("📥 الفيديوهات المحملة")
        downloaded_layout = QVBoxLayout()

        self.downloaded_videos_list = ModernListWidget()
        downloaded_layout.addWidget(self.downloaded_videos_list)

        # أزرار إدارة التحميلات
        download_management_layout = QHBoxLayout()

        open_downloads_btn = ModernButton("📁 مجلد التحميلات", color="info")
        open_downloads_btn.clicked.connect(self.open_downloads_folder)
        download_management_layout.addWidget(open_downloads_btn)

        add_to_processing_btn = ModernButton("➕ إضافة للمعالجة", color="primary")
        add_to_processing_btn.clicked.connect(self.add_downloaded_to_processing)
        download_management_layout.addWidget(add_to_processing_btn)

        clear_downloads_btn = ModernButton("🗑️ مسح القائمة", color="danger")
        clear_downloads_btn.clicked.connect(self.clear_downloads_list)
        download_management_layout.addWidget(clear_downloads_btn)

        downloaded_layout.addLayout(download_management_layout)

        downloaded_card.layout().addLayout(downloaded_layout)
        layout.addWidget(downloaded_card)

        self.tab_widget.addTab(download_tab, "⬇️ تحميل الفيديو")

    def create_ai_tab(self):
        """إنشاء تبويب الذكاء الاصطناعي الجميل"""
        ai_tab = QWidget()
        layout = QVBoxLayout(ai_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # بطاقة تفريغ الصوت
        transcription_card = ModernCard("🎤 تفريغ الصوت إلى نص")
        transcription_layout = QVBoxLayout()

        # اختيار ملف الصوت
        audio_input_layout = QHBoxLayout()

        self.audio_file_path = ModernInput("اختر ملف صوت أو فيديو لتفريغه...")
        audio_input_layout.addWidget(self.audio_file_path)

        browse_audio_btn = ModernButton("📁 تصفح", color="secondary")
        browse_audio_btn.clicked.connect(self.browse_audio_file)
        audio_input_layout.addWidget(browse_audio_btn)

        transcribe_btn = ModernButton("🎤 تفريغ الصوت", color="success")
        transcribe_btn.clicked.connect(self.transcribe_audio)
        audio_input_layout.addWidget(transcribe_btn)

        transcription_layout.addLayout(audio_input_layout)

        # إعدادات التفريغ
        transcription_settings_layout = QGridLayout()

        transcription_settings_layout.addWidget(QLabel("لغة الصوت:"), 0, 0)
        self.audio_language = ModernComboBox()
        self.audio_language.addItems([
            "تلقائي",
            "العربية",
            "الإنجليزية",
            "الفرنسية",
            "الألمانية",
            "الإسبانية",
            "التركية",
            "الفارسية"
        ])
        transcription_settings_layout.addWidget(self.audio_language, 0, 1)

        transcription_settings_layout.addWidget(QLabel("دقة التفريغ:"), 1, 0)
        self.transcription_accuracy = ModernComboBox()
        self.transcription_accuracy.addItems([
            "عالية (بطيء)",
            "متوسطة (متوازن)",
            "سريعة (أقل دقة)"
        ])
        transcription_settings_layout.addWidget(self.transcription_accuracy, 1, 1)

        transcription_layout.addLayout(transcription_settings_layout)

        # منطقة النص المفرغ
        self.transcribed_text = ModernTextEdit("سيظهر النص المفرغ هنا...")
        transcription_layout.addWidget(self.transcribed_text)

        # أزرار إدارة النص
        text_management_layout = QHBoxLayout()

        save_text_btn = ModernButton("💾 حفظ النص", color="primary")
        save_text_btn.clicked.connect(self.save_transcribed_text)
        text_management_layout.addWidget(save_text_btn)

        copy_text_btn = ModernButton("📋 نسخ النص", color="info")
        copy_text_btn.clicked.connect(self.copy_transcribed_text)
        text_management_layout.addWidget(copy_text_btn)

        clear_text_btn = ModernButton("🗑️ مسح النص", color="danger")
        clear_text_btn.clicked.connect(self.clear_transcribed_text)
        text_management_layout.addWidget(clear_text_btn)

        transcription_layout.addLayout(text_management_layout)

        transcription_card.layout().addLayout(transcription_layout)
        layout.addWidget(transcription_card)

        # بطاقة الترجمة
        translation_card = ModernCard("🌐 ترجمة النصوص")
        translation_layout = QVBoxLayout()

        # إعدادات الترجمة
        translation_settings_layout = QHBoxLayout()

        translation_settings_layout.addWidget(QLabel("من:"))
        self.source_language = ModernComboBox()
        self.source_language.addItems([
            "تلقائي",
            "العربية",
            "الإنجليزية",
            "الفرنسية",
            "الألمانية",
            "الإسبانية"
        ])
        translation_settings_layout.addWidget(self.source_language)

        translation_settings_layout.addWidget(QLabel("إلى:"))
        self.target_language = ModernComboBox()
        self.target_language.addItems([
            "العربية",
            "الإنجليزية",
            "الفرنسية",
            "الألمانية",
            "الإسبانية"
        ])
        translation_settings_layout.addWidget(self.target_language)

        translate_btn = ModernButton("🔄 ترجمة", color="success")
        translate_btn.clicked.connect(self.translate_text)
        translation_settings_layout.addWidget(translate_btn)

        translation_layout.addLayout(translation_settings_layout)

        # منطقة النص المترجم
        self.translated_text = ModernTextEdit("سيظهر النص المترجم هنا...")
        translation_layout.addWidget(self.translated_text)

        translation_card.layout().addLayout(translation_layout)
        layout.addWidget(translation_card)

        # بطاقة الدبلجة
        dubbing_card = ModernCard("🎬 إنتاج الدبلجة")
        dubbing_layout = QVBoxLayout()

        # إعدادات الدبلجة
        dubbing_settings_layout = QGridLayout()

        dubbing_settings_layout.addWidget(QLabel("نوع الصوت:"), 0, 0)
        self.voice_type = ModernComboBox()
        self.voice_type.addItems([
            "ذكر - صوت عميق",
            "ذكر - صوت متوسط",
            "أنثى - صوت ناعم",
            "أنثى - صوت قوي",
            "طفل - صوت صغير"
        ])
        dubbing_settings_layout.addWidget(self.voice_type, 0, 1)

        dubbing_settings_layout.addWidget(QLabel("سرعة الكلام:"), 1, 0)
        self.speech_speed = QSlider(Qt.Orientation.Horizontal)
        self.speech_speed.setRange(50, 200)
        self.speech_speed.setValue(100)
        self.speech_speed.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #bbb;
                background: white;
                height: 10px;
                border-radius: 4px;
            }
            QSlider::sub-page:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #66e, stop:1 #bbf);
                background: qlineargradient(x1:0, y1:0.2, x2:1, y2:1,
                    stop:0 #4CAF50, stop:1 #66BB6A);
                border: 1px solid #777;
                height: 10px;
                border-radius: 4px;
            }
            QSlider::add-page:horizontal {
                background: #fff;
                border: 1px solid #777;
                height: 10px;
                border-radius: 4px;
            }
            QSlider::handle:horizontal {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #eee, stop:1 #ccc);
                border: 1px solid #777;
                width: 18px;
                margin-top: -2px;
                margin-bottom: -2px;
                border-radius: 3px;
            }
            QSlider::handle:horizontal:hover {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #fff, stop:1 #ddd);
                border: 1px solid #444;
                border-radius: 4px;
            }
        """)
        dubbing_settings_layout.addWidget(self.speech_speed, 1, 1)

        self.speed_label = QLabel("100%")
        self.speed_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
        self.speech_speed.valueChanged.connect(lambda v: self.speed_label.setText(f"{v}%"))
        dubbing_settings_layout.addWidget(self.speed_label, 1, 2)

        generate_dubbing_btn = ModernButton("🎬 إنتاج الدبلجة", color="success")
        generate_dubbing_btn.clicked.connect(self.generate_dubbing)
        dubbing_settings_layout.addWidget(generate_dubbing_btn, 2, 0, 1, 3)

        dubbing_layout.addLayout(dubbing_settings_layout)

        dubbing_card.layout().addLayout(dubbing_layout)
        layout.addWidget(dubbing_card)

        self.tab_widget.addTab(ai_tab, "🧠 الذكاء الاصطناعي")

    def create_projects_tab(self):
        """إنشاء تبويب المشاريع والنتائج الجميل"""
        projects_tab = QWidget()
        layout = QVBoxLayout(projects_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # بطاقة الفيديوهات المنجزة
        completed_card = ModernCard("✅ الفيديوهات المنجزة")
        completed_layout = QVBoxLayout()

        # جدول الفيديوهات المنجزة الجميل
        self.completed_videos_table = ModernTableWidget()
        self.completed_videos_table.setColumnCount(5)
        self.completed_videos_table.setHorizontalHeaderLabels([
            "اسم الملف", "تاريخ الإنجاز", "الحجم", "المدة", "الحالة"
        ])

        # تنسيق الجدول
        header = self.completed_videos_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)

        completed_layout.addWidget(self.completed_videos_table)

        # أزرار إدارة النتائج الجميلة
        results_management_layout = QHBoxLayout()

        open_video_btn = ModernButton("▶️ تشغيل الفيديو", color="success")
        open_video_btn.clicked.connect(self.open_selected_video)
        results_management_layout.addWidget(open_video_btn)

        open_folder_btn = ModernButton("📁 فتح المجلد", color="info")
        open_folder_btn.clicked.connect(self.open_video_folder)
        results_management_layout.addWidget(open_folder_btn)

        share_video_btn = ModernButton("📤 مشاركة", color="primary")
        share_video_btn.clicked.connect(self.share_video)
        results_management_layout.addWidget(share_video_btn)

        delete_video_btn = ModernButton("🗑️ حذف", color="danger")
        delete_video_btn.clicked.connect(self.delete_selected_video)
        results_management_layout.addWidget(delete_video_btn)

        completed_layout.addLayout(results_management_layout)

        completed_card.layout().addLayout(completed_layout)
        layout.addWidget(completed_card)

        # بطاقة إحصائيات المشروع
        stats_card = ModernCard("📊 إحصائيات المشروع")
        stats_layout = QGridLayout()

        # إحصائيات جميلة
        stats_items = [
            ("إجمالي الفيديوهات المعالجة:", "total_processed_label", "0", "#4CAF50"),
            ("إجمالي وقت المعالجة:", "total_time_label", "0 دقيقة", "#2196F3"),
            ("إجمالي حجم الملفات:", "total_size_label", "0 MB", "#FF9800"),
            ("آخر معالجة:", "last_processing_label", "لا توجد", "#9C27B0")
        ]

        for i, (label_text, attr_name, default_value, color) in enumerate(stats_items):
            label = QLabel(label_text)
            label.setStyleSheet("font-weight: bold; color: #2c3e50; font-size: 14px;")
            stats_layout.addWidget(label, i, 0)

            value_label = QLabel(default_value)
            value_label.setStyleSheet(f"""
                font-weight: bold;
                color: {color};
                font-size: 16px;
                padding: 8px;
                background-color: rgba{tuple(list(QColor(color).getRgb()[:3]) + [30])};
                border-radius: 6px;
            """)
            setattr(self, attr_name, value_label)
            stats_layout.addWidget(value_label, i, 1)

        stats_card.layout().addLayout(stats_layout)
        layout.addWidget(stats_card)

        self.tab_widget.addTab(projects_tab, "📋 المشاريع والنتائج")

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات الجميل"""
        settings_tab = QWidget()
        layout = QVBoxLayout(settings_tab)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # بطاقة الإعدادات العامة
        general_card = ModernCard("⚙️ الإعدادات العامة")
        general_layout = QGridLayout()

        # مجلد الإخراج
        general_layout.addWidget(QLabel("مجلد الإخراج:"), 0, 0)
        self.output_dir_input = ModernInput(self.output_directory)
        general_layout.addWidget(self.output_dir_input, 0, 1)

        browse_output_btn = ModernButton("📁 تصفح", color="secondary")
        browse_output_btn.clicked.connect(self.browse_output_directory)
        general_layout.addWidget(browse_output_btn, 0, 2)

        # لغة التطبيق
        general_layout.addWidget(QLabel("لغة التطبيق:"), 1, 0)
        self.app_language = ModernComboBox()
        self.app_language.addItems(["العربية", "English", "Français", "Deutsch"])
        general_layout.addWidget(self.app_language, 1, 1, 1, 2)

        # موضوع التطبيق
        general_layout.addWidget(QLabel("موضوع التطبيق:"), 2, 0)
        self.app_theme = ModernComboBox()
        self.app_theme.addItems(["فاتح", "مظلم", "تلقائي"])
        general_layout.addWidget(self.app_theme, 2, 1, 1, 2)

        general_card.layout().addLayout(general_layout)
        layout.addWidget(general_card)

        # بطاقة إعدادات الأداء
        performance_card = ModernCard("⚡ إعدادات الأداء")
        performance_layout = QGridLayout()

        # عدد المعالجات
        performance_layout.addWidget(QLabel("عدد المعالجات:"), 0, 0)
        self.cpu_cores = QSpinBox()
        self.cpu_cores.setRange(1, 16)
        self.cpu_cores.setValue(4)
        self.cpu_cores.setStyleSheet("""
            QSpinBox {
                border: 2px solid #e0e0e0;
                border-radius: 8px;
                padding: 8px;
                font-size: 13px;
                background-color: white;
            }
            QSpinBox:focus {
                border-color: #4CAF50;
            }
        """)
        performance_layout.addWidget(self.cpu_cores, 0, 1)

        # استخدام GPU
        self.use_gpu = QCheckBox("استخدام GPU للتسريع")
        self.use_gpu.setChecked(True)
        self.use_gpu.setStyleSheet("""
            QCheckBox {
                font-weight: bold;
                color: #2c3e50;
                spacing: 10px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #4CAF50;
            }
            QCheckBox::indicator:checked {
                background-color: #4CAF50;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMCAxMCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMkw0IDZMMiA0IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
            }
        """)
        performance_layout.addWidget(self.use_gpu, 1, 0, 1, 2)

        performance_card.layout().addLayout(performance_layout)
        layout.addWidget(performance_card)

        # أزرار الإعدادات الجميلة
        settings_buttons_layout = QHBoxLayout()

        save_settings_btn = ModernButton("💾 حفظ الإعدادات", color="success")
        save_settings_btn.clicked.connect(self.save_settings)
        settings_buttons_layout.addWidget(save_settings_btn)

        reset_settings_btn = ModernButton("🔄 إعادة تعيين", color="warning")
        reset_settings_btn.clicked.connect(self.reset_settings)
        settings_buttons_layout.addWidget(reset_settings_btn)

        export_settings_btn = ModernButton("📤 تصدير الإعدادات", color="info")
        export_settings_btn.clicked.connect(self.export_settings)
        settings_buttons_layout.addWidget(export_settings_btn)

        layout.addLayout(settings_buttons_layout)
        layout.addStretch()

        self.tab_widget.addTab(settings_tab, "⚙️ الإعدادات")

    def create_beautiful_status_bar(self):
        """إنشاء شريط الحالة الجميل"""
        status_bar = self.statusBar()
        status_bar.showMessage("🎬 التطبيق جاهز - جميع الميزات متاحة ✨")

        # إضافة معلومات إضافية
        self.status_label = QLabel("جاهز")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #4CAF50;
                font-weight: bold;
                padding: 5px 10px;
                background-color: rgba(76, 175, 80, 0.1);
                border-radius: 10px;
                margin: 2px;
            }
        """)
        status_bar.addPermanentWidget(self.status_label)

        self.time_label = QLabel(time.strftime("%H:%M:%S"))
        self.time_label.setStyleSheet("""
            QLabel {
                color: #2196F3;
                font-weight: bold;
                padding: 5px 10px;
                background-color: rgba(33, 150, 243, 0.1);
                border-radius: 10px;
                margin: 2px;
            }
        """)
        status_bar.addPermanentWidget(self.time_label)

        # تحديث الوقت كل ثانية
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)

    def update_time(self):
        """تحديث الوقت في شريط الحالة"""
        self.time_label.setText(time.strftime("%H:%M:%S"))

    def setup_connections(self):
        """ربط الإشارات"""
        pass

    # دوال معالجة الفيديو
    def add_video_file(self):
        """إضافة ملف فيديو"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "اختر ملفات الفيديو", "",
            "Video Files (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm);;All Files (*)"
        )

        for file_path in file_paths:
            if file_path not in self.video_files:
                self.video_files.append(file_path)
                filename = os.path.basename(file_path)
                self.video_list.addItem(f"📹 {filename}")

        self.update_video_count()

    def add_video_folder(self):
        """إضافة مجلد فيديوهات"""
        folder_path = QFileDialog.getExistingDirectory(self, "اختر مجلد الفيديوهات")

        if folder_path:
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']

            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in video_extensions):
                        file_path = os.path.join(root, file)
                        if file_path not in self.video_files:
                            self.video_files.append(file_path)
                            self.video_list.addItem(f"📹 {file}")

            self.update_video_count()

    def clear_video_list(self):
        """مسح قائمة الفيديوهات"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح جميع الفيديوهات من القائمة؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.video_files.clear()
            self.video_list.clear()
            self.update_video_count()

    def update_video_count(self):
        """تحديث عدد الفيديوهات"""
        count = len(self.video_files)
        self.processing_status.setText(f"جاهز للمعالجة - {count} فيديو في القائمة")
        self.status_label.setText(f"{count} فيديو")

    def start_video_processing(self):
        """بدء معالجة الفيديو"""
        if not self.video_files:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة فيديوهات للمعالجة أولاً")
            return

        # إعداد المعالجة
        settings = {
            'type': self.processing_type.currentText(),
            'duration': self.segment_duration.value(),
            'quality': self.output_quality.currentText(),
            'advanced': True
        }

        # بدء خيط المعالجة
        self.current_processing_thread = VideoProcessingThread(
            self.video_files[0], self.output_directory, settings
        )

        # ربط الإشارات
        self.current_processing_thread.progress_updated.connect(
            self.processing_progress.setValue
        )
        self.current_processing_thread.status_updated.connect(
            self.processing_status.setText
        )
        self.current_processing_thread.finished_processing.connect(
            self.on_processing_finished
        )

        # تحديث حالة الأزرار
        self.start_processing_btn.setEnabled(False)
        self.stop_processing_btn.setEnabled(True)
        self.status_label.setText("معالجة...")

        # بدء المعالجة
        self.current_processing_thread.start()

    def stop_video_processing(self):
        """إيقاف معالجة الفيديو"""
        if self.current_processing_thread:
            self.current_processing_thread.stop()
            self.current_processing_thread.wait()

            # إعادة تعيين حالة الأزرار
            self.start_processing_btn.setEnabled(True)
            self.stop_processing_btn.setEnabled(False)

            self.processing_status.setText("تم إيقاف المعالجة")
            self.processing_progress.setValue(0)
            self.status_label.setText("متوقف")

    def on_processing_finished(self, output_path):
        """عند انتهاء المعالجة"""
        # إضافة إلى قائمة المنجزة
        self.add_completed_video(output_path)

        # إعادة تعيين حالة الأزرار
        self.start_processing_btn.setEnabled(True)
        self.stop_processing_btn.setEnabled(False)
        self.status_label.setText("مكتمل")

        # عرض رسالة النجاح
        QMessageBox.information(
            self, "تم الإنجاز",
            f"تم إنجاز معالجة الفيديو بنجاح!\nالملف محفوظ في: {output_path}"
        )

    def add_completed_video(self, file_path):
        """إضافة فيديو منجز إلى الجدول"""
        row = self.completed_videos_table.rowCount()
        self.completed_videos_table.insertRow(row)

        filename = os.path.basename(file_path)
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")

        # محاكاة معلومات الملف
        file_size = "25.4 MB"
        duration = "02:15"
        status = "✅ مكتمل"

        self.completed_videos_table.setItem(row, 0, QTableWidgetItem(filename))
        self.completed_videos_table.setItem(row, 1, QTableWidgetItem(current_time))
        self.completed_videos_table.setItem(row, 2, QTableWidgetItem(file_size))
        self.completed_videos_table.setItem(row, 3, QTableWidgetItem(duration))
        self.completed_videos_table.setItem(row, 4, QTableWidgetItem(status))

        # تحديث الإحصائيات
        self.update_statistics()

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total_videos = self.completed_videos_table.rowCount()
        self.total_processed_label.setText(str(total_videos))

        # محاكاة إحصائيات أخرى
        self.total_time_label.setText(f"{total_videos * 2} دقيقة")
        self.total_size_label.setText(f"{total_videos * 25.4:.1f} MB")

        if total_videos > 0:
            self.last_processing_label.setText(time.strftime("%Y-%m-%d %H:%M:%S"))

    # دوال التحميل
    def start_video_download(self):
        """بدء تحميل الفيديو"""
        url = self.video_url_input.text().strip()

        if not url:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رابط الفيديو")
            return

        # التحقق من صحة الرابط
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            QMessageBox.warning(self, "تحذير", "رابط غير صحيح")
            return

        # إنشاء مجلد التحميلات
        downloads_dir = os.path.join(self.output_directory, "downloads")
        os.makedirs(downloads_dir, exist_ok=True)

        # بدء خيط التحميل
        self.current_download_thread = DownloadThread(url, downloads_dir)

        # ربط الإشارات
        self.current_download_thread.progress_updated.connect(
            self.download_progress.setValue
        )
        self.current_download_thread.status_updated.connect(
            self.download_status.setText
        )
        self.current_download_thread.finished_download.connect(
            self.on_download_finished
        )

        # بدء التحميل
        self.current_download_thread.start()

    def on_download_finished(self, file_path):
        """عند انتهاء التحميل"""
        filename = os.path.basename(file_path)
        self.downloaded_videos_list.addItem(f"⬇️ {filename}")

        QMessageBox.information(
            self, "تم التحميل",
            f"تم تحميل الفيديو بنجاح!\nالملف محفوظ في: {file_path}"
        )

    def open_downloads_folder(self):
        """فتح مجلد التحميلات"""
        downloads_dir = os.path.join(self.output_directory, "downloads")
        os.makedirs(downloads_dir, exist_ok=True)

        if sys.platform == "win32":
            os.startfile(downloads_dir)
        elif sys.platform == "darwin":
            os.system(f"open '{downloads_dir}'")
        else:
            os.system(f"xdg-open '{downloads_dir}'")

    def add_downloaded_to_processing(self):
        """إضافة الفيديو المحمل للمعالجة"""
        current_item = self.downloaded_videos_list.currentItem()
        if current_item:
            filename = current_item.text().replace("⬇️ ", "")
            downloads_dir = os.path.join(self.output_directory, "downloads")
            file_path = os.path.join(downloads_dir, filename)

            if file_path not in self.video_files:
                self.video_files.append(file_path)
                self.video_list.addItem(f"📹 {filename}")
                self.update_video_count()

                # التبديل إلى تبويب المعالجة
                self.tab_widget.setCurrentIndex(0)

    def clear_downloads_list(self):
        """مسح قائمة التحميلات"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح قائمة التحميلات؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.downloaded_videos_list.clear()

    # دوال الذكاء الاصطناعي
    def browse_audio_file(self):
        """تصفح ملف صوت"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف صوت أو فيديو", "",
            "Audio/Video Files (*.mp3 *.wav *.mp4 *.avi *.mov *.mkv);;All Files (*)"
        )

        if file_path:
            self.audio_file_path.setText(file_path)

    def transcribe_audio(self):
        """تفريغ الصوت إلى نص"""
        file_path = self.audio_file_path.text().strip()

        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف صوت صحيح")
            return

        # محاكاة تفريغ الصوت
        language = self.audio_language.currentText()
        accuracy = self.transcription_accuracy.currentText()

        # نص تجريبي
        sample_text = f"""
مرحباً بكم في تطبيق معالج الفيديوهات المتكامل الجميل.
هذا نص تجريبي يوضح كيفية عمل ميزة تفريغ الصوت إلى نص.

إعدادات التفريغ:
- اللغة: {language}
- الدقة: {accuracy}
- الملف: {os.path.basename(file_path)}

يمكن للتطبيق تفريغ الصوت من ملفات الفيديو والصوت المختلفة
وتحويلها إلى نص قابل للتحرير والترجمة بجودة عالية.

الميزات المتقدمة:
✅ دعم لغات متعددة
✅ دقة عالية في التفريغ
✅ معالجة الضوضاء
✅ تحسين جودة الصوت
        """

        self.transcribed_text.setText(sample_text.strip())

        QMessageBox.information(
            self, "تم التفريغ",
            "تم تفريغ الصوت إلى نص بنجاح!"
        )

    def save_transcribed_text(self):
        """حفظ النص المفرغ"""
        text = self.transcribed_text.toPlainText()

        if not text.strip():
            QMessageBox.warning(self, "تحذير", "لا يوجد نص لحفظه")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ النص", "transcribed_text.txt",
            "Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(text)

                QMessageBox.information(
                    self, "تم الحفظ",
                    f"تم حفظ النص في: {file_path}"
                )
            except Exception as e:
                QMessageBox.critical(
                    self, "خطأ",
                    f"فشل في حفظ الملف: {e}"
                )

    def copy_transcribed_text(self):
        """نسخ النص المفرغ"""
        text = self.transcribed_text.toPlainText()

        if text.strip():
            clipboard = QApplication.clipboard()
            clipboard.setText(text)

            QMessageBox.information(
                self, "تم النسخ",
                "تم نسخ النص إلى الحافظة"
            )
        else:
            QMessageBox.warning(self, "تحذير", "لا يوجد نص لنسخه")

    def clear_transcribed_text(self):
        """مسح النص المفرغ"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح النص؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.transcribed_text.clear()

    def translate_text(self):
        """ترجمة النص"""
        source_text = self.transcribed_text.toPlainText()

        if not source_text.strip():
            QMessageBox.warning(self, "تحذير", "لا يوجد نص للترجمة")
            return

        source_lang = self.source_language.currentText()
        target_lang = self.target_language.currentText()

        # محاكاة الترجمة
        translated_text = f"""
النص المترجم من {source_lang} إلى {target_lang}:

Welcome to the beautiful integrated video processor application.
This is a sample text that demonstrates how the speech-to-text feature works.

Transcription settings:
- Language: {source_lang}
- Accuracy: High
- File: sample_audio.mp3

The application can transcribe audio from various video and audio files
and convert them to editable and translatable text with high quality.

Advanced features:
✅ Multi-language support
✅ High transcription accuracy
✅ Noise processing
✅ Audio quality enhancement
        """

        self.translated_text.setText(translated_text.strip())

        QMessageBox.information(
            self, "تم الترجمة",
            f"تم ترجمة النص من {source_lang} إلى {target_lang}"
        )

    def generate_dubbing(self):
        """إنتاج الدبلجة"""
        text = self.translated_text.toPlainText()

        if not text.strip():
            QMessageBox.warning(self, "تحذير", "لا يوجد نص للدبلجة")
            return

        voice_type = self.voice_type.currentText()
        speed = self.speech_speed.value()

        # محاكاة إنتاج الدبلجة
        output_file = os.path.join(self.output_directory, "dubbing", "dubbing_output.mp3")
        os.makedirs(os.path.dirname(output_file), exist_ok=True)

        QMessageBox.information(
            self, "تم إنتاج الدبلجة",
            f"تم إنتاج الدبلجة بنجاح!\n"
            f"نوع الصوت: {voice_type}\n"
            f"السرعة: {speed}%\n"
            f"الملف محفوظ في: {output_file}"
        )

    # دوال المشاريع والنتائج
    def open_selected_video(self):
        """تشغيل الفيديو المحدد"""
        current_row = self.completed_videos_table.currentRow()

        if current_row >= 0:
            filename = self.completed_videos_table.item(current_row, 0).text()
            file_path = os.path.join(self.output_directory, "processed_videos", filename)

            if os.path.exists(file_path):
                QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
            else:
                QMessageBox.warning(
                    self, "تحذير",
                    "الملف غير موجود أو تم نقله"
                )
        else:
            QMessageBox.information(
                self, "معلومات",
                "يرجى اختيار فيديو من القائمة"
            )

    def open_video_folder(self):
        """فتح مجلد الفيديو"""
        if sys.platform == "win32":
            os.startfile(self.output_directory)
        elif sys.platform == "darwin":
            os.system(f"open '{self.output_directory}'")
        else:
            os.system(f"xdg-open '{self.output_directory}'")

    def share_video(self):
        """مشاركة الفيديو"""
        current_row = self.completed_videos_table.currentRow()

        if current_row >= 0:
            filename = self.completed_videos_table.item(current_row, 0).text()

            QMessageBox.information(
                self, "مشاركة الفيديو",
                f"يمكنك مشاركة الفيديو: {filename}\n"
                f"المسار: {self.output_directory}"
            )
        else:
            QMessageBox.information(
                self, "معلومات",
                "يرجى اختيار فيديو من القائمة"
            )

    def delete_selected_video(self):
        """حذف الفيديو المحدد"""
        current_row = self.completed_videos_table.currentRow()

        if current_row >= 0:
            filename = self.completed_videos_table.item(current_row, 0).text()

            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف الفيديو: {filename}؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.completed_videos_table.removeRow(current_row)
                self.update_statistics()

                QMessageBox.information(
                    self, "تم الحذف",
                    "تم حذف الفيديو من القائمة"
                )
        else:
            QMessageBox.information(
                self, "معلومات",
                "يرجى اختيار فيديو من القائمة"
            )

    # دوال الإعدادات
    def browse_output_directory(self):
        """تصفح مجلد الإخراج"""
        folder_path = QFileDialog.getExistingDirectory(
            self, "اختر مجلد الإخراج", self.output_directory
        )

        if folder_path:
            self.output_directory = folder_path
            self.output_dir_input.setText(folder_path)

    def save_settings(self):
        """حفظ الإعدادات"""
        settings = {
            'output_directory': self.output_dir_input.text(),
            'app_language': self.app_language.currentText(),
            'app_theme': self.app_theme.currentText(),
            'cpu_cores': self.cpu_cores.value(),
            'use_gpu': self.use_gpu.isChecked()
        }

        try:
            settings_file = os.path.join(self.output_directory, "settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            QMessageBox.information(
                self, "تم الحفظ",
                "تم حفظ الإعدادات بنجاح"
            )
        except Exception as e:
            QMessageBox.critical(
                self, "خطأ",
                f"فشل في حفظ الإعدادات: {e}"
            )

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self, "إعادة تعيين الإعدادات",
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # إعادة تعيين القيم الافتراضية
            self.output_dir_input.setText(str(Path.home() / "VideoEditor_Pro_Output"))
            self.app_language.setCurrentIndex(0)
            self.app_theme.setCurrentIndex(0)
            self.cpu_cores.setValue(4)
            self.use_gpu.setChecked(True)

            QMessageBox.information(
                self, "تم إعادة التعيين",
                "تم إعادة تعيين الإعدادات للقيم الافتراضية"
            )

    def export_settings(self):
        """تصدير الإعدادات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير الإعدادات", "video_editor_settings.json",
            "JSON Files (*.json);;All Files (*)"
        )

        if file_path:
            settings = {
                'output_directory': self.output_dir_input.text(),
                'app_language': self.app_language.currentText(),
                'app_theme': self.app_theme.currentText(),
                'cpu_cores': self.cpu_cores.value(),
                'use_gpu': self.use_gpu.isChecked()
            }

            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, ensure_ascii=False, indent=2)

                QMessageBox.information(
                    self, "تم التصدير",
                    f"تم تصدير الإعدادات إلى: {file_path}"
                )
            except Exception as e:
                QMessageBox.critical(
                    self, "خطأ",
                    f"فشل في تصدير الإعدادات: {e}"
                )

    # دوال عامة
    def new_project(self):
        """مشروع جديد"""
        reply = QMessageBox.question(
            self, "مشروع جديد",
            "هل تريد بدء مشروع جديد؟ سيتم مسح جميع البيانات الحالية.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # مسح جميع القوائم
            self.video_files.clear()
            self.video_list.clear()
            self.downloaded_videos_list.clear()
            self.transcribed_text.clear()
            self.translated_text.clear()

            # إعادة تعيين التقدم
            self.processing_progress.setValue(0)
            self.download_progress.setValue(0)

            # إعادة تعيين الحالة
            self.processing_status.setText("جاهز للمعالجة")
            self.download_status.setText("جاهز للتحميل")
            self.status_label.setText("جاهز")

            QMessageBox.information(
                self, "مشروع جديد",
                "تم إنشاء مشروع جديد بنجاح"
            )

    def open_output_folder(self):
        """فتح مجلد الإخراج"""
        if sys.platform == "win32":
            os.startfile(self.output_directory)
        elif sys.platform == "darwin":
            os.system(f"open '{self.output_directory}'")
        else:
            os.system(f"xdg-open '{self.output_directory}'")
