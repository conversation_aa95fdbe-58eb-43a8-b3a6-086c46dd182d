#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للبرنامج
Quick Program Test
"""

import sys
import traceback
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_syntax_errors():
    """اختبار أخطاء الصيغة"""
    print("🔍 اختبار أخطاء الصيغة...")
    
    files_to_test = [
        "gui/__init__.py",
        "gui/main_window.py",
        "main.py"
    ]
    
    errors_found = []
    
    for file_path in files_to_test:
        full_path = project_root / file_path
        if not full_path.exists():
            print(f"  ⚠️ {file_path} غير موجود")
            continue
            
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # اختبار الصيغة
            compile(content, str(full_path), 'exec')
            print(f"  ✅ {file_path}")
            
        except SyntaxError as e:
            error_msg = f"{file_path}: السطر {e.lineno}: {e.msg}"
            print(f"  ❌ {error_msg}")
            errors_found.append(error_msg)
        except Exception as e:
            error_msg = f"{file_path}: {e}"
            print(f"  ⚠️ {error_msg}")
    
    return errors_found

def test_imports():
    """اختبار الاستيرادات"""
    print("\n📦 اختبار الاستيرادات...")
    
    import_tests = [
        ("PyQt6.QtWidgets", "from PyQt6.QtWidgets import QApplication"),
        ("PyQt6.QtCore", "from PyQt6.QtCore import Qt"),
        ("gui", "from gui import MainWindow"),
        ("main", "import main")
    ]
    
    failed_imports = []
    
    for name, import_cmd in import_tests:
        try:
            exec(import_cmd)
            print(f"  ✅ {name}")
        except Exception as e:
            error_msg = f"{name}: {e}"
            print(f"  ❌ {error_msg}")
            failed_imports.append(error_msg)
    
    return failed_imports

def test_main_window_creation():
    """اختبار إنشاء النافذة الرئيسية"""
    print("\n🖼️ اختبار إنشاء النافذة الرئيسية...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from gui import MainWindow
        
        # إنشاء تطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء النافذة
        window = MainWindow()
        print("  ✅ تم إنشاء النافذة بنجاح")
        
        # اختبار بعض الخصائص
        window.setWindowTitle("اختبار")
        window.resize(800, 600)
        print("  ✅ تم تعيين خصائص النافذة")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في إنشاء النافذة: {e}")
        traceback.print_exc()
        return False

def generate_error_report(syntax_errors, import_errors, window_creation_failed):
    """إنشاء تقرير الأخطاء"""
    print("\n" + "="*50)
    print("📊 تقرير الأخطاء")
    print("="*50)
    
    total_errors = len(syntax_errors) + len(import_errors) + (1 if window_creation_failed else 0)
    
    if total_errors == 0:
        print("🎉 لا توجد أخطاء! البرنامج جاهز للتشغيل")
        print("\n🚀 يمكنك تشغيل البرنامج باستخدام:")
        print("  python main.py")
        return True
    
    print(f"❌ تم العثور على {total_errors} خطأ")
    
    if syntax_errors:
        print(f"\n🔴 أخطاء الصيغة ({len(syntax_errors)}):")
        for error in syntax_errors:
            print(f"  • {error}")
    
    if import_errors:
        print(f"\n🟠 أخطاء الاستيراد ({len(import_errors)}):")
        for error in import_errors:
            print(f"  • {error}")
    
    if window_creation_failed:
        print(f"\n🟡 خطأ في إنشاء النافذة الرئيسية")
    
    print("\n💡 الحلول المقترحة:")
    
    if any("IndentationError" in error for error in syntax_errors):
        print("  🔧 إصلاح المسافات البادئة: python fix_all_errors.py")
    
    if any("PyQt6" in error for error in import_errors):
        print("  📦 تثبيت PyQt6: pip install PyQt6")
    
    if import_errors or window_creation_failed:
        print("  🚀 استخدام النسخة الآمنة: python main_safe.py")
    
    print("  🔧 الإصلاح الشامل: python fix_all_errors.py")
    
    return False

def main():
    """الدالة الرئيسية للاختبار السريع"""
    print("🧪 اختبار سريع لمعالج الفيديوهات المتكامل")
    print("="*50)
    
    # 1. اختبار أخطاء الصيغة
    syntax_errors = test_syntax_errors()
    
    # 2. اختبار الاستيرادات
    import_errors = test_imports()
    
    # 3. اختبار إنشاء النافذة الرئيسية
    window_creation_success = test_main_window_creation()
    
    # 4. إنشاء تقرير الأخطاء
    success = generate_error_report(
        syntax_errors, 
        import_errors, 
        not window_creation_success
    )
    
    return success

if __name__ == "__main__":
    try:
        success = main()
        input("\nاضغط Enter للخروج...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف الاختبار")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
