#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق الكامل - Complete Application
معالج الفيديوهات المتكامل مع جميع الميزات
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    # فحص Python
    if sys.version_info < (3, 8):
        print(f"❌ يتطلب Python 3.8+ (الحالي: {sys.version})")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # فحص PyQt6
    try:
        import PyQt6
        print(f"✅ PyQt6 {PyQt6.QtCore.PYQT_VERSION_STR}")
    except ImportError:
        print("❌ PyQt6 غير مثبت")
        print("💡 الحل: pip install PyQt6")
        return False
    
    return True

def setup_application():
    """إعداد التطبيق"""
    print("🚀 إعداد التطبيق...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QIcon
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("معالج الفيديوهات المتكامل")
        app.setApplicationVersion("2.0.0")
        app.setOrganizationName("Video Editor Pro")
        
        # إعداد RTL
        try:
            app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            print("✅ تم إعداد اتجاه RTL")
        except:
            try:
                app.setLayoutDirection(Qt.RightToLeft)
                print("✅ تم إعداد اتجاه RTL (طريقة بديلة)")
            except:
                app.setLayoutDirection(2)
                print("✅ تم إعداد اتجاه RTL (رقمي)")
        
        # إعداد الستايل العام
        app.setStyleSheet("""
            /* الستايل العام للتطبيق */
            QMainWindow {
                background-color: #f8f9fa;
                color: #212529;
            }
            
            /* التبويبات */
            QTabWidget::pane {
                border: 2px solid #dee2e6;
                background-color: white;
                border-radius: 10px;
                margin-top: 5px;
            }
            
            QTabBar::tab {
                background-color: #e9ecef;
                color: #495057;
                padding: 12px 20px;
                margin-right: 3px;
                border-top-left-radius: 10px;
                border-top-right-radius: 10px;
                font-weight: bold;
                font-size: 13px;
                min-width: 120px;
            }
            
            QTabBar::tab:selected {
                background-color: #4CAF50;
                color: white;
                border-bottom: 2px solid #4CAF50;
            }
            
            QTabBar::tab:hover {
                background-color: #f8f9fa;
                color: #4CAF50;
            }
            
            /* المجموعات */
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #495057;
                border: 2px solid #dee2e6;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 10px 0 10px;
                background-color: white;
                color: #4CAF50;
            }
            
            /* حقول الإدخال */
            QLineEdit, QTextEdit, QComboBox, QSpinBox {
                border: 2px solid #ced4da;
                border-radius: 6px;
                padding: 8px;
                font-size: 12px;
                background-color: white;
            }
            
            QLineEdit:focus, QTextEdit:focus, QComboBox:focus, QSpinBox:focus {
                border-color: #4CAF50;
                outline: none;
            }
            
            /* القوائم */
            QListWidget, QTableWidget {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                background-color: white;
                alternate-background-color: #f8f9fa;
                gridline-color: #dee2e6;
            }
            
            QListWidget::item, QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #dee2e6;
            }
            
            QListWidget::item:selected, QTableWidget::item:selected {
                background-color: #4CAF50;
                color: white;
            }
            
            QListWidget::item:hover, QTableWidget::item:hover {
                background-color: #e8f5e9;
            }
            
            /* أشرطة التقدم */
            QProgressBar {
                border: 2px solid #dee2e6;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 12px;
                height: 25px;
                background-color: #f8f9fa;
            }
            
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:0.5 #66BB6A, stop:1 #4CAF50);
                border-radius: 6px;
                margin: 1px;
            }
            
            /* أشرطة التمرير */
            QScrollBar:vertical {
                background-color: #f8f9fa;
                width: 12px;
                border-radius: 6px;
            }
            
            QScrollBar::handle:vertical {
                background-color: #ced4da;
                border-radius: 6px;
                min-height: 20px;
            }
            
            QScrollBar::handle:vertical:hover {
                background-color: #adb5bd;
            }
            
            /* شريط الحالة */
            QStatusBar {
                background-color: #e9ecef;
                border-top: 1px solid #dee2e6;
                padding: 5px;
                font-weight: bold;
                color: #495057;
            }
            
            /* التلميحات */
            QToolTip {
                background-color: #343a40;
                color: white;
                border: 1px solid #495057;
                border-radius: 4px;
                padding: 5px;
                font-size: 11px;
            }
        """)
        
        print("✅ تم إعداد الستايل")
        return app
        
    except Exception as e:
        print(f"❌ خطأ في إعداد التطبيق: {e}")
        return None

def create_main_window():
    """إنشاء النافذة الرئيسية"""
    print("🖼️ إنشاء النافذة الرئيسية...")
    
    try:
        from gui.main_window_complete import CompleteMainWindow
        
        window = CompleteMainWindow()
        print("✅ تم إنشاء النافذة الرئيسية")
        
        return window
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد النافذة: {e}")
        print("🔄 محاولة استخدام النافذة البديلة...")
        
        try:
            from gui.main_window_safe import SafeMainWindow
            window = SafeMainWindow()
            print("✅ تم إنشاء النافذة البديلة")
            return window
        except:
            print("❌ فشل في إنشاء النافذة البديلة")
            return None
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة: {e}")
        return None

def show_welcome_message():
    """عرض رسالة الترحيب"""
    print("\n" + "="*60)
    print("🎬 معالج الفيديوهات المتكامل - الإصدار الكامل")
    print("="*60)
    print("✨ الميزات المتاحة:")
    print("  🎬 معالجة وتقطيع الفيديوهات")
    print("  ⬇️ تحميل الفيديوهات من الإنترنت")
    print("  🎤 تفريغ الصوت إلى نص")
    print("  🌐 ترجمة النصوص")
    print("  🎭 إنتاج الدبلجة")
    print("  📊 إدارة المشاريع والنتائج")
    print("  ⚙️ إعدادات متقدمة")
    print("="*60)
    print("🚀 التطبيق جاهز للاستخدام!")
    print("="*60)

def main():
    """الدالة الرئيسية"""
    try:
        # عرض رسالة الترحيب
        show_welcome_message()
        
        # فحص المتطلبات
        if not check_requirements():
            input("\nاضغط Enter للخروج...")
            return 1
        
        # إعداد التطبيق
        app = setup_application()
        if not app:
            input("\nاضغط Enter للخروج...")
            return 1
        
        # إنشاء النافذة الرئيسية
        window = create_main_window()
        if not window:
            print("❌ فشل في إنشاء النافذة الرئيسية")
            input("\nاضغط Enter للخروج...")
            return 1
        
        # عرض النافذة
        print("📺 عرض النافذة...")
        window.show()
        
        print("✅ تم تشغيل التطبيق بنجاح!")
        print("🎉 استمتع بجميع الميزات المتقدمة!")
        
        # تشغيل حلقة الأحداث
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ خطأ كارثي: {e}")
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
