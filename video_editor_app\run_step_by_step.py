#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل تدريجي للتطبيق
Step-by-Step Application Launch
"""

import sys
import time
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def step_1_check_python():
    """الخطوة 1: فحص Python"""
    print("🐍 الخطوة 1: فحص Python...")
    
    print(f"  إصدار Python: {sys.version}")
    print(f"  مسار Python: {sys.executable}")
    
    if sys.version_info < (3, 8):
        print("  ❌ يتطلب Python 3.8 أو أحدث")
        return False
    
    print("  ✅ Python متوافق")
    return True

def step_2_check_pyqt6():
    """الخطوة 2: فحص PyQt6"""
    print("\n🖥️ الخطوة 2: فحص PyQt6...")
    
    try:
        print("  - استيراد PyQt6...")
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        print("  ✅ PyQt6 متاح")
        
        print("  - إنشاء QApplication...")
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("  ✅ QApplication تم إنشاؤه")
        
        return True, app
        
    except Exception as e:
        print(f"  ❌ خطأ في PyQt6: {e}")
        return False, None

def step_3_test_simple_window(app):
    """الخطوة 3: اختبار نافذة بسيطة"""
    print("\n🪟 الخطوة 3: اختبار نافذة بسيطة...")
    
    try:
        from PyQt6.QtWidgets import QMainWindow, QLabel, QWidget, QVBoxLayout
        from PyQt6.QtCore import Qt
        
        print("  - إنشاء نافذة...")
        window = QMainWindow()
        window.setWindowTitle("اختبار بسيط")
        window.resize(400, 300)
        
        print("  - إضافة محتوى...")
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        label = QLabel("اختبار النافذة البسيطة")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
        window.setCentralWidget(central_widget)
        
        print("  - عرض النافذة...")
        window.show()
        
        print("  ✅ النافذة البسيطة تعمل")
        return True, window
        
    except Exception as e:
        print(f"  ❌ خطأ في النافذة البسيطة: {e}")
        return False, None

def step_4_test_project_imports():
    """الخطوة 4: اختبار استيراد وحدات المشروع"""
    print("\n📦 الخطوة 4: اختبار وحدات المشروع...")
    
    modules = {}
    
    # config
    try:
        print("  - استيراد config...")
        import config
        modules['config'] = config
        print("  ✅ config")
    except Exception as e:
        print(f"  ⚠️ config: {e}")
        modules['config'] = None
    
    # utils
    try:
        print("  - استيراد utils...")
        from utils import VideoEditorLogger
        modules['utils'] = True
        print("  ✅ utils")
    except Exception as e:
        print(f"  ⚠️ utils: {e}")
        modules['utils'] = False
    
    # database
    try:
        print("  - استيراد database...")
        from database import DatabaseManager
        modules['database'] = DatabaseManager
        print("  ✅ database")
    except Exception as e:
        print(f"  ⚠️ database: {e}")
        modules['database'] = None
    
    # src
    try:
        print("  - استيراد src...")
        from src import VideoEditorCore
        modules['src'] = VideoEditorCore
        print("  ✅ src")
    except Exception as e:
        print(f"  ⚠️ src: {e}")
        modules['src'] = None
    
    # gui
    try:
        print("  - استيراد gui...")
        from gui import MainWindow
        modules['gui'] = MainWindow
        print("  ✅ gui")
    except Exception as e:
        print(f"  ❌ gui: {e}")
        modules['gui'] = None
    
    return modules

def step_5_test_main_window(modules):
    """الخطوة 5: اختبار النافذة الرئيسية"""
    print("\n🖼️ الخطوة 5: اختبار النافذة الرئيسية...")
    
    if modules['gui'] is None:
        print("  ❌ MainWindow غير متاح")
        return False, None
    
    try:
        MainWindow = modules['gui']
        
        print("  - إنشاء MainWindow...")
        window = MainWindow()
        
        print("  - تعيين خصائص النافذة...")
        window.setWindowTitle("معالج الفيديوهات - اختبار")
        window.resize(800, 600)
        
        print("  ✅ MainWindow تم إنشاؤه بنجاح")
        return True, window
        
    except Exception as e:
        print(f"  ❌ خطأ في MainWindow: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def step_6_show_and_run(app, window):
    """الخطوة 6: عرض النافذة وتشغيل التطبيق"""
    print("\n🚀 الخطوة 6: تشغيل التطبيق...")
    
    try:
        print("  - عرض النافذة...")
        window.show()
        
        print("  - بدء حلقة الأحداث...")
        print("  ✅ التطبيق يعمل! (اضغط Ctrl+C للإيقاف)")
        
        sys.exit(app.exec())
        
    except KeyboardInterrupt:
        print("\n  ⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True
    except Exception as e:
        print(f"  ❌ خطأ في تشغيل التطبيق: {e}")
        return False

def run_alternative_if_failed():
    """تشغيل بديل إذا فشل التطبيق الرئيسي"""
    print("\n🔄 تشغيل البديل...")
    
    alternatives = [
        ("main_fixed.py", "النسخة المحسنة"),
        ("main_simple.py", "النسخة البسيطة"),
        ("main_emergency.py", "وضع الطوارئ"),
    ]
    
    for script, description in alternatives:
        script_path = Path(__file__).parent / script
        if script_path.exists():
            print(f"  🔧 جرب تشغيل: python {script} ({description})")
        else:
            print(f"  ⚠️ {script} غير موجود")

def main():
    """الدالة الرئيسية للتشغيل التدريجي"""
    print("🎬 تشغيل تدريجي لمعالج الفيديوهات المتكامل")
    print("="*50)
    
    # الخطوة 1: فحص Python
    if not step_1_check_python():
        return False
    
    # الخطوة 2: فحص PyQt6
    pyqt6_success, app = step_2_check_pyqt6()
    if not pyqt6_success:
        print("\n❌ فشل في PyQt6")
        print("💡 الحل: pip install PyQt6")
        return False
    
    # الخطوة 3: اختبار نافذة بسيطة
    simple_success, simple_window = step_3_test_simple_window(app)
    if not simple_success:
        print("\n❌ فشل في النافذة البسيطة")
        return False
    
    # إغلاق النافذة البسيطة
    simple_window.close()
    
    # الخطوة 4: اختبار وحدات المشروع
    modules = step_4_test_project_imports()
    
    # الخطوة 5: اختبار النافذة الرئيسية
    main_window_success, main_window = step_5_test_main_window(modules)
    
    if not main_window_success:
        print("\n❌ فشل في النافذة الرئيسية")
        run_alternative_if_failed()
        return False
    
    # الخطوة 6: تشغيل التطبيق
    return step_6_show_and_run(app, main_window)

if __name__ == "__main__":
    try:
        success = main()
        if not success:
            input("\nاضغط Enter للخروج...")
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
