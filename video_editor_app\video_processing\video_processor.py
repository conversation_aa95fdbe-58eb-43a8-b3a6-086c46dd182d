# -*- coding: utf-8 -*-
"""
وحدة معالجة الفيديوهات
Video Processing Module for Video Editor Application
"""

import os
import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional, Callable, Tuple
import logging
from datetime import timedelta

try:
    from moviepy.editor import VideoFileClip, concatenate_videoclips, CompositeVideoClip
    from moviepy.video.fx import resize, fadein, fadeout
    from moviepy.audio.fx import volumex
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False

try:
    import ffmpeg
    FFMPEG_AVAILABLE = True
except ImportError:
    FFMPEG_AVAILABLE = False

from utils.logger import VideoEditorLogger

class VideoProcessor:
    """فئة معالجة الفيديوهات"""
    
    def __init__(self, output_path: str = None):
        """
        تهيئة معالج الفيديوهات
        
        Args:
            output_path: مسار الإخراج الافتراضي
        """
        self.logger = VideoEditorLogger(__name__)
        
        if output_path is None:
            output_path = Path(__file__).parent.parent / "output_videos"
        
        self.output_path = Path(output_path)
        self.output_path.mkdir(exist_ok=True)
        
        # التحقق من توفر المكتبات
        if not MOVIEPY_AVAILABLE:
            self.logger.warning("مكتبة MoviePy غير متاحة")
        if not FFMPEG_AVAILABLE:
            self.logger.warning("مكتبة FFmpeg غير متاحة")
    
    def get_video_info(self, video_path: str) -> Dict:
        """
        الحصول على معلومات الفيديو
        
        Args:
            video_path: مسار الفيديو
            
        Returns:
            Dict: معلومات الفيديو
        """
        try:
            if MOVIEPY_AVAILABLE:
                with VideoFileClip(video_path) as clip:
                    return {
                        'duration': clip.duration,
                        'fps': clip.fps,
                        'size': clip.size,
                        'width': clip.w,
                        'height': clip.h,
                        'aspect_ratio': clip.w / clip.h,
                        'has_audio': clip.audio is not None,
                        'file_size': Path(video_path).stat().st_size
                    }
            else:
                # استخدام OpenCV كبديل
                cap = cv2.VideoCapture(video_path)
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                duration = frame_count / fps if fps > 0 else 0
                cap.release()
                
                return {
                    'duration': duration,
                    'fps': fps,
                    'size': (width, height),
                    'width': width,
                    'height': height,
                    'aspect_ratio': width / height if height > 0 else 1,
                    'has_audio': True,  # افتراضي
                    'file_size': Path(video_path).stat().st_size
                }
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على معلومات الفيديو: {str(e)}")
            return {}
    
    def cut_video_into_segments(self, video_path: str, segment_duration: int = 30,
                               cut_type: str = 'equal', progress_callback: Callable = None) -> List[str]:
        """
        قص الفيديو إلى مقاطع
        
        Args:
            video_path: مسار الفيديو
            segment_duration: مدة المقطع بالثواني
            cut_type: نوع القص ('equal', 'smart', 'scene', 'audio')
            progress_callback: دالة تحديث التقدم
            
        Returns:
            List[str]: قائمة مسارات المقاطع
        """
        try:
            if not MOVIEPY_AVAILABLE:
                raise Exception("مكتبة MoviePy مطلوبة لقص الفيديوهات")
            
            self.logger.operation_start("قص الفيديو", video_path)
            
            with VideoFileClip(video_path) as video:
                video_duration = video.duration
                segments = []
                
                if cut_type == 'equal':
                    segments = self._cut_equal_segments(video, segment_duration, progress_callback)
                elif cut_type == 'smart':
                    segments = self._cut_smart_segments(video, segment_duration, progress_callback)
                elif cut_type == 'scene':
                    segments = self._cut_scene_segments(video, segment_duration, progress_callback)
                elif cut_type == 'audio':
                    segments = self._cut_audio_segments(video, segment_duration, progress_callback)
                
                self.logger.operation_complete("قص الفيديو", video_path, len(segments))
                return segments
                
        except Exception as e:
            self.logger.operation_error("قص الفيديو", video_path, str(e))
            return []
    
    def _cut_equal_segments(self, video: VideoFileClip, segment_duration: int,
                           progress_callback: Callable = None) -> List[str]:
        """قص مقاطع متساوية"""
        segments = []
        video_name = Path(video.filename).stem
        total_duration = video.duration
        num_segments = int(total_duration / segment_duration) + 1
        
        for i in range(num_segments):
            start_time = i * segment_duration
            end_time = min((i + 1) * segment_duration, total_duration)
            
            if end_time - start_time < 5:  # تجاهل المقاطع الأقل من 5 ثوان
                continue
            
            segment = video.subclip(start_time, end_time)
            output_path = self.output_path / f"{video_name}_segment_{i+1:03d}.mp4"
            
            segment.write_videofile(
                str(output_path),
                codec='libx264',
                audio_codec='aac',
                verbose=False,
                logger=None
            )
            
            segments.append(str(output_path))
            
            if progress_callback:
                progress = int((i + 1) / num_segments * 100)
                progress_callback(progress)
        
        return segments
    
    def _cut_smart_segments(self, video: VideoFileClip, segment_duration: int,
                           progress_callback: Callable = None) -> List[str]:
        """قص ذكي بناءً على المحتوى"""
        # تنفيذ مبسط - يمكن تحسينه لاحقاً
        return self._cut_equal_segments(video, segment_duration, progress_callback)
    
    def _cut_scene_segments(self, video: VideoFileClip, segment_duration: int,
                           progress_callback: Callable = None) -> List[str]:
        """قص حسب المشاهد"""
        # تنفيذ مبسط - يمكن تحسينه لاحقاً
        return self._cut_equal_segments(video, segment_duration, progress_callback)
    
    def _cut_audio_segments(self, video: VideoFileClip, segment_duration: int,
                           progress_callback: Callable = None) -> List[str]:
        """قص حسب الصوت"""
        # تنفيذ مبسط - يمكن تحسينه لاحقاً
        return self._cut_equal_segments(video, segment_duration, progress_callback)
    
    def apply_automatic_montage(self, video_path: str, montage_options: Dict,
                               progress_callback: Callable = None) -> str:
        """
        تطبيق مونتاج تلقائي
        
        Args:
            video_path: مسار الفيديو
            montage_options: خيارات المونتاج
            progress_callback: دالة تحديث التقدم
            
        Returns:
            str: مسار الفيديو المعدل
        """
        try:
            if not MOVIEPY_AVAILABLE:
                raise Exception("مكتبة MoviePy مطلوبة للمونتاج")
            
            self.logger.operation_start("المونتاج التلقائي", video_path)
            
            with VideoFileClip(video_path) as video:
                clips = [video]
                
                # إضافة مقدمة
                if montage_options.get('add_intro', False):
                    intro_clip = self._create_intro_clip(video.size, video.fps)
                    clips.insert(0, intro_clip)
                
                # إضافة انتقالات
                if montage_options.get('add_transitions', False):
                    clips = self._add_transitions(clips)
                
                # إضافة فلاتر
                filter_type = montage_options.get('filter_type', 'none')
                if filter_type != 'none':
                    clips = [self._apply_filter(clip, filter_type) for clip in clips]
                
                # إضافة خاتمة
                if montage_options.get('add_outro', False):
                    outro_clip = self._create_outro_clip(video.size, video.fps)
                    clips.append(outro_clip)
                
                # دمج المقاطع
                final_video = concatenate_videoclips(clips)
                
                # إضافة موسيقى خلفية
                if montage_options.get('add_music', False):
                    final_video = self._add_background_music(final_video)
                
                # حفظ الفيديو النهائي
                video_name = Path(video_path).stem
                output_path = self.output_path / f"{video_name}_montage.mp4"
                
                final_video.write_videofile(
                    str(output_path),
                    codec='libx264',
                    audio_codec='aac',
                    verbose=False,
                    logger=None
                )
                
                if progress_callback:
                    progress_callback(100)
                
                self.logger.operation_complete("المونتاج التلقائي", video_path, 0)
                return str(output_path)
                
        except Exception as e:
            self.logger.operation_error("المونتاج التلقائي", video_path, str(e))
            return ""
    
    def _create_intro_clip(self, size: Tuple[int, int], fps: float):
        """إنشاء مقدمة"""
        # مقدمة بسيطة - يمكن تحسينها
        from moviepy.editor import ColorClip, TextClip, CompositeVideoClip
        
        # خلفية ملونة
        bg = ColorClip(size=size, color=(0, 0, 0), duration=3)
        
        # نص المقدمة
        txt = TextClip("مرحباً بكم", fontsize=50, color='white', font='Arial')
        txt = txt.set_position('center').set_duration(3)
        
        return CompositeVideoClip([bg, txt])
    
    def _create_outro_clip(self, size: Tuple[int, int], fps: float):
        """إنشاء خاتمة"""
        from moviepy.editor import ColorClip, TextClip, CompositeVideoClip
        
        # خلفية ملونة
        bg = ColorClip(size=size, color=(0, 0, 0), duration=2)
        
        # نص الخاتمة
        txt = TextClip("شكراً لكم", fontsize=50, color='white', font='Arial')
        txt = txt.set_position('center').set_duration(2)
        
        return CompositeVideoClip([bg, txt])
    
    def _add_transitions(self, clips: List):
        """إضافة انتقالات بين المقاطع"""
        if len(clips) <= 1:
            return clips
        
        transition_clips = []
        for i, clip in enumerate(clips):
            if i == 0:
                # أول مقطع - إضافة fade in
                clip = fadein(clip, 0.5)
            elif i == len(clips) - 1:
                # آخر مقطع - إضافة fade out
                clip = fadeout(clip, 0.5)
            else:
                # المقاطع الوسطى - إضافة fade in و fade out
                clip = fadein(fadeout(clip, 0.5), 0.5)
            
            transition_clips.append(clip)
        
        return transition_clips
    
    def _apply_filter(self, clip, filter_type: str):
        """تطبيق فلتر على المقطع"""
        # تنفيذ مبسط للفلاتر
        if filter_type == 'brightness':
            return clip.fx(lambda gf, t: gf(t) * 1.2)
        elif filter_type == 'contrast':
            return clip.fx(lambda gf, t: np.clip(gf(t) * 1.5, 0, 255))
        else:
            return clip
    
    def _add_background_music(self, video):
        """إضافة موسيقى خلفية"""
        # تنفيذ مبسط - يحتاج ملف موسيقى
        return video
    
    def resize_video(self, video_path: str, target_resolution: Tuple[int, int],
                    progress_callback: Callable = None) -> str:
        """
        تغيير حجم الفيديو
        
        Args:
            video_path: مسار الفيديو
            target_resolution: الدقة المطلوبة (width, height)
            progress_callback: دالة تحديث التقدم
            
        Returns:
            str: مسار الفيديو المعدل
        """
        try:
            if not MOVIEPY_AVAILABLE:
                raise Exception("مكتبة MoviePy مطلوبة لتغيير الحجم")
            
            with VideoFileClip(video_path) as video:
                resized_video = resize(video, target_resolution)
                
                video_name = Path(video_path).stem
                output_path = self.output_path / f"{video_name}_resized.mp4"
                
                resized_video.write_videofile(
                    str(output_path),
                    codec='libx264',
                    audio_codec='aac',
                    verbose=False,
                    logger=None
                )
                
                if progress_callback:
                    progress_callback(100)
                
                return str(output_path)
                
        except Exception as e:
            self.logger.error(f"خطأ في تغيير حجم الفيديو: {str(e)}")
            return ""
    
    def extract_audio(self, video_path: str) -> str:
        """
        استخراج الصوت من الفيديو
        
        Args:
            video_path: مسار الفيديو
            
        Returns:
            str: مسار ملف الصوت
        """
        try:
            if not MOVIEPY_AVAILABLE:
                raise Exception("مكتبة MoviePy مطلوبة لاستخراج الصوت")
            
            with VideoFileClip(video_path) as video:
                audio = video.audio
                
                if audio is None:
                    raise Exception("الفيديو لا يحتوي على صوت")
                
                video_name = Path(video_path).stem
                output_path = self.output_path / f"{video_name}_audio.wav"
                
                audio.write_audiofile(str(output_path), verbose=False, logger=None)
                
                return str(output_path)
                
        except Exception as e:
            self.logger.error(f"خطأ في استخراج الصوت: {str(e)}")
            return ""
