#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة محسنة من التطبيق الرئيسي
Improved Version of Main Application
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def safe_import_pyqt6():
    """استيراد آمن لـ PyQt6"""
    try:
        from PyQt6.QtWidgets import QApplication, QMessageBox
        from PyQt6.QtCore import QTranslator, QLocale, Qt
        from PyQt6.QtGui import QIcon
        return True, (QApplication, QMessageBox, QTranslator, QLocale, Qt, QIcon)
    except ImportError as e:
        print(f"❌ خطأ في استيراد PyQt6: {e}")
        print("يرجى تثبيت PyQt6: pip install PyQt6")
        return False, None

def safe_import_modules():
    """استيراد آمن للوحدات"""
    modules = {}
    
    # استيراد config
    try:
        import config
        modules['config'] = config
        print("✅ تم استيراد config")
    except ImportError as e:
        print(f"⚠️ خطأ في استيراد config: {e}")
        modules['config'] = None
    
    # استيراد DatabaseManager
    try:
        from database.db_manager import DatabaseManager
        modules['DatabaseManager'] = DatabaseManager
        print("✅ تم استيراد DatabaseManager")
    except ImportError as e:
        print(f"⚠️ خطأ في استيراد DatabaseManager: {e}")
        modules['DatabaseManager'] = None
    
    # استيراد logger
    try:
        from utils.logger import setup_logger
        modules['setup_logger'] = setup_logger
        print("✅ تم استيراد setup_logger")
    except ImportError as e:
        print(f"⚠️ خطأ في استيراد setup_logger: {e}")
        modules['setup_logger'] = None
    
    # استيراد MainWindow
    try:
        from gui.main_window import MainWindow
        modules['MainWindow'] = MainWindow
        print("✅ تم استيراد MainWindow")
    except ImportError as e:
        print(f"❌ خطأ في استيراد MainWindow: {e}")
        modules['MainWindow'] = None
    
    return modules

def setup_rtl_layout(app, Qt):
    """إعداد اتجاه RTL للغة العربية بشكل آمن"""
    rtl_methods = [
        (lambda: app.setLayoutDirection(Qt.LayoutDirection.RightToLeft), "Qt.LayoutDirection.RightToLeft"),
        (lambda: app.setLayoutDirection(Qt.RightToLeft), "Qt.RightToLeft"),
        (lambda: app.setLayoutDirection(2), "الرقم المباشر (2)"),
    ]
    
    for method, description in rtl_methods:
        try:
            method()
            print(f"✅ تم تعيين اتجاه RTL باستخدام {description}")
            return True
        except (AttributeError, TypeError, Exception) as e:
            print(f"⚠️ فشل {description}: {e}")
            continue
    
    print("❌ فشل في تعيين اتجاه RTL بجميع الطرق")
    return False

def setup_application(QApplication, Qt):
    """إعداد التطبيق الأساسي"""
    print("🔧 إعداد التطبيق...")
    
    app = QApplication(sys.argv)
    app.setApplicationName("معالج الفيديوهات المتكامل")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Video Editor Team")
    
    # إعداد اللغة العربية (RTL)
    setup_rtl_layout(app, Qt)
    
    # إعداد الستايل
    try:
        import qdarkstyle
        app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt6())
        print("✅ تم تطبيق qdarkstyle")
    except ImportError:
        # ستايل مظلم بسيط كبديل
        app.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #3b3b3b;
            }
            QTabBar::tab {
                background-color: #555;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
            }
        """)
        print("✅ تم تطبيق الستايل الافتراضي")
    
    return app

def initialize_config(config):
    """تهيئة الإعدادات"""
    if config:
        try:
            config.create_directories()
            print("✅ تم إنشاء المجلدات")
            return True
        except Exception as e:
            print(f"⚠️ خطأ في إنشاء المجلدات: {e}")
            return False
    return True

def initialize_database(DatabaseManager, QMessageBox):
    """تهيئة قاعدة البيانات"""
    if DatabaseManager:
        try:
            db_manager = DatabaseManager()
            db_manager.initialize_database()
            print("✅ تم تهيئة قاعدة البيانات")
            return True
        except Exception as e:
            print(f"⚠️ خطأ في قاعدة البيانات: {e}")
            try:
                QMessageBox.warning(None, "تحذير", f"خطأ في قاعدة البيانات: {e}")
            except:
                pass
            return False
    return True

def create_main_window(MainWindow):
    """إنشاء النافذة الرئيسية"""
    if MainWindow:
        try:
            print("🖼️ إنشاء النافذة الرئيسية...")
            main_window = MainWindow()
            print("✅ تم إنشاء النافذة الرئيسية")
            return main_window
        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة الرئيسية: {e}")
            import traceback
            traceback.print_exc()
            return None
    else:
        print("❌ MainWindow غير متاح")
        return None

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    print("🎬 معالج الفيديوهات المتكامل - نسخة محسنة")
    print("="*50)
    
    try:
        # 1. استيراد PyQt6
        print("📦 استيراد PyQt6...")
        pyqt6_success, pyqt6_modules = safe_import_pyqt6()
        if not pyqt6_success:
            return False
        
        QApplication, QMessageBox, QTranslator, QLocale, Qt, QIcon = pyqt6_modules
        
        # 2. استيراد الوحدات
        print("\n📦 استيراد وحدات المشروع...")
        modules = safe_import_modules()
        
        # 3. إعداد نظام السجلات
        print("\n📝 إعداد نظام السجلات...")
        if modules['setup_logger']:
            try:
                logger = modules['setup_logger']()
                logger.info("بدء تشغيل تطبيق معالج الفيديوهات")
                print("✅ تم إعداد نظام السجلات")
            except Exception as e:
                print(f"⚠️ خطأ في نظام السجلات: {e}")
                logger = None
        else:
            logger = None
        
        # 4. إنشاء التطبيق
        print("\n🚀 إنشاء التطبيق...")
        app = setup_application(QApplication, Qt)
        
        # 5. تهيئة الإعدادات
        print("\n⚙️ تهيئة الإعدادات...")
        initialize_config(modules['config'])
        
        # 6. تهيئة قاعدة البيانات
        print("\n💾 تهيئة قاعدة البيانات...")
        initialize_database(modules['DatabaseManager'], QMessageBox)
        
        # 7. إنشاء النافذة الرئيسية
        print("\n🖼️ إنشاء النافذة الرئيسية...")
        main_window = create_main_window(modules['MainWindow'])
        
        if main_window is None:
            print("❌ فشل في إنشاء النافذة الرئيسية")
            QMessageBox.critical(None, "خطأ", "فشل في إنشاء النافذة الرئيسية")
            return False
        
        # 8. عرض النافذة
        print("\n📺 عرض النافذة...")
        main_window.show()
        
        if logger:
            logger.info("تم تشغيل التطبيق بنجاح")
        else:
            print("✅ تم تشغيل التطبيق بنجاح")
        
        # 9. تشغيل حلقة الأحداث
        print("\n🔄 بدء حلقة الأحداث...")
        sys.exit(app.exec())
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return True
    except Exception as e:
        error_msg = f"خطأ في تشغيل التطبيق: {str(e)}"
        print(f"❌ {error_msg}")
        
        # محاولة عرض رسالة خطأ رسومية
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            QMessageBox.critical(None, "خطأ في التطبيق", error_msg)
        except:
            pass
        
        import traceback
        traceback.print_exc()
        
        if logger:
            logger.error(error_msg)
        
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
