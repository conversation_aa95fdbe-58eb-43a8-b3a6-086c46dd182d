#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لجميع أخطاء البرنامج
Complete Fix for All Program Errors
"""

import sys
import os
import re
import subprocess
import shutil
from pathlib import Path

def backup_files():
    """نسخ احتياطي للملفات"""
    print("💾 إنشاء نسخ احتياطية...")
    
    backup_dir = Path(__file__).parent / "backup_complete_fix"
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "main.py",
        "gui/__init__.py",
        "gui/main_window.py",
        "src/__init__.py",
        "utils/__init__.py",
        "database/__init__.py"
    ]
    
    for file_path in files_to_backup:
        source = Path(__file__).parent / file_path
        if source.exists():
            dest = backup_dir / f"{source.name}.backup"
            shutil.copy2(source, dest)
            print(f"  ✅ تم نسخ {file_path}")

def fix_indentation_errors():
    """إصلاح أخطاء المسافات البادئة"""
    print("🔧 إصلاح أخطاء المسافات البادئة...")
    
    python_files = [
        "gui/main_window.py",
        "gui/__init__.py",
        "src/__init__.py",
        "utils/__init__.py",
        "database/__init__.py",
        "main.py"
    ]
    
    for file_path in python_files:
        full_path = Path(__file__).parent / file_path
        if not full_path.exists():
            continue
            
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # إصلاح المسافات البادئة الشائعة
            lines = content.split('\n')
            fixed_lines = []
            
            for i, line in enumerate(lines):
                # إصلاح المسافات المختلطة
                if line.strip():
                    # تحويل tabs إلى spaces
                    line = line.expandtabs(4)
                    
                    # إصلاح المسافات البادئة غير المتسقة
                    stripped = line.lstrip()
                    if stripped:
                        indent_level = (len(line) - len(stripped)) // 4
                        line = '    ' * indent_level + stripped
                
                fixed_lines.append(line)
            
            fixed_content = '\n'.join(fixed_lines)
            
            # كتابة الملف المصحح
            if fixed_content != content:
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                print(f"  ✅ تم إصلاح {file_path}")
            
        except Exception as e:
            print(f"  ❌ خطأ في إصلاح {file_path}: {e}")

def fix_import_errors():
    """إصلاح أخطاء الاستيراد"""
    print("📦 إصلاح أخطاء الاستيراد...")
    
    # إصلاح gui/__init__.py
    gui_init_path = Path(__file__).parent / "gui" / "__init__.py"
    if gui_init_path.exists():
        try:
            gui_init_content = '''# -*- coding: utf-8 -*-
"""
وحدة واجهة المستخدم الرسومية
GUI Module
"""

try:
    from .main_window import MainWindow
    print("✅ تم استيراد MainWindow")
except ImportError as e:
    print(f"⚠️ خطأ في استيراد MainWindow: {e}")
    
    # إنشاء MainWindow وهمي
    from PyQt6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QLabel
    
    class MainWindow(QMainWindow):
        def __init__(self):
            super().__init__()
            self.setWindowTitle("معالج الفيديوهات - وضع الطوارئ")
            self.setGeometry(100, 100, 800, 600)
            
            central_widget = QWidget()
            self.setCentralWidget(central_widget)
            
            layout = QVBoxLayout(central_widget)
            label = QLabel("التطبيق يعمل في وضع الطوارئ")
            layout.addWidget(label)

__all__ = ['MainWindow']
'''
            
            with open(gui_init_path, 'w', encoding='utf-8') as f:
                f.write(gui_init_content)
            print("  ✅ تم إصلاح gui/__init__.py")
            
        except Exception as e:
            print(f"  ❌ خطأ في إصلاح gui/__init__.py: {e}")

def create_safe_main():
    """إنشاء main.py آمن"""
    print("🚀 إنشاء main.py آمن...")
    
    safe_main_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي الآمن
Safe Main Application
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def safe_import_pyqt6():
    """استيراد آمن لـ PyQt6"""
    try:
        from PyQt6.QtWidgets import QApplication, QMessageBox
        from PyQt6.QtCore import Qt
        return True, (QApplication, QMessageBox, Qt)
    except ImportError as e:
        print(f"❌ خطأ في استيراد PyQt6: {e}")
        print("💡 الحل: pip install PyQt6")
        return False, None

def safe_import_gui():
    """استيراد آمن للواجهة"""
    try:
        from gui import MainWindow
        return True, MainWindow
    except ImportError as e:
        print(f"❌ خطأ في استيراد GUI: {e}")
        return False, None

def setup_rtl(app, Qt):
    """إعداد RTL آمن"""
    try:
        app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        return True
    except:
        try:
            app.setLayoutDirection(Qt.RightToLeft)
            return True
        except:
            try:
                app.setLayoutDirection(2)
                return True
            except:
                return False

def main():
    """الدالة الرئيسية"""
    print("🎬 معالج الفيديوهات المتكامل")
    print("="*40)
    
    try:
        # 1. استيراد PyQt6
        pyqt6_success, pyqt6_modules = safe_import_pyqt6()
        if not pyqt6_success:
            input("اضغط Enter للخروج...")
            return False
        
        QApplication, QMessageBox, Qt = pyqt6_modules
        
        # 2. إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("معالج الفيديوهات المتكامل")
        
        # 3. إعداد RTL
        setup_rtl(app, Qt)
        
        # 4. استيراد الواجهة
        gui_success, MainWindow = safe_import_gui()
        if not gui_success:
            QMessageBox.critical(None, "خطأ", "فشل في تحميل واجهة المستخدم")
            return False
        
        # 5. إنشاء النافذة الرئيسية
        try:
            window = MainWindow()
            window.show()
            print("✅ تم تشغيل التطبيق بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة: {e}")
            QMessageBox.critical(None, "خطأ", f"فشل في إنشاء النافذة: {e}")
            return False
        
        # 6. تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\\n⏹️ تم إيقاف التطبيق")
        return True
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ خطأ كارثي: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
'''
    
    try:
        main_path = Path(__file__).parent / "main_fixed_complete.py"
        with open(main_path, 'w', encoding='utf-8') as f:
            f.write(safe_main_content)
        print(f"  ✅ تم إنشاء {main_path}")
        return True
    except Exception as e:
        print(f"  ❌ فشل في إنشاء main آمن: {e}")
        return False

def test_syntax():
    """اختبار صحة الصيغة"""
    print("🧪 اختبار صحة الصيغة...")
    
    python_files = [
        "gui/__init__.py",
        "gui/main_window.py",
        "main_fixed_complete.py"
    ]
    
    all_valid = True
    
    for file_path in python_files:
        full_path = Path(__file__).parent / file_path
        if not full_path.exists():
            continue
            
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # اختبار الصيغة
            compile(content, str(full_path), 'exec')
            print(f"  ✅ {file_path} صحيح")
            
        except SyntaxError as e:
            print(f"  ❌ خطأ صيغة في {file_path}: {e}")
            all_valid = False
        except Exception as e:
            print(f"  ⚠️ خطأ في {file_path}: {e}")
    
    return all_valid

def install_requirements():
    """تثبيت المتطلبات"""
    print("📦 تثبيت المتطلبات...")
    
    requirements = [
        "PyQt6>=6.6.0",
        "requests>=2.31.0",
        "Pillow>=10.0.0"
    ]
    
    for package in requirements:
        try:
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--upgrade", "--quiet"
            ])
            print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"  ⚠️ فشل في تثبيت {package}")

def main():
    """الدالة الرئيسية للإصلاح الشامل"""
    print("🔧 الإصلاح الشامل لجميع أخطاء البرنامج")
    print("="*50)
    
    # 1. نسخ احتياطي
    backup_files()
    print()
    
    # 2. تثبيت المتطلبات
    install_requirements()
    print()
    
    # 3. إصلاح المسافات البادئة
    fix_indentation_errors()
    print()
    
    # 4. إصلاح أخطاء الاستيراد
    fix_import_errors()
    print()
    
    # 5. إنشاء main آمن
    safe_main_created = create_safe_main()
    print()
    
    # 6. اختبار الصيغة
    syntax_valid = test_syntax()
    print()
    
    # النتيجة النهائية
    print("="*50)
    print("📊 النتائج:")
    print(f"  main آمن: {'✅ تم إنشاؤه' if safe_main_created else '❌ فشل'}")
    print(f"  صحة الصيغة: {'✅ صحيحة' if syntax_valid else '❌ أخطاء'}")
    
    if safe_main_created and syntax_valid:
        print("\\n🎉 تم إصلاح جميع الأخطاء!")
        print("\\n🚀 طرق التشغيل:")
        print("  1. python main_fixed_complete.py")
        print("  2. python main_safe.py")
        print("  3. python launch.py")
        return True
    else:
        print("\\n⚠️ تم إصلاح معظم الأخطاء")
        print("💡 جرب: python main_fixed_complete.py")
        return False

if __name__ == "__main__":
    success = main()
    input("\\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
