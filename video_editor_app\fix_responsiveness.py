#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل عدم الاستجابة
Fix Application Responsiveness Issues
"""

import sys
import os
import subprocess
import shutil
from pathlib import Path

def backup_original_files():
    """نسخ احتياطي للملفات الأصلية"""
    print("💾 إنشاء نسخ احتياطية...")
    
    files_to_backup = [
        "main.py",
        "gui/main_window.py",
        "config.py"
    ]
    
    backup_dir = Path(__file__).parent / "backup"
    backup_dir.mkdir(exist_ok=True)
    
    for file_path in files_to_backup:
        source = Path(__file__).parent / file_path
        if source.exists():
            dest = backup_dir / f"{source.name}.backup"
            shutil.copy2(source, dest)
            print(f"  ✅ تم نسخ {file_path}")

def fix_main_window_initialization():
    """إصلاح تهيئة النافذة الرئيسية"""
    print("🖼️ إصلاح تهيئة النافذة الرئيسية...")
    
    main_window_path = Path(__file__).parent / "gui" / "main_window.py"
    
    if not main_window_path.exists():
        print("  ❌ main_window.py غير موجود")
        return False
    
    try:
        with open(main_window_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن __init__ في MainWindow
        if "def __init__(self):" in content:
            # إضافة معالجة آمنة للتهيئة
            safe_init = '''def __init__(self):
        """تهيئة النافذة الرئيسية بشكل آمن"""
        super().__init__()
        
        try:
            print("🔧 بدء تهيئة النافذة الرئيسية...")
            
            # إعداد النافذة الأساسي
            self.setWindowTitle("معالج الفيديوهات المتكامل")
            self.setGeometry(100, 100, 1200, 800)
            
            # تهيئة المتغيرات
            self.video_files = []
            self.current_video_info = {}
            self.processing_thread = None
            
            print("✅ تم إعداد المتغيرات الأساسية")
            
            # إعداد نظام السجلات
            try:
                self.logger = VideoEditorLogger("MainWindow")
                print("✅ تم إعداد نظام السجلات")
            except Exception as e:
                print(f"⚠️ خطأ في نظام السجلات: {e}")
                self.logger = None
            
            # إعداد النواة
            try:
                self.core = VideoEditorCore()
                print("✅ تم إعداد النواة")
            except Exception as e:
                print(f"⚠️ خطأ في النواة: {e}")
                self.core = None
            
            # إعداد الواجهة
            try:
                self.setup_ui()
                print("✅ تم إعداد الواجهة")
            except Exception as e:
                print(f"❌ خطأ في إعداد الواجهة: {e}")
                # إنشاء واجهة بسيطة كبديل
                self.setup_simple_ui()
            
            # ربط الإشارات
            try:
                self.connect_signals()
                print("✅ تم ربط الإشارات")
            except Exception as e:
                print(f"⚠️ خطأ في ربط الإشارات: {e}")
            
            print("🎉 تم إنجاز تهيئة النافذة الرئيسية")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة النافذة الرئيسية: {e}")
            import traceback
            traceback.print_exc()
            
            # إنشاء واجهة طوارئ
            self.setup_emergency_ui()'''
            
            # استبدال __init__ القديم
            import re
            pattern = r'def __init__\(self\):.*?(?=def|\Z)'
            content = re.sub(pattern, safe_init + '\n\n    ', content, flags=re.DOTALL)
            
            # إضافة دالة الواجهة البسيطة
            simple_ui_method = '''
    def setup_simple_ui(self):
        """إعداد واجهة بسيطة"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QPushButton
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        label = QLabel("معالج الفيديوهات المتكامل - الوضع الآمن")
        layout.addWidget(label)
        
        button = QPushButton("اختبار الاستجابة")
        button.clicked.connect(lambda: print("التطبيق يستجيب!"))
        layout.addWidget(button)
    
    def setup_emergency_ui(self):
        """إعداد واجهة الطوارئ"""
        from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        label = QLabel("خطأ في التهيئة - وضع الطوارئ")
        layout.addWidget(label)
'''
            
            content += simple_ui_method
            
            # كتابة الملف المحدث
            with open(main_window_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("  ✅ تم إصلاح تهيئة النافذة الرئيسية")
            return True
            
    except Exception as e:
        print(f"  ❌ خطأ في إصلاح main_window.py: {e}")
        return False

def create_emergency_main():
    """إنشاء main.py للطوارئ"""
    print("🚨 إنشاء main.py للطوارئ...")
    
    emergency_main = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف main.py للطوارئ - Emergency Main
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """الدالة الرئيسية للطوارئ"""
    print("🚨 تشغيل وضع الطوارئ")
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
        from PyQt6.QtCore import Qt
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("معالج الفيديوهات - وضع الطوارئ")
        
        # إعداد RTL
        try:
            app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        except:
            app.setLayoutDirection(2)
        
        # إنشاء نافذة بسيطة
        window = QMainWindow()
        window.setWindowTitle("معالج الفيديوهات - وضع الطوارئ")
        window.resize(600, 400)
        
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        label = QLabel("التطبيق يعمل في وضع الطوارئ")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
        
        button = QPushButton("اختبار الاستجابة")
        button.clicked.connect(lambda: print("✅ التطبيق يستجيب!"))
        layout.addWidget(button)
        
        window.show()
        
        print("✅ تم تشغيل وضع الطوارئ")
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في وضع الطوارئ: {e}")
        return False

if __name__ == "__main__":
    main()
'''
    
    try:
        emergency_path = Path(__file__).parent / "main_emergency.py"
        with open(emergency_path, 'w', encoding='utf-8') as f:
            f.write(emergency_main)
        
        print(f"  ✅ تم إنشاء {emergency_path}")
        return True
        
    except Exception as e:
        print(f"  ❌ فشل في إنشاء main_emergency.py: {e}")
        return False

def run_diagnostics():
    """تشغيل التشخيص"""
    print("🔍 تشغيل التشخيص...")
    
    try:
        # تشغيل سكريپت التشخيص
        result = subprocess.run([
            sys.executable, "diagnose_issue.py"
        ], capture_output=True, text=True, timeout=30)
        
        print("نتائج التشخيص:")
        print(result.stdout)
        
        if result.stderr:
            print("أخطاء:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("  ❌ انتهت مهلة التشخيص - التطبيق معلق")
        return False
    except Exception as e:
        print(f"  ❌ خطأ في التشخيص: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح"""
    print("🔧 إصلاح مشاكل عدم الاستجابة")
    print("="*40)
    
    # 1. نسخ احتياطي
    backup_original_files()
    print()
    
    # 2. تشغيل التشخيص
    diagnostic_success = run_diagnostics()
    print()
    
    # 3. إصلاح النافذة الرئيسية
    main_window_fixed = fix_main_window_initialization()
    print()
    
    # 4. إنشاء main للطوارئ
    emergency_created = create_emergency_main()
    print()
    
    # النتيجة النهائية
    print("="*40)
    print("📊 النتائج:")
    print(f"  التشخيص: {'✅ نجح' if diagnostic_success else '❌ فشل'}")
    print(f"  إصلاح النافذة: {'✅ تم' if main_window_fixed else '❌ فشل'}")
    print(f"  وضع الطوارئ: {'✅ تم إنشاؤه' if emergency_created else '❌ فشل'}")
    
    print("\n💡 طرق التشغيل:")
    print("  1. python main_fixed.py (النسخة المحسنة)")
    print("  2. python main_simple.py (النسخة البسيطة)")
    print("  3. python main_emergency.py (وضع الطوارئ)")
    print("  4. python diagnose_issue.py (التشخيص)")
    
    if diagnostic_success and main_window_fixed:
        print("\n🎉 تم إصلاح مشاكل الاستجابة!")
        return True
    else:
        print("\n⚠️ تم إنشاء بدائل للتشغيل")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
