#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح سريع لمشكلة RTL
Quick Fix for RTL Layout Direction Issue
"""

import sys
import subprocess
from pathlib import Path

def check_pyqt6_version():
    """فحص إصدار PyQt6"""
    print("🔍 فحص إصدار PyQt6...")
    
    try:
        import PyQt6
        version = PyQt6.QtCore.PYQT_VERSION_STR
        print(f"  ✅ PyQt6 الإصدار: {version}")
        return version
    except ImportError:
        print("  ❌ PyQt6 غير مثبت")
        return None
    except AttributeError:
        print("  ⚠️ لا يمكن تحديد إصدار PyQt6")
        return "unknown"

def upgrade_pyqt6():
    """تحديث PyQt6 لأحدث إصدار"""
    print("📦 تحديث PyQt6...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--upgrade", "PyQt6"
        ])
        print("  ✅ تم تحديث PyQt6 بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"  ❌ فشل في تحديث PyQt6: {e}")
        return False

def test_rtl_methods():
    """اختبار طرق RTL المختلفة"""
    print("🧪 اختبار طرق RTL...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        working_methods = []
        
        # اختبار الطرق المختلفة
        methods = [
            ("Qt.LayoutDirection.RightToLeft", lambda: Qt.LayoutDirection.RightToLeft),
            ("Qt.RightToLeft", lambda: Qt.RightToLeft),
            ("الرقم المباشر (2)", lambda: 2),
        ]
        
        for name, method in methods:
            try:
                value = method()
                app.setLayoutDirection(value)
                print(f"  ✅ {name} يعمل (القيمة: {value})")
                working_methods.append((name, value))
            except Exception as e:
                print(f"  ❌ {name} لا يعمل: {e}")
        
        return working_methods
        
    except Exception as e:
        print(f"  ❌ خطأ في اختبار RTL: {e}")
        return []

def create_rtl_fix():
    """إنشاء إصلاح RTL مخصص"""
    print("🔧 إنشاء إصلاح RTL...")
    
    fix_code = '''# -*- coding: utf-8 -*-
"""
إصلاح RTL مخصص
Custom RTL Fix
"""

def setup_rtl_safe(app):
    """إعداد RTL آمن ومتوافق مع جميع إصدارات PyQt6"""
    
    # قائمة الطرق المختلفة للوصول لـ RightToLeft
    rtl_attempts = [
        # الطريقة الحديثة (PyQt6 6.5+)
        lambda: getattr(__import__('PyQt6.QtCore', fromlist=['Qt']).Qt.LayoutDirection, 'RightToLeft', None),
        
        # الطريقة القديمة (PyQt6 < 6.5)
        lambda: getattr(__import__('PyQt6.QtCore', fromlist=['Qt']).Qt, 'RightToLeft', None),
        
        # الرقم المباشر (دائماً يعمل)
        lambda: 2,
    ]
    
    for i, attempt in enumerate(rtl_attempts):
        try:
            rtl_value = attempt()
            if rtl_value is not None:
                app.setLayoutDirection(rtl_value)
                print(f"✅ RTL تم تعيينه بالطريقة {i+1} (القيمة: {rtl_value})")
                return True
        except Exception as e:
            print(f"⚠️ الطريقة {i+1} فشلت: {e}")
            continue
    
    print("❌ فشل في تعيين RTL بجميع الطرق")
    return False

# للاستخدام في main.py
if __name__ == "__main__":
    from PyQt6.QtWidgets import QApplication
    app = QApplication([])
    setup_rtl_safe(app)
'''
    
    try:
        fix_path = Path(__file__).parent / "utils" / "rtl_fix.py"
        fix_path.parent.mkdir(exist_ok=True)
        
        with open(fix_path, 'w', encoding='utf-8') as f:
            f.write(fix_code)
        
        print(f"  ✅ تم إنشاء إصلاح RTL في: {fix_path}")
        return True
        
    except Exception as e:
        print(f"  ❌ فشل في إنشاء إصلاح RTL: {e}")
        return False

def update_main_py():
    """تحديث main.py لاستخدام الإصلاح الآمن"""
    print("📝 تحديث main.py...")
    
    try:
        main_path = Path(__file__).parent / "main.py"
        
        if not main_path.exists():
            print("  ❌ main.py غير موجود")
            return False
        
        # قراءة الملف
        with open(main_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن دالة setup_rtl_layout وتحديثها
        if "def setup_rtl_layout(app):" in content:
            # استبدال الدالة بإصدار محسن
            new_function = '''def setup_rtl_layout(app):
    """إعداد اتجاه RTL للغة العربية بشكل آمن ومتوافق"""
    
    # قائمة الطرق المختلفة مرتبة حسب الأولوية
    rtl_methods = []
    
    try:
        # الطريقة الحديثة
        rtl_methods.append((Qt.LayoutDirection.RightToLeft, "Qt.LayoutDirection.RightToLeft"))
    except AttributeError:
        pass
    
    try:
        # الطريقة القديمة
        rtl_methods.append((Qt.RightToLeft, "Qt.RightToLeft"))
    except AttributeError:
        pass
    
    # الرقم المباشر (دائماً متاح)
    rtl_methods.append((2, "الرقم المباشر (2)"))
    
    # تجربة كل طريقة
    for rtl_value, description in rtl_methods:
        try:
            app.setLayoutDirection(rtl_value)
            print(f"✅ تم تعيين اتجاه RTL باستخدام {description}")
            return True
        except Exception as e:
            print(f"⚠️ فشل {description}: {e}")
            continue
    
    print("❌ فشل في تعيين اتجاه RTL بجميع الطرق")
    return False'''
            
            # استبدال الدالة القديمة
            import re
            pattern = r'def setup_rtl_layout\(app\):.*?return False'
            content = re.sub(pattern, new_function, content, flags=re.DOTALL)
            
            # كتابة الملف المحدث
            with open(main_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("  ✅ تم تحديث main.py بنجاح")
            return True
        else:
            print("  ⚠️ لم يتم العثور على دالة setup_rtl_layout")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في تحديث main.py: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح"""
    print("🔧 إصلاح سريع لمشكلة RTL")
    print("="*40)
    
    # فحص إصدار PyQt6
    version = check_pyqt6_version()
    if version is None:
        print("\n❌ PyQt6 غير مثبت")
        print("يرجى تثبيت PyQt6: pip install PyQt6")
        return False
    
    print()
    
    # تحديث PyQt6
    if version and version != "unknown":
        upgrade_success = upgrade_pyqt6()
        print()
    
    # اختبار طرق RTL
    working_methods = test_rtl_methods()
    print()
    
    # إنشاء إصلاح مخصص
    fix_created = create_rtl_fix()
    print()
    
    # تحديث main.py
    main_updated = update_main_py()
    print()
    
    # النتيجة النهائية
    print("="*40)
    print("📊 النتائج:")
    print(f"  طرق RTL العاملة: {len(working_methods)}")
    print(f"  إصلاح مخصص: {'✅ تم إنشاؤه' if fix_created else '❌ فشل'}")
    print(f"  main.py: {'✅ تم تحديثه' if main_updated else '❌ لم يتم تحديثه'}")
    
    if working_methods:
        print("\n🎉 تم إصلاح مشكلة RTL!")
        print("يمكنك الآن تشغيل التطبيق: python main.py")
        return True
    else:
        print("\n❌ لم يتم حل مشكلة RTL")
        print("💡 جرب تحديث PyQt6: pip install --upgrade PyQt6")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
