#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة تجمد التطبيق
Debug Application Freeze Issue
"""

import sys
import signal
import threading
import time
import traceback
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class FreezeDetector:
    """كاشف التجمد"""
    
    def __init__(self, timeout=10):
        self.timeout = timeout
        self.is_frozen = False
        self.current_step = "بدء التشغيل"
        
    def start_monitoring(self):
        """بدء مراقبة التجمد"""
        def monitor():
            time.sleep(self.timeout)
            if not self.is_frozen:
                print(f"⚠️ التطبيق متجمد في: {self.current_step}")
                print("🔍 تتبع المكدس:")
                for thread_id, frame in sys._current_frames().items():
                    print(f"\nThread {thread_id}:")
                    traceback.print_stack(frame)
                
                # إنهاء قسري
                import os
                os._exit(1)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def update_step(self, step):
        """تحديث الخطوة الحالية"""
        self.current_step = step
        print(f"📍 {step}")
    
    def mark_completed(self):
        """تمييز الانتهاء"""
        self.is_frozen = True

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    detector = FreezeDetector(5)
    detector.start_monitoring()
    
    try:
        detector.update_step("استيراد Python الأساسي")
        import os, sys, json, sqlite3, threading, logging
        
        detector.update_step("استيراد PyQt6")
        from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QLabel
        from PyQt6.QtCore import Qt, QTimer
        
        detector.update_step("إنشاء QApplication")
        app = QApplication([])
        
        detector.mark_completed()
        print("✅ الاستيرادات الأساسية تعمل")
        return True, app
        
    except Exception as e:
        print(f"❌ خطأ في الاستيرادات: {e}")
        traceback.print_exc()
        return False, None

def test_project_modules():
    """اختبار وحدات المشروع مع كشف التجمد"""
    detector = FreezeDetector(3)
    detector.start_monitoring()
    
    modules = {}
    
    try:
        detector.update_step("استيراد config")
        try:
            import config
            modules['config'] = config
        except Exception as e:
            print(f"⚠️ config فشل: {e}")
            modules['config'] = None
        
        detector.update_step("استيراد utils")
        try:
            from utils import VideoEditorLogger
            modules['utils'] = True
        except Exception as e:
            print(f"⚠️ utils فشل: {e}")
            modules['utils'] = False
        
        detector.update_step("استيراد database")
        try:
            from database import DatabaseManager
            modules['database'] = DatabaseManager
        except Exception as e:
            print(f"⚠️ database فشل: {e}")
            modules['database'] = None
        
        detector.update_step("استيراد src")
        try:
            from src import VideoEditorCore
            modules['src'] = VideoEditorCore
        except Exception as e:
            print(f"⚠️ src فشل: {e}")
            modules['src'] = None
        
        detector.update_step("استيراد gui")
        try:
            from gui import MainWindow
            modules['gui'] = MainWindow
        except Exception as e:
            print(f"❌ gui فشل: {e}")
            modules['gui'] = None
        
        detector.mark_completed()
        return modules
        
    except Exception as e:
        print(f"❌ خطأ في وحدات المشروع: {e}")
        return {}

def test_main_window_creation():
    """اختبار إنشاء النافذة الرئيسية مع كشف التجمد"""
    detector = FreezeDetector(15)
    detector.start_monitoring()
    
    try:
        detector.update_step("استيراد MainWindow")
        from gui.main_window import MainWindow
        
        detector.update_step("إنشاء MainWindow")
        window = MainWindow()
        
        detector.update_step("تعيين خصائص النافذة")
        window.setWindowTitle("اختبار")
        window.resize(800, 600)
        
        detector.mark_completed()
        print("✅ تم إنشاء MainWindow بنجاح")
        return True, window
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء MainWindow: {e}")
        traceback.print_exc()
        return False, None

def find_freeze_source():
    """العثور على مصدر التجمد"""
    print("🔍 البحث عن مصدر التجمد...")
    
    # اختبار الاستيرادات
    print("\n1️⃣ اختبار الاستيرادات الأساسية:")
    basic_success, app = test_basic_imports()
    
    if not basic_success:
        print("❌ المشكلة في الاستيرادات الأساسية")
        return "basic_imports"
    
    # اختبار وحدات المشروع
    print("\n2️⃣ اختبار وحدات المشروع:")
    modules = test_project_modules()
    
    if not modules.get('gui'):
        print("❌ المشكلة في استيراد GUI")
        return "gui_import"
    
    # اختبار إنشاء النافذة
    print("\n3️⃣ اختبار إنشاء النافذة:")
    window_success, window = test_main_window_creation()
    
    if not window_success:
        print("❌ المشكلة في إنشاء النافذة الرئيسية")
        return "main_window_creation"
    
    print("✅ لم يتم العثور على مصدر التجمد في الاختبارات")
    return "unknown"

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🔍 تشخيص مشكلة تجمد التطبيق")
    print("="*40)
    
    freeze_source = find_freeze_source()
    
    print(f"\n📊 نتيجة التشخيص: {freeze_source}")
    
    solutions = {
        "basic_imports": "تثبيت PyQt6: pip install PyQt6",
        "gui_import": "إصلاح استيرادات GUI",
        "main_window_creation": "إصلاح تهيئة النافذة الرئيسية",
        "unknown": "المشكلة في مكان آخر - تحتاج تحليل أعمق"
    }
    
    print(f"💡 الحل المقترح: {solutions.get(freeze_source, 'غير معروف')}")
    
    return freeze_source

if __name__ == "__main__":
    try:
        result = main()
        input("\nاضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التشخيص")
    except Exception as e:
        print(f"\n❌ خطأ في التشخيص: {e}")
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
