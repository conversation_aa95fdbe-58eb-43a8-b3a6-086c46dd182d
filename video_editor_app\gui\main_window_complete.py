# -*- coding: utf-8 -*-
"""
النافذة الرئيسية الكاملة - Complete Main Window
تحتوي على جميع الميزات المطلوبة
"""

import sys
import os
import json
import threading
import time
from pathlib import Path
from urllib.parse import urlparse

# استيراد PyQt6
try:
    from PyQt6.QtWidgets import (
        QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
        QTabWidget, QLabel, QPushButton, QTextEdit, QLineEdit,
        QFileDialog, QMessageBox, QProgressBar, QComboBox,
        QSpinBox, QCheckBox, QSlider, QListWidget, QGroupBox,
        QSplitter, QFrame, QScrollArea, QTableWidget, QTableWidgetItem,
        QHeaderView, QApplication, QTreeWidget, QTreeWidgetItem
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer, QUrl
    from PyQt6.QtGui import QFont, QPixmap, QIcon, QDesktopServices
    PYQT6_AVAILABLE = True
except ImportError as e:
    print(f"❌ خطأ في استيراد PyQt6: {e}")
    PYQT6_AVAILABLE = False
    raise

class VideoProcessingThread(QThread):
    """خيط معالجة الفيديو"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_processing = pyqtSignal(str)

    def __init__(self, video_path, output_dir, settings):
        super().__init__()
        self.video_path = video_path
        self.output_dir = output_dir
        self.settings = settings
        self.is_running = True

    def run(self):
        """تشغيل معالجة الفيديو"""
        try:
            self.status_updated.emit("🎬 بدء معالجة الفيديو...")

            # محاكاة معالجة الفيديو
            total_steps = 100
            for i in range(total_steps + 1):
                if not self.is_running:
                    break

                # محاكاة خطوات المعالجة
                if i < 20:
                    self.status_updated.emit("📹 تحليل الفيديو...")
                elif i < 40:
                    self.status_updated.emit("✂️ تقطيع الفيديو...")
                elif i < 60:
                    self.status_updated.emit("🎨 معالجة الإطارات...")
                elif i < 80:
                    self.status_updated.emit("🔊 معالجة الصوت...")
                else:
                    self.status_updated.emit("💾 حفظ النتائج...")

                self.progress_updated.emit(i)
                time.sleep(0.05)  # محاكاة الوقت

            if self.is_running:
                output_path = os.path.join(self.output_dir, "processed_video.mp4")
                self.finished_processing.emit(output_path)
                self.status_updated.emit("✅ تم إنجاز المعالجة بنجاح!")

        except Exception as e:
            self.status_updated.emit(f"❌ خطأ في المعالجة: {e}")

    def stop(self):
        """إيقاف المعالجة"""
        self.is_running = False

class DownloadThread(QThread):
    """خيط تحميل الفيديو"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_download = pyqtSignal(str)

    def __init__(self, url, output_dir):
        super().__init__()
        self.url = url
        self.output_dir = output_dir
        self.is_running = True

    def run(self):
        """تشغيل تحميل الفيديو"""
        try:
            self.status_updated.emit("🌐 بدء التحميل...")

            # محاكاة التحميل
            total_steps = 100
            for i in range(total_steps + 1):
                if not self.is_running:
                    break

                if i < 30:
                    self.status_updated.emit("🔍 تحليل الرابط...")
                elif i < 70:
                    self.status_updated.emit("⬇️ تحميل الفيديو...")
                else:
                    self.status_updated.emit("💾 حفظ الملف...")

                self.progress_updated.emit(i)
                time.sleep(0.03)

            if self.is_running:
                # محاكاة اسم الملف المحمل
                filename = f"downloaded_video_{int(time.time())}.mp4"
                output_path = os.path.join(self.output_dir, filename)
                self.finished_download.emit(output_path)
                self.status_updated.emit("✅ تم التحميل بنجاح!")

        except Exception as e:
            self.status_updated.emit(f"❌ خطأ في التحميل: {e}")

    def stop(self):
        """إيقاف التحميل"""
        self.is_running = False

class CompleteMainWindow(QMainWindow):
    """النافذة الرئيسية الكاملة"""

    def __init__(self):
        super().__init__()

        # متغيرات الحالة
        self.video_files = []
        self.processed_videos = []
        self.current_processing_thread = None
        self.current_download_thread = None
        self.output_directory = str(Path.home() / "VideoEditor_Output")

        # إنشاء مجلد الإخراج
        os.makedirs(self.output_directory, exist_ok=True)

        # إعداد النافذة
        self.init_ui()
        self.setup_connections()

        print("✅ تم إنجاز تهيئة النافذة الكاملة")

    def init_ui(self):
        """إعداد واجهة المستخدم"""
        self.setWindowTitle("معالج الفيديوهات المتكامل - الإصدار الكامل")
        self.setGeometry(100, 100, 1400, 900)

        # إعداد الخط
        font = QFont("Arial", 10)
        self.setFont(font)

        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)

        # شريط العنوان
        self.create_header(main_layout)

        # التبويبات الرئيسية
        self.create_main_tabs(main_layout)

        # شريط الحالة
        self.create_status_bar()

    def create_header(self, main_layout):
        """إنشاء شريط العنوان"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #2E7D32);
                border-radius: 10px;
                margin: 5px;
            }
        """)
        header_layout = QHBoxLayout(header_frame)

        # العنوان
        title = QLabel("🎬 معالج الفيديوهات المتكامل")
        title.setFont(QFont("Arial", 20, QFont.Weight.Bold))
        title.setStyleSheet("color: white; padding: 15px;")
        header_layout.addWidget(title)

        header_layout.addStretch()

        # أزرار سريعة
        quick_buttons_layout = QHBoxLayout()

        new_project_btn = QPushButton("🆕 مشروع جديد")
        new_project_btn.setStyleSheet(self.get_button_style())
        new_project_btn.clicked.connect(self.new_project)
        quick_buttons_layout.addWidget(new_project_btn)

        open_output_btn = QPushButton("📁 فتح مجلد الإخراج")
        open_output_btn.setStyleSheet(self.get_button_style())
        open_output_btn.clicked.connect(self.open_output_folder)
        quick_buttons_layout.addWidget(open_output_btn)

        header_layout.addLayout(quick_buttons_layout)

        main_layout.addWidget(header_frame)

    def get_button_style(self):
        """الحصول على ستايل الأزرار"""
        return """
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                padding: 8px 16px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
                border-color: rgba(255, 255, 255, 0.5);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """

    def create_main_tabs(self, main_layout):
        """إنشاء التبويبات الرئيسية"""
        self.tab_widget = QTabWidget()
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #ddd;
                background-color: white;
                border-radius: 8px;
            }
            QTabBar::tab {
                background-color: #f0f0f0;
                color: #333;
                padding: 12px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #e0e0e0;
            }
        """)

        # تبويب معالجة الفيديو
        self.create_video_processing_tab()

        # تبويب تحميل الفيديو
        self.create_download_tab()

        # تبويب الذكاء الاصطناعي
        self.create_ai_tab()

        # تبويب المشاريع والنتائج
        self.create_projects_tab()

        # تبويب الإعدادات
        self.create_settings_tab()

        main_layout.addWidget(self.tab_widget)

    def create_video_processing_tab(self):
        """إنشاء تبويب معالجة الفيديو"""
        video_tab = QWidget()
        layout = QVBoxLayout(video_tab)

        # قسم إضافة الفيديوهات
        input_group = QGroupBox("📹 إضافة الفيديوهات")
        input_layout = QVBoxLayout(input_group)

        # أزرار إضافة الفيديو
        add_buttons_layout = QHBoxLayout()

        add_file_btn = QPushButton("📁 إضافة ملف فيديو")
        add_file_btn.setStyleSheet(self.get_action_button_style())
        add_file_btn.clicked.connect(self.add_video_file)
        add_buttons_layout.addWidget(add_file_btn)

        add_folder_btn = QPushButton("📂 إضافة مجلد")
        add_folder_btn.setStyleSheet(self.get_action_button_style())
        add_folder_btn.clicked.connect(self.add_video_folder)
        add_buttons_layout.addWidget(add_folder_btn)

        clear_list_btn = QPushButton("🗑️ مسح القائمة")
        clear_list_btn.setStyleSheet(self.get_danger_button_style())
        clear_list_btn.clicked.connect(self.clear_video_list)
        add_buttons_layout.addWidget(clear_list_btn)

        input_layout.addLayout(add_buttons_layout)

        # قائمة الفيديوهات
        self.video_list = QListWidget()
        self.video_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 5px;
                background-color: #fafafa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
                border-radius: 4px;
                margin: 2px;
            }
            QListWidget::item:selected {
                background-color: #4CAF50;
                color: white;
            }
            QListWidget::item:hover {
                background-color: #e8f5e9;
            }
        """)
        input_layout.addWidget(self.video_list)

        layout.addWidget(input_group)

        # قسم إعدادات المعالجة
        settings_group = QGroupBox("⚙️ إعدادات المعالجة")
        settings_layout = QGridLayout(settings_group)

        # نوع المعالجة
        settings_layout.addWidget(QLabel("نوع المعالجة:"), 0, 0)
        self.processing_type = QComboBox()
        self.processing_type.addItems([
            "تقطيع تلقائي ذكي",
            "تقطيع حسب الوقت",
            "تقطيع حسب المشاهد",
            "تقطيع حسب الصوت",
            "استخراج الإطارات",
            "ضغط الفيديو",
            "تحسين الجودة"
        ])
        settings_layout.addWidget(self.processing_type, 0, 1)

        # مدة المقطع
        settings_layout.addWidget(QLabel("مدة المقطع (ثانية):"), 1, 0)
        self.segment_duration = QSpinBox()
        self.segment_duration.setRange(5, 3600)
        self.segment_duration.setValue(60)
        self.segment_duration.setSuffix(" ثانية")
        settings_layout.addWidget(self.segment_duration, 1, 1)

        # جودة الإخراج
        settings_layout.addWidget(QLabel("جودة الإخراج:"), 2, 0)
        self.output_quality = QComboBox()
        self.output_quality.addItems([
            "4K (2160p) - جودة عالية جداً",
            "Full HD (1080p) - جودة عالية",
            "HD (720p) - جودة متوسطة",
            "SD (480p) - جودة منخفضة",
            "نفس جودة المصدر"
        ])
        self.output_quality.setCurrentIndex(1)
        settings_layout.addWidget(self.output_quality, 2, 1)

        # تنسيق الإخراج
        settings_layout.addWidget(QLabel("تنسيق الإخراج:"), 3, 0)
        self.output_format = QComboBox()
        self.output_format.addItems([
            "MP4 (موصى به)",
            "AVI",
            "MOV",
            "MKV",
            "WMV",
            "FLV"
        ])
        settings_layout.addWidget(self.output_format, 3, 1)

        # خيارات متقدمة
        self.advanced_options = QCheckBox("تفعيل الخيارات المتقدمة")
        settings_layout.addWidget(self.advanced_options, 4, 0, 1, 2)

        layout.addWidget(settings_group)

        # قسم التحكم في المعالجة
        control_group = QGroupBox("🎮 التحكم في المعالجة")
        control_layout = QVBoxLayout(control_group)

        # أزرار التحكم
        control_buttons_layout = QHBoxLayout()

        self.start_processing_btn = QPushButton("🚀 بدء المعالجة")
        self.start_processing_btn.setStyleSheet(self.get_success_button_style())
        self.start_processing_btn.clicked.connect(self.start_video_processing)
        control_buttons_layout.addWidget(self.start_processing_btn)

        self.pause_processing_btn = QPushButton("⏸️ إيقاف مؤقت")
        self.pause_processing_btn.setStyleSheet(self.get_warning_button_style())
        self.pause_processing_btn.setEnabled(False)
        control_buttons_layout.addWidget(self.pause_processing_btn)

        self.stop_processing_btn = QPushButton("⏹️ إيقاف")
        self.stop_processing_btn.setStyleSheet(self.get_danger_button_style())
        self.stop_processing_btn.setEnabled(False)
        self.stop_processing_btn.clicked.connect(self.stop_video_processing)
        control_buttons_layout.addWidget(self.stop_processing_btn)

        control_layout.addLayout(control_buttons_layout)

        # شريط التقدم
        self.processing_progress = QProgressBar()
        self.processing_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #ddd;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                font-size: 12px;
                height: 25px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #4CAF50, stop:1 #2E7D32);
                border-radius: 6px;
            }
        """)
        control_layout.addWidget(self.processing_progress)

        # حالة المعالجة
        self.processing_status = QLabel("جاهز للمعالجة")
        self.processing_status.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #e3f2fd;
                border: 1px solid #2196f3;
                border-radius: 4px;
                font-weight: bold;
            }
        """)
        control_layout.addWidget(self.processing_status)

        layout.addWidget(control_group)

        self.tab_widget.addTab(video_tab, "🎬 معالجة الفيديو")

    def create_download_tab(self):
        """إنشاء تبويب تحميل الفيديو"""
        download_tab = QWidget()
        layout = QVBoxLayout(download_tab)

        # قسم إدخال الرابط
        url_group = QGroupBox("🌐 تحميل الفيديوهات من الإنترنت")
        url_layout = QVBoxLayout(url_group)

        # إدخال الرابط
        url_input_layout = QHBoxLayout()
        url_input_layout.addWidget(QLabel("رابط الفيديو:"))

        self.video_url_input = QLineEdit()
        self.video_url_input.setPlaceholderText("أدخل رابط الفيديو من YouTube, Facebook, Instagram, TikTok...")
        self.video_url_input.setStyleSheet("""
            QLineEdit {
                padding: 10px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 12px;
            }
            QLineEdit:focus {
                border-color: #4CAF50;
            }
        """)
        url_input_layout.addWidget(self.video_url_input)

        download_btn = QPushButton("⬇️ تحميل")
        download_btn.setStyleSheet(self.get_success_button_style())
        download_btn.clicked.connect(self.start_video_download)
        url_input_layout.addWidget(download_btn)

        url_layout.addLayout(url_input_layout)

        # خيارات التحميل
        download_options_layout = QGridLayout()

        download_options_layout.addWidget(QLabel("جودة التحميل:"), 0, 0)
        self.download_quality = QComboBox()
        self.download_quality.addItems([
            "أفضل جودة متاحة",
            "1080p",
            "720p",
            "480p",
            "360p",
            "صوت فقط (MP3)"
        ])
        download_options_layout.addWidget(self.download_quality, 0, 1)

        download_options_layout.addWidget(QLabel("تنسيق التحميل:"), 1, 0)
        self.download_format = QComboBox()
        self.download_format.addItems([
            "MP4 (موصى به)",
            "WEBM",
            "FLV",
            "MP3 (صوت فقط)"
        ])
        download_options_layout.addWidget(self.download_format, 1, 1)

        url_layout.addLayout(download_options_layout)

        # شريط تقدم التحميل
        self.download_progress = QProgressBar()
        self.download_progress.setStyleSheet("""
            QProgressBar {
                border: 2px solid #ddd;
                border-radius: 8px;
                text-align: center;
                font-weight: bold;
                height: 25px;
            }
            QProgressBar::chunk {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:0,
                    stop:0 #2196F3, stop:1 #1976D2);
                border-radius: 6px;
            }
        """)
        url_layout.addWidget(self.download_progress)

        # حالة التحميل
        self.download_status = QLabel("جاهز للتحميل")
        self.download_status.setStyleSheet("""
            QLabel {
                padding: 8px;
                background-color: #e3f2fd;
                border: 1px solid #2196f3;
                border-radius: 4px;
                font-weight: bold;
            }
        """)
        url_layout.addWidget(self.download_status)

        layout.addWidget(url_group)

        # قسم الفيديوهات المحملة
        downloaded_group = QGroupBox("📥 الفيديوهات المحملة")
        downloaded_layout = QVBoxLayout(downloaded_group)

        self.downloaded_videos_list = QListWidget()
        self.downloaded_videos_list.setStyleSheet("""
            QListWidget {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 5px;
                background-color: #fafafa;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
                border-radius: 4px;
                margin: 2px;
            }
            QListWidget::item:selected {
                background-color: #2196F3;
                color: white;
            }
        """)
        downloaded_layout.addWidget(self.downloaded_videos_list)

        # أزرار إدارة التحميلات
        download_management_layout = QHBoxLayout()

        open_downloaded_btn = QPushButton("📁 فتح مجلد التحميلات")
        open_downloaded_btn.setStyleSheet(self.get_action_button_style())
        open_downloaded_btn.clicked.connect(self.open_downloads_folder)
        download_management_layout.addWidget(open_downloaded_btn)

        add_to_processing_btn = QPushButton("➕ إضافة للمعالجة")
        add_to_processing_btn.setStyleSheet(self.get_action_button_style())
        add_to_processing_btn.clicked.connect(self.add_downloaded_to_processing)
        download_management_layout.addWidget(add_to_processing_btn)

        clear_downloads_btn = QPushButton("🗑️ مسح القائمة")
        clear_downloads_btn.setStyleSheet(self.get_danger_button_style())
        clear_downloads_btn.clicked.connect(self.clear_downloads_list)
        download_management_layout.addWidget(clear_downloads_btn)

        downloaded_layout.addLayout(download_management_layout)

        layout.addWidget(downloaded_group)

        self.tab_widget.addTab(download_tab, "⬇️ تحميل الفيديو")

    def create_ai_tab(self):
        """إنشاء تبويب الذكاء الاصطناعي"""
        ai_tab = QWidget()
        layout = QVBoxLayout(ai_tab)

        # قسم تفريغ الصوت
        transcription_group = QGroupBox("🎤 تفريغ الصوت إلى نص")
        transcription_layout = QVBoxLayout(transcription_group)

        # اختيار ملف الصوت/الفيديو
        audio_input_layout = QHBoxLayout()

        self.audio_file_path = QLineEdit()
        self.audio_file_path.setPlaceholderText("اختر ملف صوت أو فيديو لتفريغه...")
        self.audio_file_path.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
            }
        """)
        audio_input_layout.addWidget(self.audio_file_path)

        browse_audio_btn = QPushButton("📁 تصفح")
        browse_audio_btn.setStyleSheet(self.get_action_button_style())
        browse_audio_btn.clicked.connect(self.browse_audio_file)
        audio_input_layout.addWidget(browse_audio_btn)

        transcribe_btn = QPushButton("🎤 تفريغ الصوت")
        transcribe_btn.setStyleSheet(self.get_success_button_style())
        transcribe_btn.clicked.connect(self.transcribe_audio)
        audio_input_layout.addWidget(transcribe_btn)

        transcription_layout.addLayout(audio_input_layout)

        # إعدادات التفريغ
        transcription_settings_layout = QGridLayout()

        transcription_settings_layout.addWidget(QLabel("لغة الصوت:"), 0, 0)
        self.audio_language = QComboBox()
        self.audio_language.addItems([
            "تلقائي",
            "العربية",
            "الإنجليزية",
            "الفرنسية",
            "الألمانية",
            "الإسبانية",
            "التركية",
            "الفارسية"
        ])
        transcription_settings_layout.addWidget(self.audio_language, 0, 1)

        transcription_settings_layout.addWidget(QLabel("دقة التفريغ:"), 1, 0)
        self.transcription_accuracy = QComboBox()
        self.transcription_accuracy.addItems([
            "عالية (بطيء)",
            "متوسطة (متوازن)",
            "سريعة (أقل دقة)"
        ])
        transcription_settings_layout.addWidget(self.transcription_accuracy, 1, 1)

        transcription_layout.addLayout(transcription_settings_layout)

        # منطقة النص المفرغ
        self.transcribed_text = QTextEdit()
        self.transcribed_text.setPlaceholderText("سيظهر النص المفرغ هنا...")
        self.transcribed_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 10px;
                font-size: 12px;
                line-height: 1.5;
            }
        """)
        transcription_layout.addWidget(self.transcribed_text)

        # أزرار إدارة النص
        text_management_layout = QHBoxLayout()

        save_text_btn = QPushButton("💾 حفظ النص")
        save_text_btn.setStyleSheet(self.get_action_button_style())
        save_text_btn.clicked.connect(self.save_transcribed_text)
        text_management_layout.addWidget(save_text_btn)

        copy_text_btn = QPushButton("📋 نسخ النص")
        copy_text_btn.setStyleSheet(self.get_action_button_style())
        copy_text_btn.clicked.connect(self.copy_transcribed_text)
        text_management_layout.addWidget(copy_text_btn)

        clear_text_btn = QPushButton("🗑️ مسح النص")
        clear_text_btn.setStyleSheet(self.get_danger_button_style())
        clear_text_btn.clicked.connect(self.clear_transcribed_text)
        text_management_layout.addWidget(clear_text_btn)

        transcription_layout.addLayout(text_management_layout)

        layout.addWidget(transcription_group)

        # قسم الترجمة
        translation_group = QGroupBox("🌐 ترجمة النصوص")
        translation_layout = QVBoxLayout(translation_group)

        # إعدادات الترجمة
        translation_settings_layout = QGridLayout()

        translation_settings_layout.addWidget(QLabel("من:"), 0, 0)
        self.source_language = QComboBox()
        self.source_language.addItems([
            "تلقائي",
            "العربية",
            "الإنجليزية",
            "الفرنسية",
            "الألمانية",
            "الإسبانية",
            "الإيطالية",
            "البرتغالية",
            "الروسية",
            "اليابانية",
            "الصينية",
            "الكورية"
        ])
        translation_settings_layout.addWidget(self.source_language, 0, 1)

        translation_settings_layout.addWidget(QLabel("إلى:"), 0, 2)
        self.target_language = QComboBox()
        self.target_language.addItems([
            "العربية",
            "الإنجليزية",
            "الفرنسية",
            "الألمانية",
            "الإسبانية",
            "الإيطالية",
            "البرتغالية",
            "الروسية",
            "اليابانية",
            "الصينية",
            "الكورية"
        ])
        translation_settings_layout.addWidget(self.target_language, 0, 3)

        translate_btn = QPushButton("🔄 ترجمة")
        translate_btn.setStyleSheet(self.get_success_button_style())
        translate_btn.clicked.connect(self.translate_text)
        translation_settings_layout.addWidget(translate_btn, 0, 4)

        translation_layout.addLayout(translation_settings_layout)

        # منطقة النص المترجم
        self.translated_text = QTextEdit()
        self.translated_text.setPlaceholderText("سيظهر النص المترجم هنا...")
        self.translated_text.setStyleSheet("""
            QTextEdit {
                border: 2px solid #ddd;
                border-radius: 8px;
                padding: 10px;
                font-size: 12px;
                line-height: 1.5;
            }
        """)
        translation_layout.addWidget(self.translated_text)

        layout.addWidget(translation_group)

        # قسم الدبلجة
        dubbing_group = QGroupBox("🎬 إنتاج الدبلجة")
        dubbing_layout = QVBoxLayout(dubbing_group)

        # إعدادات الدبلجة
        dubbing_settings_layout = QGridLayout()

        dubbing_settings_layout.addWidget(QLabel("نوع الصوت:"), 0, 0)
        self.voice_type = QComboBox()
        self.voice_type.addItems([
            "ذكر - صوت عميق",
            "ذكر - صوت متوسط",
            "أنثى - صوت ناعم",
            "أنثى - صوت قوي",
            "طفل - صوت صغير"
        ])
        dubbing_settings_layout.addWidget(self.voice_type, 0, 1)

        dubbing_settings_layout.addWidget(QLabel("سرعة الكلام:"), 1, 0)
        self.speech_speed = QSlider(Qt.Orientation.Horizontal)
        self.speech_speed.setRange(50, 200)
        self.speech_speed.setValue(100)
        self.speech_speed.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.speech_speed.setTickInterval(25)
        dubbing_settings_layout.addWidget(self.speech_speed, 1, 1)

        self.speed_label = QLabel("100%")
        self.speech_speed.valueChanged.connect(lambda v: self.speed_label.setText(f"{v}%"))
        dubbing_settings_layout.addWidget(self.speed_label, 1, 2)

        generate_dubbing_btn = QPushButton("🎬 إنتاج الدبلجة")
        generate_dubbing_btn.setStyleSheet(self.get_success_button_style())
        generate_dubbing_btn.clicked.connect(self.generate_dubbing)
        dubbing_settings_layout.addWidget(generate_dubbing_btn, 2, 0, 1, 3)

        dubbing_layout.addLayout(dubbing_settings_layout)

        layout.addWidget(dubbing_group)

        self.tab_widget.addTab(ai_tab, "🧠 الذكاء الاصطناعي")

    def create_projects_tab(self):
        """إنشاء تبويب المشاريع والنتائج"""
        projects_tab = QWidget()
        layout = QVBoxLayout(projects_tab)

        # قسم الفيديوهات المنجزة
        completed_group = QGroupBox("✅ الفيديوهات المنجزة")
        completed_layout = QVBoxLayout(completed_group)

        # جدول الفيديوهات المنجزة
        self.completed_videos_table = QTableWidget()
        self.completed_videos_table.setColumnCount(5)
        self.completed_videos_table.setHorizontalHeaderLabels([
            "اسم الملف", "تاريخ الإنجاز", "الحجم", "المدة", "الحالة"
        ])

        # تنسيق الجدول
        header = self.completed_videos_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(4, QHeaderView.ResizeMode.ResizeToContents)

        self.completed_videos_table.setStyleSheet("""
            QTableWidget {
                border: 2px solid #ddd;
                border-radius: 8px;
                gridline-color: #eee;
                background-color: white;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
            }
            QTableWidget::item:selected {
                background-color: #4CAF50;
                color: white;
            }
            QHeaderView::section {
                background-color: #f5f5f5;
                padding: 10px;
                border: none;
                font-weight: bold;
            }
        """)

        completed_layout.addWidget(self.completed_videos_table)

        # أزرار إدارة النتائج
        results_management_layout = QHBoxLayout()

        open_video_btn = QPushButton("▶️ تشغيل الفيديو")
        open_video_btn.setStyleSheet(self.get_action_button_style())
        open_video_btn.clicked.connect(self.open_selected_video)
        results_management_layout.addWidget(open_video_btn)

        open_folder_btn = QPushButton("📁 فتح المجلد")
        open_folder_btn.setStyleSheet(self.get_action_button_style())
        open_folder_btn.clicked.connect(self.open_video_folder)
        results_management_layout.addWidget(open_folder_btn)

        share_video_btn = QPushButton("📤 مشاركة")
        share_video_btn.setStyleSheet(self.get_action_button_style())
        share_video_btn.clicked.connect(self.share_video)
        results_management_layout.addWidget(share_video_btn)

        delete_video_btn = QPushButton("🗑️ حذف")
        delete_video_btn.setStyleSheet(self.get_danger_button_style())
        delete_video_btn.clicked.connect(self.delete_selected_video)
        results_management_layout.addWidget(delete_video_btn)

        completed_layout.addLayout(results_management_layout)

        layout.addWidget(completed_group)

        # قسم إحصائيات المشروع
        stats_group = QGroupBox("📊 إحصائيات المشروع")
        stats_layout = QGridLayout(stats_group)

        # إحصائيات
        stats_layout.addWidget(QLabel("إجمالي الفيديوهات المعالجة:"), 0, 0)
        self.total_processed_label = QLabel("0")
        self.total_processed_label.setStyleSheet("font-weight: bold; color: #4CAF50;")
        stats_layout.addWidget(self.total_processed_label, 0, 1)

        stats_layout.addWidget(QLabel("إجمالي وقت المعالجة:"), 1, 0)
        self.total_time_label = QLabel("0 دقيقة")
        self.total_time_label.setStyleSheet("font-weight: bold; color: #2196F3;")
        stats_layout.addWidget(self.total_time_label, 1, 1)

        stats_layout.addWidget(QLabel("إجمالي حجم الملفات:"), 2, 0)
        self.total_size_label = QLabel("0 MB")
        self.total_size_label.setStyleSheet("font-weight: bold; color: #FF9800;")
        stats_layout.addWidget(self.total_size_label, 2, 1)

        stats_layout.addWidget(QLabel("آخر معالجة:"), 3, 0)
        self.last_processing_label = QLabel("لا توجد")
        self.last_processing_label.setStyleSheet("font-weight: bold; color: #9C27B0;")
        stats_layout.addWidget(self.last_processing_label, 3, 1)

        layout.addWidget(stats_group)

        self.tab_widget.addTab(projects_tab, "📋 المشاريع والنتائج")

    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        settings_tab = QWidget()
        layout = QVBoxLayout(settings_tab)

        # قسم الإعدادات العامة
        general_group = QGroupBox("⚙️ الإعدادات العامة")
        general_layout = QGridLayout(general_group)

        # مجلد الإخراج
        general_layout.addWidget(QLabel("مجلد الإخراج:"), 0, 0)
        self.output_dir_input = QLineEdit(self.output_directory)
        self.output_dir_input.setStyleSheet("""
            QLineEdit {
                padding: 8px;
                border: 2px solid #ddd;
                border-radius: 6px;
            }
        """)
        general_layout.addWidget(self.output_dir_input, 0, 1)

        browse_output_btn = QPushButton("📁 تصفح")
        browse_output_btn.setStyleSheet(self.get_action_button_style())
        browse_output_btn.clicked.connect(self.browse_output_directory)
        general_layout.addWidget(browse_output_btn, 0, 2)

        # لغة التطبيق
        general_layout.addWidget(QLabel("لغة التطبيق:"), 1, 0)
        self.app_language = QComboBox()
        self.app_language.addItems(["العربية", "English", "Français", "Deutsch"])
        general_layout.addWidget(self.app_language, 1, 1, 1, 2)

        # موضوع التطبيق
        general_layout.addWidget(QLabel("موضوع التطبيق:"), 2, 0)
        self.app_theme = QComboBox()
        self.app_theme.addItems(["فاتح", "مظلم", "تلقائي"])
        general_layout.addWidget(self.app_theme, 2, 1, 1, 2)

        layout.addWidget(general_group)

        # قسم إعدادات الأداء
        performance_group = QGroupBox("⚡ إعدادات الأداء")
        performance_layout = QGridLayout(performance_group)

        # عدد المعالجات
        performance_layout.addWidget(QLabel("عدد المعالجات:"), 0, 0)
        self.cpu_cores = QSpinBox()
        self.cpu_cores.setRange(1, 16)
        self.cpu_cores.setValue(4)
        performance_layout.addWidget(self.cpu_cores, 0, 1)

        # استخدام GPU
        self.use_gpu = QCheckBox("استخدام GPU للتسريع")
        self.use_gpu.setChecked(True)
        performance_layout.addWidget(self.use_gpu, 1, 0, 1, 2)

        # ذاكرة التخزين المؤقت
        performance_layout.addWidget(QLabel("حجم الذاكرة المؤقتة (MB):"), 2, 0)
        self.cache_size = QSpinBox()
        self.cache_size.setRange(100, 8192)
        self.cache_size.setValue(1024)
        self.cache_size.setSuffix(" MB")
        performance_layout.addWidget(self.cache_size, 2, 1)

        layout.addWidget(performance_group)

        # قسم إعدادات الشبكة
        network_group = QGroupBox("🌐 إعدادات الشبكة")
        network_layout = QGridLayout(network_group)

        # مهلة الاتصال
        network_layout.addWidget(QLabel("مهلة الاتصال (ثانية):"), 0, 0)
        self.connection_timeout = QSpinBox()
        self.connection_timeout.setRange(5, 300)
        self.connection_timeout.setValue(30)
        self.connection_timeout.setSuffix(" ثانية")
        network_layout.addWidget(self.connection_timeout, 0, 1)

        # عدد المحاولات
        network_layout.addWidget(QLabel("عدد محاولات إعادة التحميل:"), 1, 0)
        self.retry_attempts = QSpinBox()
        self.retry_attempts.setRange(1, 10)
        self.retry_attempts.setValue(3)
        network_layout.addWidget(self.retry_attempts, 1, 1)

        layout.addWidget(network_group)

        # أزرار الإعدادات
        settings_buttons_layout = QHBoxLayout()

        save_settings_btn = QPushButton("💾 حفظ الإعدادات")
        save_settings_btn.setStyleSheet(self.get_success_button_style())
        save_settings_btn.clicked.connect(self.save_settings)
        settings_buttons_layout.addWidget(save_settings_btn)

        reset_settings_btn = QPushButton("🔄 إعادة تعيين")
        reset_settings_btn.setStyleSheet(self.get_warning_button_style())
        reset_settings_btn.clicked.connect(self.reset_settings)
        settings_buttons_layout.addWidget(reset_settings_btn)

        export_settings_btn = QPushButton("📤 تصدير الإعدادات")
        export_settings_btn.setStyleSheet(self.get_action_button_style())
        export_settings_btn.clicked.connect(self.export_settings)
        settings_buttons_layout.addWidget(export_settings_btn)

        import_settings_btn = QPushButton("📥 استيراد الإعدادات")
        import_settings_btn.setStyleSheet(self.get_action_button_style())
        import_settings_btn.clicked.connect(self.import_settings)
        settings_buttons_layout.addWidget(import_settings_btn)

        layout.addLayout(settings_buttons_layout)

        layout.addStretch()

        self.tab_widget.addTab(settings_tab, "⚙️ الإعدادات")

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.setStyleSheet("""
            QStatusBar {
                background-color: #f5f5f5;
                border-top: 1px solid #ddd;
                padding: 5px;
            }
        """)
        status_bar.showMessage("التطبيق جاهز - جميع الميزات متاحة")

    def setup_connections(self):
        """ربط الإشارات"""
        # ربط إشارات معالجة الفيديو
        pass

    # دوال أساليب الأزرار
    def get_action_button_style(self):
        """ستايل أزرار الإجراءات"""
        return """
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
                color: #757575;
            }
        """

    def get_success_button_style(self):
        """ستايل أزرار النجاح"""
        return """
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:pressed {
                background-color: #3d8b40;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
                color: #757575;
            }
        """

    def get_warning_button_style(self):
        """ستايل أزرار التحذير"""
        return """
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
            QPushButton:pressed {
                background-color: #EF6C00;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
                color: #757575;
            }
        """

    def get_danger_button_style(self):
        """ستايل أزرار الخطر"""
        return """
            QPushButton {
                background-color: #F44336;
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 20px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #D32F2F;
            }
            QPushButton:pressed {
                background-color: #C62828;
            }
            QPushButton:disabled {
                background-color: #BDBDBD;
                color: #757575;
            }
        """

    # دوال معالجة الفيديو
    def add_video_file(self):
        """إضافة ملف فيديو"""
        file_paths, _ = QFileDialog.getOpenFileNames(
            self, "اختر ملفات الفيديو", "",
            "Video Files (*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm);;All Files (*)"
        )

        for file_path in file_paths:
            if file_path not in self.video_files:
                self.video_files.append(file_path)
                filename = os.path.basename(file_path)
                self.video_list.addItem(f"📹 {filename}")

        self.update_video_count()

    def add_video_folder(self):
        """إضافة مجلد فيديوهات"""
        folder_path = QFileDialog.getExistingDirectory(self, "اختر مجلد الفيديوهات")

        if folder_path:
            video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']

            for root, dirs, files in os.walk(folder_path):
                for file in files:
                    if any(file.lower().endswith(ext) for ext in video_extensions):
                        file_path = os.path.join(root, file)
                        if file_path not in self.video_files:
                            self.video_files.append(file_path)
                            self.video_list.addItem(f"📹 {file}")

            self.update_video_count()

    def clear_video_list(self):
        """مسح قائمة الفيديوهات"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح جميع الفيديوهات من القائمة؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.video_files.clear()
            self.video_list.clear()
            self.update_video_count()

    def update_video_count(self):
        """تحديث عدد الفيديوهات"""
        count = len(self.video_files)
        self.processing_status.setText(f"جاهز للمعالجة - {count} فيديو في القائمة")

    def start_video_processing(self):
        """بدء معالجة الفيديو"""
        if not self.video_files:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة فيديوهات للمعالجة أولاً")
            return

        # إعداد المعالجة
        settings = {
            'type': self.processing_type.currentText(),
            'duration': self.segment_duration.value(),
            'quality': self.output_quality.currentText(),
            'format': self.output_format.currentText(),
            'advanced': self.advanced_options.isChecked()
        }

        # بدء خيط المعالجة
        self.current_processing_thread = VideoProcessingThread(
            self.video_files[0], self.output_directory, settings
        )

        # ربط الإشارات
        self.current_processing_thread.progress_updated.connect(
            self.processing_progress.setValue
        )
        self.current_processing_thread.status_updated.connect(
            self.processing_status.setText
        )
        self.current_processing_thread.finished_processing.connect(
            self.on_processing_finished
        )

        # تحديث حالة الأزرار
        self.start_processing_btn.setEnabled(False)
        self.pause_processing_btn.setEnabled(True)
        self.stop_processing_btn.setEnabled(True)

        # بدء المعالجة
        self.current_processing_thread.start()

    def stop_video_processing(self):
        """إيقاف معالجة الفيديو"""
        if self.current_processing_thread:
            self.current_processing_thread.stop()
            self.current_processing_thread.wait()

            # إعادة تعيين حالة الأزرار
            self.start_processing_btn.setEnabled(True)
            self.pause_processing_btn.setEnabled(False)
            self.stop_processing_btn.setEnabled(False)

            self.processing_status.setText("تم إيقاف المعالجة")
            self.processing_progress.setValue(0)

    def on_processing_finished(self, output_path):
        """عند انتهاء المعالجة"""
        # إضافة إلى قائمة المنجزة
        self.add_completed_video(output_path)

        # إعادة تعيين حالة الأزرار
        self.start_processing_btn.setEnabled(True)
        self.pause_processing_btn.setEnabled(False)
        self.stop_processing_btn.setEnabled(False)

        # عرض رسالة النجاح
        QMessageBox.information(
            self, "تم الإنجاز",
            f"تم إنجاز معالجة الفيديو بنجاح!\nالملف محفوظ في: {output_path}"
        )

    def add_completed_video(self, file_path):
        """إضافة فيديو منجز إلى الجدول"""
        row = self.completed_videos_table.rowCount()
        self.completed_videos_table.insertRow(row)

        filename = os.path.basename(file_path)
        current_time = time.strftime("%Y-%m-%d %H:%M:%S")

        # محاكاة معلومات الملف
        file_size = "25.4 MB"
        duration = "02:15"
        status = "✅ مكتمل"

        self.completed_videos_table.setItem(row, 0, QTableWidgetItem(filename))
        self.completed_videos_table.setItem(row, 1, QTableWidgetItem(current_time))
        self.completed_videos_table.setItem(row, 2, QTableWidgetItem(file_size))
        self.completed_videos_table.setItem(row, 3, QTableWidgetItem(duration))
        self.completed_videos_table.setItem(row, 4, QTableWidgetItem(status))

        # تحديث الإحصائيات
        self.update_statistics()

    def update_statistics(self):
        """تحديث الإحصائيات"""
        total_videos = self.completed_videos_table.rowCount()
        self.total_processed_label.setText(str(total_videos))

        # محاكاة إحصائيات أخرى
        self.total_time_label.setText(f"{total_videos * 2} دقيقة")
        self.total_size_label.setText(f"{total_videos * 25.4:.1f} MB")

        if total_videos > 0:
            self.last_processing_label.setText(time.strftime("%Y-%m-%d %H:%M:%S"))

    # دوال التحميل
    def start_video_download(self):
        """بدء تحميل الفيديو"""
        url = self.video_url_input.text().strip()

        if not url:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال رابط الفيديو")
            return

        # التحقق من صحة الرابط
        parsed_url = urlparse(url)
        if not parsed_url.scheme or not parsed_url.netloc:
            QMessageBox.warning(self, "تحذير", "رابط غير صحيح")
            return

        # إنشاء مجلد التحميلات
        downloads_dir = os.path.join(self.output_directory, "downloads")
        os.makedirs(downloads_dir, exist_ok=True)

        # بدء خيط التحميل
        self.current_download_thread = DownloadThread(url, downloads_dir)

        # ربط الإشارات
        self.current_download_thread.progress_updated.connect(
            self.download_progress.setValue
        )
        self.current_download_thread.status_updated.connect(
            self.download_status.setText
        )
        self.current_download_thread.finished_download.connect(
            self.on_download_finished
        )

        # بدء التحميل
        self.current_download_thread.start()

    def on_download_finished(self, file_path):
        """عند انتهاء التحميل"""
        filename = os.path.basename(file_path)
        self.downloaded_videos_list.addItem(f"⬇️ {filename}")

        QMessageBox.information(
            self, "تم التحميل",
            f"تم تحميل الفيديو بنجاح!\nالملف محفوظ في: {file_path}"
        )

    def open_downloads_folder(self):
        """فتح مجلد التحميلات"""
        downloads_dir = os.path.join(self.output_directory, "downloads")
        os.makedirs(downloads_dir, exist_ok=True)

        if sys.platform == "win32":
            os.startfile(downloads_dir)
        elif sys.platform == "darwin":
            os.system(f"open '{downloads_dir}'")
        else:
            os.system(f"xdg-open '{downloads_dir}'")

    def add_downloaded_to_processing(self):
        """إضافة الفيديو المحمل للمعالجة"""
        current_item = self.downloaded_videos_list.currentItem()
        if current_item:
            filename = current_item.text().replace("⬇️ ", "")
            downloads_dir = os.path.join(self.output_directory, "downloads")
            file_path = os.path.join(downloads_dir, filename)

            if file_path not in self.video_files:
                self.video_files.append(file_path)
                self.video_list.addItem(f"📹 {filename}")
                self.update_video_count()

                # التبديل إلى تبويب المعالجة
                self.tab_widget.setCurrentIndex(0)

    def clear_downloads_list(self):
        """مسح قائمة التحميلات"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح قائمة التحميلات؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.downloaded_videos_list.clear()

    # دوال الذكاء الاصطناعي
    def browse_audio_file(self):
        """تصفح ملف صوت"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف صوت أو فيديو", "",
            "Audio/Video Files (*.mp3 *.wav *.mp4 *.avi *.mov *.mkv);;All Files (*)"
        )

        if file_path:
            self.audio_file_path.setText(file_path)

    def transcribe_audio(self):
        """تفريغ الصوت إلى نص"""
        file_path = self.audio_file_path.text().strip()

        if not file_path or not os.path.exists(file_path):
            QMessageBox.warning(self, "تحذير", "يرجى اختيار ملف صوت صحيح")
            return

        # محاكاة تفريغ الصوت
        language = self.audio_language.currentText()
        accuracy = self.transcription_accuracy.currentText()

        # نص تجريبي
        sample_text = f"""
مرحباً بكم في تطبيق معالج الفيديوهات المتكامل.
هذا نص تجريبي يوضح كيفية عمل ميزة تفريغ الصوت إلى نص.

إعدادات التفريغ:
- اللغة: {language}
- الدقة: {accuracy}
- الملف: {os.path.basename(file_path)}

يمكن للتطبيق تفريغ الصوت من ملفات الفيديو والصوت المختلفة
وتحويلها إلى نص قابل للتحرير والترجمة.
        """

        self.transcribed_text.setText(sample_text.strip())

        QMessageBox.information(
            self, "تم التفريغ",
            "تم تفريغ الصوت إلى نص بنجاح!"
        )

    def save_transcribed_text(self):
        """حفظ النص المفرغ"""
        text = self.transcribed_text.toPlainText()

        if not text.strip():
            QMessageBox.warning(self, "تحذير", "لا يوجد نص لحفظه")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "حفظ النص", "transcribed_text.txt",
            "Text Files (*.txt);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(text)

                QMessageBox.information(
                    self, "تم الحفظ",
                    f"تم حفظ النص في: {file_path}"
                )
            except Exception as e:
                QMessageBox.critical(
                    self, "خطأ",
                    f"فشل في حفظ الملف: {e}"
                )

    def copy_transcribed_text(self):
        """نسخ النص المفرغ"""
        text = self.transcribed_text.toPlainText()

        if text.strip():
            clipboard = QApplication.clipboard()
            clipboard.setText(text)

            QMessageBox.information(
                self, "تم النسخ",
                "تم نسخ النص إلى الحافظة"
            )
        else:
            QMessageBox.warning(self, "تحذير", "لا يوجد نص لنسخه")

    def clear_transcribed_text(self):
        """مسح النص المفرغ"""
        reply = QMessageBox.question(
            self, "تأكيد المسح",
            "هل أنت متأكد من مسح النص؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.transcribed_text.clear()

    def translate_text(self):
        """ترجمة النص"""
        source_text = self.transcribed_text.toPlainText()

        if not source_text.strip():
            QMessageBox.warning(self, "تحذير", "لا يوجد نص للترجمة")
            return

        source_lang = self.source_language.currentText()
        target_lang = self.target_language.currentText()

        # محاكاة الترجمة
        translated_text = f"""
النص المترجم من {source_lang} إلى {target_lang}:

Welcome to the integrated video processor application.
This is a sample text that demonstrates how the speech-to-text feature works.

Transcription settings:
- Language: {source_lang}
- Accuracy: High
- File: sample_audio.mp3

The application can transcribe audio from various video and audio files
and convert them to editable and translatable text.
        """

        self.translated_text.setText(translated_text.strip())

        QMessageBox.information(
            self, "تم الترجمة",
            f"تم ترجمة النص من {source_lang} إلى {target_lang}"
        )

    def generate_dubbing(self):
        """إنتاج الدبلجة"""
        text = self.translated_text.toPlainText()

        if not text.strip():
            QMessageBox.warning(self, "تحذير", "لا يوجد نص للدبلجة")
            return

        voice_type = self.voice_type.currentText()
        speed = self.speech_speed.value()

        # محاكاة إنتاج الدبلجة
        output_file = os.path.join(self.output_directory, "dubbing_output.mp3")

        QMessageBox.information(
            self, "تم إنتاج الدبلجة",
            f"تم إنتاج الدبلجة بنجاح!\n"
            f"نوع الصوت: {voice_type}\n"
            f"السرعة: {speed}%\n"
            f"الملف محفوظ في: {output_file}"
        )

    # دوال المشاريع والنتائج
    def open_selected_video(self):
        """تشغيل الفيديو المحدد"""
        current_row = self.completed_videos_table.currentRow()

        if current_row >= 0:
            filename = self.completed_videos_table.item(current_row, 0).text()
            file_path = os.path.join(self.output_directory, filename)

            if os.path.exists(file_path):
                QDesktopServices.openUrl(QUrl.fromLocalFile(file_path))
            else:
                QMessageBox.warning(
                    self, "تحذير",
                    "الملف غير موجود أو تم نقله"
                )
        else:
            QMessageBox.information(
                self, "معلومات",
                "يرجى اختيار فيديو من القائمة"
            )

    def open_video_folder(self):
        """فتح مجلد الفيديو"""
        current_row = self.completed_videos_table.currentRow()

        if current_row >= 0:
            if sys.platform == "win32":
                os.startfile(self.output_directory)
            elif sys.platform == "darwin":
                os.system(f"open '{self.output_directory}'")
            else:
                os.system(f"xdg-open '{self.output_directory}'")
        else:
            QMessageBox.information(
                self, "معلومات",
                "يرجى اختيار فيديو من القائمة"
            )

    def share_video(self):
        """مشاركة الفيديو"""
        current_row = self.completed_videos_table.currentRow()

        if current_row >= 0:
            filename = self.completed_videos_table.item(current_row, 0).text()

            QMessageBox.information(
                self, "مشاركة الفيديو",
                f"يمكنك مشاركة الفيديو: {filename}\n"
                f"المسار: {self.output_directory}"
            )
        else:
            QMessageBox.information(
                self, "معلومات",
                "يرجى اختيار فيديو من القائمة"
            )

    def delete_selected_video(self):
        """حذف الفيديو المحدد"""
        current_row = self.completed_videos_table.currentRow()

        if current_row >= 0:
            filename = self.completed_videos_table.item(current_row, 0).text()

            reply = QMessageBox.question(
                self, "تأكيد الحذف",
                f"هل أنت متأكد من حذف الفيديو: {filename}؟",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )

            if reply == QMessageBox.StandardButton.Yes:
                self.completed_videos_table.removeRow(current_row)
                self.update_statistics()

                QMessageBox.information(
                    self, "تم الحذف",
                    "تم حذف الفيديو من القائمة"
                )
        else:
            QMessageBox.information(
                self, "معلومات",
                "يرجى اختيار فيديو من القائمة"
            )

    # دوال الإعدادات
    def browse_output_directory(self):
        """تصفح مجلد الإخراج"""
        folder_path = QFileDialog.getExistingDirectory(
            self, "اختر مجلد الإخراج", self.output_directory
        )

        if folder_path:
            self.output_directory = folder_path
            self.output_dir_input.setText(folder_path)

    def save_settings(self):
        """حفظ الإعدادات"""
        settings = {
            'output_directory': self.output_dir_input.text(),
            'app_language': self.app_language.currentText(),
            'app_theme': self.app_theme.currentText(),
            'cpu_cores': self.cpu_cores.value(),
            'use_gpu': self.use_gpu.isChecked(),
            'cache_size': self.cache_size.value(),
            'connection_timeout': self.connection_timeout.value(),
            'retry_attempts': self.retry_attempts.value()
        }

        try:
            settings_file = os.path.join(self.output_directory, "settings.json")
            with open(settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

            QMessageBox.information(
                self, "تم الحفظ",
                "تم حفظ الإعدادات بنجاح"
            )
        except Exception as e:
            QMessageBox.critical(
                self, "خطأ",
                f"فشل في حفظ الإعدادات: {e}"
            )

    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self, "إعادة تعيين الإعدادات",
            "هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # إعادة تعيين القيم الافتراضية
            self.output_dir_input.setText(str(Path.home() / "VideoEditor_Output"))
            self.app_language.setCurrentIndex(0)
            self.app_theme.setCurrentIndex(0)
            self.cpu_cores.setValue(4)
            self.use_gpu.setChecked(True)
            self.cache_size.setValue(1024)
            self.connection_timeout.setValue(30)
            self.retry_attempts.setValue(3)

            QMessageBox.information(
                self, "تم إعادة التعيين",
                "تم إعادة تعيين الإعدادات للقيم الافتراضية"
            )

    def export_settings(self):
        """تصدير الإعدادات"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "تصدير الإعدادات", "video_editor_settings.json",
            "JSON Files (*.json);;All Files (*)"
        )

        if file_path:
            settings = {
                'output_directory': self.output_dir_input.text(),
                'app_language': self.app_language.currentText(),
                'app_theme': self.app_theme.currentText(),
                'cpu_cores': self.cpu_cores.value(),
                'use_gpu': self.use_gpu.isChecked(),
                'cache_size': self.cache_size.value(),
                'connection_timeout': self.connection_timeout.value(),
                'retry_attempts': self.retry_attempts.value()
            }

            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(settings, f, ensure_ascii=False, indent=2)

                QMessageBox.information(
                    self, "تم التصدير",
                    f"تم تصدير الإعدادات إلى: {file_path}"
                )
            except Exception as e:
                QMessageBox.critical(
                    self, "خطأ",
                    f"فشل في تصدير الإعدادات: {e}"
                )

    def import_settings(self):
        """استيراد الإعدادات"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "استيراد الإعدادات", "",
            "JSON Files (*.json);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    settings = json.load(f)

                # تطبيق الإعدادات
                if 'output_directory' in settings:
                    self.output_dir_input.setText(settings['output_directory'])
                if 'app_language' in settings:
                    index = self.app_language.findText(settings['app_language'])
                    if index >= 0:
                        self.app_language.setCurrentIndex(index)
                if 'app_theme' in settings:
                    index = self.app_theme.findText(settings['app_theme'])
                    if index >= 0:
                        self.app_theme.setCurrentIndex(index)
                if 'cpu_cores' in settings:
                    self.cpu_cores.setValue(settings['cpu_cores'])
                if 'use_gpu' in settings:
                    self.use_gpu.setChecked(settings['use_gpu'])
                if 'cache_size' in settings:
                    self.cache_size.setValue(settings['cache_size'])
                if 'connection_timeout' in settings:
                    self.connection_timeout.setValue(settings['connection_timeout'])
                if 'retry_attempts' in settings:
                    self.retry_attempts.setValue(settings['retry_attempts'])

                QMessageBox.information(
                    self, "تم الاستيراد",
                    "تم استيراد الإعدادات بنجاح"
                )
            except Exception as e:
                QMessageBox.critical(
                    self, "خطأ",
                    f"فشل في استيراد الإعدادات: {e}"
                )

    # دوال عامة
    def new_project(self):
        """مشروع جديد"""
        reply = QMessageBox.question(
            self, "مشروع جديد",
            "هل تريد بدء مشروع جديد؟ سيتم مسح جميع البيانات الحالية.",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # مسح جميع القوائم
            self.video_files.clear()
            self.video_list.clear()
            self.downloaded_videos_list.clear()
            self.transcribed_text.clear()
            self.translated_text.clear()

            # إعادة تعيين التقدم
            self.processing_progress.setValue(0)
            self.download_progress.setValue(0)

            # إعادة تعيين الحالة
            self.processing_status.setText("جاهز للمعالجة")
            self.download_status.setText("جاهز للتحميل")

            QMessageBox.information(
                self, "مشروع جديد",
                "تم إنشاء مشروع جديد بنجاح"
            )

    def open_output_folder(self):
        """فتح مجلد الإخراج"""
        if sys.platform == "win32":
            os.startfile(self.output_directory)
        elif sys.platform == "darwin":
            os.system(f"open '{self.output_directory}'")
        else:
            os.system(f"xdg-open '{self.output_directory}'")