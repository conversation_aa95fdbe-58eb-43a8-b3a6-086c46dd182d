#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف main.py للطوارئ - Emergency Main
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    """الدالة الرئيسية للطوارئ"""
    print("🚨 تشغيل وضع الطوارئ")
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
        from PyQt6.QtCore import Qt
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("معالج الفيديوهات - وضع الطوارئ")
        
        # إعداد RTL
        try:
            app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        except:
            app.setLayoutDirection(2)
        
        # إنشاء نافذة بسيطة
        window = QMainWindow()
        window.setWindowTitle("معالج الفيديوهات - وضع الطوارئ")
        window.resize(600, 400)
        
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        label = QLabel("التطبيق يعمل في وضع الطوارئ")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
        
        button = QPushButton("اختبار الاستجابة")
        button.clicked.connect(lambda: print("✅ التطبيق يستجيب!"))
        layout.addWidget(button)
        
        window.show()
        
        print("✅ تم تشغيل وضع الطوارئ")
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ خطأ في وضع الطوارئ: {e}")
        return False

if __name__ == "__main__":
    main()
