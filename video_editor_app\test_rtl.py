#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إعداد اتجاه RTL
Test RTL Layout Direction Setup
"""

import sys
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_pyqt6_import():
    """اختبار استيراد PyQt6"""
    print("🔍 اختبار استيراد PyQt6...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        print("  ✅ PyQt6 متاح")
        return True
    except ImportError as e:
        print(f"  ❌ PyQt6 غير متاح: {e}")
        return False

def test_qt_constants():
    """اختبار ثوابت Qt"""
    print("🔍 اختبار ثوابت Qt...")
    
    try:
        from PyQt6.QtCore import Qt
        
        # اختبار الطرق المختلفة للوصول لـ RightToLeft
        methods = []
        
        # الطريقة الحديثة
        try:
            rtl = Qt.LayoutDirection.RightToLeft
            methods.append(("Qt.LayoutDirection.RightToLeft", rtl))
            print(f"  ✅ Qt.LayoutDirection.RightToLeft = {rtl}")
        except AttributeError:
            print("  ❌ Qt.LayoutDirection.RightToLeft غير متاح")
        
        # الطريقة القديمة
        try:
            rtl = Qt.RightToLeft
            methods.append(("Qt.RightToLeft", rtl))
            print(f"  ✅ Qt.RightToLeft = {rtl}")
        except AttributeError:
            print("  ❌ Qt.RightToLeft غير متاح")
        
        # الرقم المباشر
        try:
            rtl = 2  # Qt.RightToLeft عادة = 2
            methods.append(("الرقم المباشر", rtl))
            print(f"  ✅ الرقم المباشر = {rtl}")
        except:
            print("  ❌ الرقم المباشر غير متاح")
        
        return methods
        
    except ImportError:
        print("  ❌ لا يمكن استيراد Qt")
        return []

def test_rtl_setup():
    """اختبار إعداد RTL"""
    print("🔍 اختبار إعداد RTL...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        # إنشاء تطبيق للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # اختبار الطرق المختلفة
        success_methods = []
        
        # الطريقة الحديثة
        try:
            app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            current = app.layoutDirection()
            print(f"  ✅ Qt.LayoutDirection.RightToLeft نجح - الاتجاه الحالي: {current}")
            success_methods.append("Qt.LayoutDirection.RightToLeft")
        except Exception as e:
            print(f"  ❌ Qt.LayoutDirection.RightToLeft فشل: {e}")
        
        # الطريقة القديمة
        try:
            app.setLayoutDirection(Qt.RightToLeft)
            current = app.layoutDirection()
            print(f"  ✅ Qt.RightToLeft نجح - الاتجاه الحالي: {current}")
            success_methods.append("Qt.RightToLeft")
        except Exception as e:
            print(f"  ❌ Qt.RightToLeft فشل: {e}")
        
        # الرقم المباشر
        try:
            app.setLayoutDirection(2)
            current = app.layoutDirection()
            print(f"  ✅ الرقم المباشر (2) نجح - الاتجاه الحالي: {current}")
            success_methods.append("الرقم المباشر")
        except Exception as e:
            print(f"  ❌ الرقم المباشر فشل: {e}")
        
        return success_methods
        
    except Exception as e:
        print(f"  ❌ خطأ في اختبار RTL: {e}")
        return []

def test_main_application():
    """اختبار التطبيق الرئيسي"""
    print("🔍 اختبار التطبيق الرئيسي...")
    
    try:
        # استيراد الدالة من main.py
        from main import setup_rtl_layout
        
        from PyQt6.QtWidgets import QApplication
        
        # إنشاء تطبيق للاختبار
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # اختبار دالة الإعداد
        result = setup_rtl_layout(app)
        
        if result:
            current = app.layoutDirection()
            print(f"  ✅ setup_rtl_layout نجح - الاتجاه الحالي: {current}")
            return True
        else:
            print("  ❌ setup_rtl_layout فشل")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في اختبار التطبيق الرئيسي: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار إعداد اتجاه RTL")
    print("="*50)
    
    # اختبار استيراد PyQt6
    if not test_pyqt6_import():
        print("\n❌ PyQt6 غير متاح - لا يمكن المتابعة")
        print("يرجى تثبيت PyQt6: pip install PyQt6")
        return False
    
    print()
    
    # اختبار ثوابت Qt
    qt_methods = test_qt_constants()
    print()
    
    # اختبار إعداد RTL
    rtl_methods = test_rtl_setup()
    print()
    
    # اختبار التطبيق الرئيسي
    main_app_works = test_main_application()
    print()
    
    # النتيجة النهائية
    print("="*50)
    print("📊 النتائج:")
    print(f"  ثوابت Qt المتاحة: {len(qt_methods)}")
    print(f"  طرق RTL الناجحة: {len(rtl_methods)}")
    print(f"  التطبيق الرئيسي: {'✅ يعمل' if main_app_works else '❌ لا يعمل'}")
    
    if rtl_methods:
        print(f"\n✅ الطرق الناجحة: {', '.join(rtl_methods)}")
        print("🎉 مشكلة RTL تم حلها!")
        return True
    else:
        print("\n❌ لم تنجح أي طريقة لإعداد RTL")
        print("💡 جرب تحديث PyQt6: pip install --upgrade PyQt6")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
