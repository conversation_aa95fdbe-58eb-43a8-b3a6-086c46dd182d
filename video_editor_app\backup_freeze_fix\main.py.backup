#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق معالج الفيديوهات المتكامل
Video Editor Application - Main Entry Point

المطور: مساعد الذكي
التاريخ: 2025
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# فحص المكتبات المطلوبة أولاً
try:
    from utils.safe_imports import safe_importer, is_available, print_status

    # طباعة تقرير المكتبات
    print_status()

    # التحقق من المكتبات الأساسية
    if not is_available('PyQt6'):
        print("❌ PyQt6 غير مثبت - لا يمكن تشغيل التطبيق")
        print("يرجى تثبيت PyQt6: pip install PyQt6")
        sys.exit(1)

except ImportError:
    print("⚠️ نظام الفحص غير متاح - سيتم المتابعة بدونه")

# استيراد PyQt6
try:
    from PyQt6.QtWidgets import QApplication, QMessageBox
    from PyQt6.QtCore import QTranslator, QLocale, Qt
    from PyQt6.QtGui import QIcon
except ImportError as e:
    print(f"❌ خطأ في استيراد PyQt6: {e}")
    print("يرجى تثبيت PyQt6: pip install PyQt6")
    sys.exit(1)

# استيراد qdarkstyle مع معالجة الخطأ
try:
    import qdarkstyle
    QDARKSTYLE_AVAILABLE = True
except ImportError:
    QDARKSTYLE_AVAILABLE = False
    print("⚠️ qdarkstyle غير مثبت - سيتم استخدام الستايل الافتراضي")

# استيراد الوحدات الأساسية مع معالجة الأخطاء
try:
    from gui.main_window import MainWindow
except ImportError as e:
    print(f"❌ خطأ في استيراد الواجهة الرئيسية: {e}")
    sys.exit(1)

try:
    from database.db_manager import DatabaseManager
except ImportError as e:
    print(f"⚠️ خطأ في استيراد قاعدة البيانات: {e}")
    DatabaseManager = None

try:
    from utils.logger import setup_logger
except ImportError as e:
    print(f"⚠️ خطأ في استيراد نظام السجلات: {e}")
    setup_logger = None

try:
    from config import config
except ImportError as e:
    print(f"⚠️ خطأ في استيراد الإعدادات: {e}")
    config = None

def setup_rtl_layout(app):
    """إعداد اتجاه RTL للغة العربية بشكل آمن"""
    rtl_methods = [
        # الطريقة الحديثة
        (lambda: app.setLayoutDirection(Qt.LayoutDirection.RightToLeft), "Qt.LayoutDirection.RightToLeft"),
        # الطريقة القديمة
        (lambda: app.setLayoutDirection(Qt.RightToLeft), "Qt.RightToLeft"),
        # الرقم المباشر
        (lambda: app.setLayoutDirection(2), "الرقم المباشر (2)"),
    ]

    for method, description in rtl_methods:
        try:
            method()
            print(f"✅ تم تعيين اتجاه RTL باستخدام {description}")
            return True
        except (AttributeError, TypeError, Exception) as e:
            print(f"⚠️ فشل {description}: {e}")
            continue

    print("❌ فشل في تعيين اتجاه RTL بجميع الطرق")
    return False

def setup_application():
    """إعداد التطبيق الأساسي"""
    app = QApplication(sys.argv)
    app.setApplicationName("معالج الفيديوهات المتكامل")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Video Editor Team")

    # إعداد اللغة العربية (RTL)
    setup_rtl_layout(app)
    
    # إعداد الستايل المظلم (إذا كان متاحاً)
    if QDARKSTYLE_AVAILABLE:
        app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt6())
    else:
        # ستايل مظلم بسيط كبديل
        app.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #3b3b3b;
            }
            QTabBar::tab {
                background-color: #555;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
            }
        """)
    
    # إعداد أيقونة التطبيق
    icon_path = project_root / "assets" / "app_icon.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    return app

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        # إعداد نظام التسجيل (إذا كان متاحاً)
        if setup_logger:
            logger = setup_logger()
            logger.info("بدء تشغيل تطبيق معالج الفيديوهات")
        else:
            logger = None
            print("🚀 بدء تشغيل معالج الفيديوهات المتكامل")

        # إنشاء التطبيق
        try:
            app = setup_application()
        except Exception as e:
            print(f"❌ خطأ في إعداد التطبيق: {e}")
            # محاولة إنشاء تطبيق أساسي
            app = QApplication(sys.argv)
            app.setApplicationName("معالج الفيديوهات المتكامل")
            print("⚠️ تم إنشاء تطبيق أساسي بدون RTL")

        # تحميل الإعدادات وإنشاء المجلدات (إذا كانت متاحة)
        if config:
            try:
                config.create_directories()
            except Exception as e:
                print(f"⚠️ خطأ في إنشاء المجلدات: {e}")

        # إعداد قاعدة البيانات (إذا كانت متاحة)
        if DatabaseManager:
            try:
                db_manager = DatabaseManager()
                db_manager.initialize_database()
            except Exception as e:
                print(f"⚠️ خطأ في قاعدة البيانات: {e}")
                QMessageBox.warning(None, "تحذير", f"خطأ في قاعدة البيانات: {e}")

        # إنشاء النافذة الرئيسية
        try:
            main_window = MainWindow()
            main_window.show()
        except Exception as e:
            print(f"❌ خطأ في إنشاء الواجهة الرئيسية: {e}")
            QMessageBox.critical(None, "خطأ", f"خطأ في إنشاء الواجهة: {e}")
            sys.exit(1)

        if logger:
            logger.info("تم تشغيل التطبيق بنجاح")
        else:
            print("✅ تم تشغيل التطبيق بنجاح")

        # تشغيل التطبيق
        sys.exit(app.exec())

    except Exception as e:
        error_msg = f"خطأ في تشغيل التطبيق: {str(e)}"
        print(f"❌ {error_msg}")

        # محاولة عرض رسالة خطأ رسومية
        try:
            app = QApplication.instance()
            if app is None:
                app = QApplication(sys.argv)
            QMessageBox.critical(None, "خطأ في التطبيق", error_msg)
        except:
            pass

        if logger:
            logger.error(error_msg)
        else:
            logging.error(error_msg)

        sys.exit(1)

if __name__ == "__main__":
    main()
