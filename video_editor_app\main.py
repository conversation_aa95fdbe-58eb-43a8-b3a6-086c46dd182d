#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق معالج الفيديوهات المتكامل
Video Editor Application - Main Entry Point

المطور: مساعد الذكي
التاريخ: 2025
"""

import sys
import os
import logging
from pathlib import Path

# إضافة مسار المشروع إلى sys.path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTranslator, QLocale
from PyQt6.QtGui import QIcon

# استيراد qdarkstyle مع معالجة الخطأ
try:
    import qdarkstyle
    QDARKSTYLE_AVAILABLE = True
except ImportError:
    QDARKSTYLE_AVAILABLE = False
    print("⚠️ qdarkstyle غير مثبت - سيتم استخدام الستايل الافتراضي")

# استيراد الواجهة الرئيسية
from gui.main_window import MainWindow
from database.db_manager import DatabaseManager
from utils.logger import setup_logger
from config import config

def setup_application():
    """إعداد التطبيق الأساسي"""
    app = QApplication(sys.argv)
    
    # إعداد اللغة العربية
    app.setLayoutDirection(2)  # RTL for Arabic
    
    # إعداد الستايل المظلم (إذا كان متاحاً)
    if QDARKSTYLE_AVAILABLE:
        app.setStyleSheet(qdarkstyle.load_stylesheet_pyqt6())
    else:
        # ستايل مظلم بسيط كبديل
        app.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QWidget {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QTabWidget::pane {
                border: 1px solid #555;
                background-color: #3b3b3b;
            }
            QTabBar::tab {
                background-color: #555;
                color: white;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
            }
        """)
    
    # إعداد أيقونة التطبيق
    icon_path = project_root / "assets" / "app_icon.ico"
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))
    
    return app

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        # إعداد نظام التسجيل
        logger = setup_logger()
        logger.info("بدء تشغيل تطبيق معالج الفيديوهات")
        
        # إنشاء التطبيق
        app = setup_application()
        
        # تحميل الإعدادات وإنشاء المجلدات
        config.create_directories()

        # إعداد قاعدة البيانات
        db_manager = DatabaseManager()
        db_manager.initialize_database()

        # إنشاء النافذة الرئيسية
        main_window = MainWindow()
        main_window.show()
        
        logger.info("تم تشغيل التطبيق بنجاح")
        
        # تشغيل التطبيق
        sys.exit(app.exec())
        
    except Exception as e:
        logging.error(f"خطأ في تشغيل التطبيق: {str(e)}")
        print(f"خطأ في تشغيل التطبيق: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
