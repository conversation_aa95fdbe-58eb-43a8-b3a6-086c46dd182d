# -*- coding: utf-8 -*-
"""
وحدة تتبع الوجوه
Face Tracking Module for Video Editor Application
"""

import cv2
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional, Callable, Tuple
import logging

try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
except ImportError:
    MEDIAPIPE_AVAILABLE = False

try:
    import face_recognition
    FACE_RECOGNITION_AVAILABLE = True
except ImportError:
    FACE_RECOGNITION_AVAILABLE = False

try:
    from moviepy.editor import VideoFileClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False

from utils.logger import VideoEditorLogger

class FaceTracker:
    """فئة تتبع الوجوه في الفيديوهات"""
    
    def __init__(self, output_path: str = None):
        """
        تهيئة متتبع الوجوه
        
        Args:
            output_path: مسار الإخراج الافتراضي
        """
        self.logger = VideoEditorLogger(__name__)
        
        if output_path is None:
            output_path = Path(__file__).parent.parent / "output_videos"
        
        self.output_path = Path(output_path)
        self.output_path.mkdir(exist_ok=True)
        
        # إعداد MediaPipe
        if MEDIAPIPE_AVAILABLE:
            self.mp_face_detection = mp.solutions.face_detection
            self.mp_drawing = mp.solutions.drawing_utils
            self.face_detection = self.mp_face_detection.FaceDetection(
                model_selection=0, min_detection_confidence=0.5
            )
        
        # إعداد OpenCV
        self.face_cascade = cv2.CascadeClassifier(
            cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
        )
        
        self.logger.info("تم تهيئة متتبع الوجوه")
    
    def detect_faces_in_frame(self, frame: np.ndarray, method: str = 'mediapipe') -> List[Dict]:
        """
        كشف الوجوه في إطار واحد
        
        Args:
            frame: الإطار المراد تحليله
            method: طريقة الكشف ('mediapipe', 'opencv', 'face_recognition')
            
        Returns:
            List[Dict]: قائمة الوجوه المكتشفة
        """
        faces = []
        
        try:
            if method == 'mediapipe' and MEDIAPIPE_AVAILABLE:
                faces = self._detect_faces_mediapipe(frame)
            elif method == 'opencv':
                faces = self._detect_faces_opencv(frame)
            elif method == 'face_recognition' and FACE_RECOGNITION_AVAILABLE:
                faces = self._detect_faces_face_recognition(frame)
            else:
                # استخدام OpenCV كطريقة افتراضية
                faces = self._detect_faces_opencv(frame)
                
        except Exception as e:
            self.logger.error(f"خطأ في كشف الوجوه: {str(e)}")
        
        return faces
    
    def _detect_faces_mediapipe(self, frame: np.ndarray) -> List[Dict]:
        """كشف الوجوه باستخدام MediaPipe"""
        faces = []
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.face_detection.process(rgb_frame)
        
        if results.detections:
            h, w, _ = frame.shape
            for detection in results.detections:
                bbox = detection.location_data.relative_bounding_box
                
                # تحويل الإحداثيات النسبية إلى مطلقة
                x = int(bbox.xmin * w)
                y = int(bbox.ymin * h)
                width = int(bbox.width * w)
                height = int(bbox.height * h)
                
                faces.append({
                    'x': x,
                    'y': y,
                    'width': width,
                    'height': height,
                    'confidence': detection.score[0],
                    'center': (x + width // 2, y + height // 2),
                    'area': width * height
                })
        
        return faces
    
    def _detect_faces_opencv(self, frame: np.ndarray) -> List[Dict]:
        """كشف الوجوه باستخدام OpenCV"""
        faces = []
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        detected_faces = self.face_cascade.detectMultiScale(
            gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30)
        )
        
        for (x, y, w, h) in detected_faces:
            faces.append({
                'x': x,
                'y': y,
                'width': w,
                'height': h,
                'confidence': 1.0,  # OpenCV لا يعطي confidence score
                'center': (x + w // 2, y + h // 2),
                'area': w * h
            })
        
        return faces
    
    def _detect_faces_face_recognition(self, frame: np.ndarray) -> List[Dict]:
        """كشف الوجوه باستخدام face_recognition"""
        faces = []
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        face_locations = face_recognition.face_locations(rgb_frame)
        
        for (top, right, bottom, left) in face_locations:
            width = right - left
            height = bottom - top
            
            faces.append({
                'x': left,
                'y': top,
                'width': width,
                'height': height,
                'confidence': 1.0,
                'center': (left + width // 2, top + height // 2),
                'area': width * height
            })
        
        return faces
    
    def track_faces_in_video(self, video_path: str, tracking_options: Dict,
                            progress_callback: Callable = None) -> str:
        """
        تتبع الوجوه في الفيديو وإنشاء فيديو معدل
        
        Args:
            video_path: مسار الفيديو
            tracking_options: خيارات التتبع
            progress_callback: دالة تحديث التقدم
            
        Returns:
            str: مسار الفيديو المعدل
        """
        try:
            self.logger.operation_start("تتبع الوجوه", video_path)
            
            cap = cv2.VideoCapture(video_path)
            
            # الحصول على معلومات الفيديو
            fps = int(cap.get(cv2.CAP_PROP_FPS))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            # إعداد كاتب الفيديو
            video_name = Path(video_path).stem
            output_path = self.output_path / f"{video_name}_face_tracked.mp4"
            
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
            
            # متغيرات التتبع
            tracking_type = tracking_options.get('tracking_type', 'main_face')
            sensitivity = tracking_options.get('sensitivity', 5)
            detection_method = tracking_options.get('method', 'mediapipe')
            
            frame_count = 0
            main_face_tracker = None
            
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # كشف الوجوه في الإطار الحالي
                faces = self.detect_faces_in_frame(frame, detection_method)
                
                if faces:
                    if tracking_type == 'main_face':
                        # تتبع الوجه الرئيسي (الأكبر)
                        main_face = max(faces, key=lambda f: f['area'])
                        frame = self._center_on_face(frame, main_face, width, height)
                        
                    elif tracking_type == 'all_faces':
                        # تتبع جميع الوجوه
                        frame = self._highlight_all_faces(frame, faces)
                        
                    elif tracking_type == 'largest_face':
                        # تتبع أكبر وجه
                        largest_face = max(faces, key=lambda f: f['area'])
                        frame = self._center_on_face(frame, largest_face, width, height)
                
                # كتابة الإطار المعدل
                out.write(frame)
                
                frame_count += 1
                
                # تحديث التقدم
                if progress_callback and frame_count % 30 == 0:  # كل ثانية تقريباً
                    progress = int((frame_count / total_frames) * 100)
                    progress_callback(progress)
            
            # تنظيف الموارد
            cap.release()
            out.release()
            
            # إضافة الصوت الأصلي إذا كان متاحاً
            if MOVIEPY_AVAILABLE:
                output_path = self._add_original_audio(video_path, str(output_path))
            
            if progress_callback:
                progress_callback(100)
            
            self.logger.operation_complete("تتبع الوجوه", video_path, 0)
            return str(output_path)
            
        except Exception as e:
            self.logger.operation_error("تتبع الوجوه", video_path, str(e))
            return ""
    
    def _center_on_face(self, frame: np.ndarray, face: Dict, 
                       target_width: int, target_height: int) -> np.ndarray:
        """توسيط الإطار على الوجه"""
        h, w = frame.shape[:2]
        face_center_x, face_center_y = face['center']
        
        # حساب منطقة القص الجديدة
        crop_width = min(target_width, w)
        crop_height = min(target_height, h)
        
        # توسيط القص على الوجه
        start_x = max(0, face_center_x - crop_width // 2)
        start_y = max(0, face_center_y - crop_height // 2)
        
        # التأكد من عدم تجاوز حدود الإطار
        if start_x + crop_width > w:
            start_x = w - crop_width
        if start_y + crop_height > h:
            start_y = h - crop_height
        
        # قص الإطار
        cropped = frame[start_y:start_y + crop_height, start_x:start_x + crop_width]
        
        # تغيير الحجم إذا لزم الأمر
        if cropped.shape[:2] != (target_height, target_width):
            cropped = cv2.resize(cropped, (target_width, target_height))
        
        return cropped
    
    def _highlight_all_faces(self, frame: np.ndarray, faces: List[Dict]) -> np.ndarray:
        """تمييز جميع الوجوه في الإطار"""
        for face in faces:
            x, y, w, h = face['x'], face['y'], face['width'], face['height']
            
            # رسم مستطيل حول الوجه
            cv2.rectangle(frame, (x, y), (x + w, y + h), (0, 255, 0), 2)
            
            # إضافة نص يوضح الثقة
            confidence = face.get('confidence', 1.0)
            cv2.putText(frame, f'{confidence:.2f}', (x, y - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        
        return frame
    
    def _add_original_audio(self, original_video_path: str, processed_video_path: str) -> str:
        """إضافة الصوت الأصلي للفيديو المعالج"""
        try:
            from moviepy.editor import VideoFileClip
            
            # تحميل الفيديو الأصلي والمعالج
            original_clip = VideoFileClip(original_video_path)
            processed_clip = VideoFileClip(processed_video_path)
            
            # إضافة الصوت الأصلي
            if original_clip.audio is not None:
                final_clip = processed_clip.set_audio(original_clip.audio)
                
                # حفظ الفيديو النهائي
                output_path = processed_video_path.replace('.mp4', '_with_audio.mp4')
                final_clip.write_videofile(
                    output_path,
                    codec='libx264',
                    audio_codec='aac',
                    verbose=False,
                    logger=None
                )
                
                # تنظيف الموارد
                original_clip.close()
                processed_clip.close()
                final_clip.close()
                
                # حذف الملف المؤقت
                Path(processed_video_path).unlink()
                
                return output_path
            
            original_clip.close()
            processed_clip.close()
            
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الصوت: {str(e)}")
        
        return processed_video_path
    
    def analyze_face_distribution(self, video_path: str) -> Dict:
        """
        تحليل توزيع الوجوه في الفيديو
        
        Args:
            video_path: مسار الفيديو
            
        Returns:
            Dict: إحصائيات الوجوه
        """
        try:
            cap = cv2.VideoCapture(video_path)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            face_counts = []
            frame_count = 0
            
            # تحليل عينة من الإطارات (كل 30 إطار)
            while frame_count < total_frames:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_count)
                ret, frame = cap.read()
                
                if not ret:
                    break
                
                faces = self.detect_faces_in_frame(frame)
                face_counts.append(len(faces))
                
                frame_count += 30  # تخطي 30 إطار
            
            cap.release()
            
            if face_counts:
                return {
                    'total_frames_analyzed': len(face_counts),
                    'average_faces_per_frame': np.mean(face_counts),
                    'max_faces_in_frame': max(face_counts),
                    'min_faces_in_frame': min(face_counts),
                    'frames_with_faces': sum(1 for count in face_counts if count > 0),
                    'face_detection_rate': sum(1 for count in face_counts if count > 0) / len(face_counts)
                }
            else:
                return {}
                
        except Exception as e:
            self.logger.error(f"خطأ في تحليل توزيع الوجوه: {str(e)}")
            return {}
