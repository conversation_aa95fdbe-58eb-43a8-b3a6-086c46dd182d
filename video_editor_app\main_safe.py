#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق آمن تماماً - Completely Safe Application
نسخة مضمونة الاستجابة 100%
"""

import sys
import os
import signal
import threading
import time
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class ApplicationMonitor:
    """مراقب التطبيق لمنع التجمد"""
    
    def __init__(self, timeout=30):
        self.timeout = timeout
        self.is_running = True
        self.last_heartbeat = time.time()
        
    def start_monitoring(self):
        """بدء المراقبة"""
        def monitor():
            while self.is_running:
                time.sleep(5)
                if time.time() - self.last_heartbeat > self.timeout:
                    print("⚠️ التطبيق متجمد - إنهاء قسري")
                    os._exit(1)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def heartbeat(self):
        """نبضة للإشارة أن التطبيق يعمل"""
        self.last_heartbeat = time.time()
    
    def stop(self):
        """إيقاف المراقبة"""
        self.is_running = False

def setup_signal_handlers():
    """إعداد معالجات الإشارات"""
    def signal_handler(signum, frame):
        print(f"\n🛑 تم استلام إشارة {signum} - إغلاق التطبيق...")
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def safe_import_pyqt6():
    """استيراد آمن لـ PyQt6"""
    try:
        print("📦 استيراد PyQt6...")
        from PyQt6.QtWidgets import QApplication, QMessageBox
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QIcon
        print("✅ تم استيراد PyQt6 بنجاح")
        return True, (QApplication, QMessageBox, Qt, QIcon)
    except ImportError as e:
        print(f"❌ خطأ في استيراد PyQt6: {e}")
        print("💡 الحل: pip install PyQt6")
        return False, None

def safe_import_main_window():
    """استيراد آمن للنافذة الرئيسية"""
    try:
        print("🖼️ استيراد النافذة الرئيسية...")
        from gui.main_window_safe import SafeMainWindow
        print("✅ تم استيراد النافذة الآمنة")
        return True, SafeMainWindow
    except ImportError as e:
        print(f"❌ خطأ في استيراد النافذة: {e}")
        return False, None

def setup_rtl_safe(app, Qt):
    """إعداد RTL آمن"""
    try:
        print("🔄 إعداد اتجاه RTL...")
        
        # طرق متعددة للـ RTL
        rtl_methods = [
            (lambda: app.setLayoutDirection(Qt.LayoutDirection.RightToLeft), "Qt.LayoutDirection.RightToLeft"),
            (lambda: app.setLayoutDirection(Qt.RightToLeft), "Qt.RightToLeft"),
            (lambda: app.setLayoutDirection(2), "الرقم المباشر"),
        ]
        
        for method, description in rtl_methods:
            try:
                method()
                print(f"✅ تم تعيين RTL باستخدام {description}")
                return True
            except Exception as e:
                print(f"⚠️ فشل {description}: {e}")
                continue
        
        print("❌ فشل في تعيين RTL")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في إعداد RTL: {e}")
        return False

def setup_application_safe(QApplication, Qt):
    """إعداد التطبيق بشكل آمن"""
    try:
        print("🚀 إنشاء التطبيق...")
        
        app = QApplication(sys.argv)
        app.setApplicationName("معالج الفيديوهات المتكامل - الوضع الآمن")
        app.setApplicationVersion("1.0.0-safe")
        app.setOrganizationName("Video Editor Team")
        
        # إعداد RTL
        setup_rtl_safe(app, Qt)
        
        # إعداد ستايل بسيط
        app.setStyleSheet("""
            QMainWindow {
                background-color: #f0f0f0;
                color: #333333;
            }
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QTabWidget::pane {
                border: 1px solid #ccc;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #e0e0e0;
                color: #333;
                padding: 8px 16px;
                margin-right: 2px;
            }
            QTabBar::tab:selected {
                background-color: #4CAF50;
                color: white;
            }
        """)
        
        print("✅ تم إعداد التطبيق بنجاح")
        return app
        
    except Exception as e:
        print(f"❌ خطأ في إعداد التطبيق: {e}")
        return None

def create_main_window_safe(SafeMainWindow, monitor):
    """إنشاء النافذة الرئيسية بشكل آمن"""
    try:
        print("🖼️ إنشاء النافذة الرئيسية...")
        
        # نبضة للمراقب
        monitor.heartbeat()
        
        window = SafeMainWindow()
        
        # نبضة أخرى
        monitor.heartbeat()
        
        print("✅ تم إنشاء النافذة بنجاح")
        return window
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة: {e}")
        import traceback
        traceback.print_exc()
        return None

def run_application_safe(app, window, monitor):
    """تشغيل التطبيق بشكل آمن"""
    try:
        print("📺 عرض النافذة...")
        window.show()
        
        # نبضة للمراقب
        monitor.heartbeat()
        
        print("🔄 بدء حلقة الأحداث...")
        print("✅ التطبيق يعمل! (اضغط Ctrl+C للإيقاف)")
        
        # تشغيل حلقة الأحداث مع نبضات دورية
        def heartbeat_timer():
            while monitor.is_running:
                time.sleep(1)
                monitor.heartbeat()
        
        heartbeat_thread = threading.Thread(target=heartbeat_timer, daemon=True)
        heartbeat_thread.start()
        
        # تشغيل التطبيق
        result = app.exec()
        
        # إيقاف المراقب
        monitor.stop()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return 1

def create_emergency_window(QApplication, QMessageBox):
    """إنشاء نافذة طوارئ"""
    try:
        from PyQt6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        window = QMainWindow()
        window.setWindowTitle("وضع الطوارئ")
        window.resize(400, 200)
        
        central_widget = QWidget()
        window.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        label = QLabel("⚠️ حدث خطأ في التطبيق الرئيسي\nتم تشغيل وضع الطوارئ")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(label)
        
        close_btn = QPushButton("إغلاق")
        close_btn.clicked.connect(app.quit)
        layout.addWidget(close_btn)
        
        window.show()
        return app.exec()
        
    except Exception as e:
        print(f"❌ فشل حتى في وضع الطوارئ: {e}")
        return 1

def main():
    """الدالة الرئيسية الآمنة"""
    print("🎬 معالج الفيديوهات المتكامل - الوضع الآمن")
    print("="*50)
    
    # إعداد معالجات الإشارات
    setup_signal_handlers()
    
    # بدء مراقب التطبيق
    monitor = ApplicationMonitor(timeout=60)
    monitor.start_monitoring()
    
    try:
        # 1. استيراد PyQt6
        pyqt6_success, pyqt6_modules = safe_import_pyqt6()
        if not pyqt6_success:
            print("❌ فشل في استيراد PyQt6")
            return 1
        
        QApplication, QMessageBox, Qt, QIcon = pyqt6_modules
        monitor.heartbeat()
        
        # 2. استيراد النافذة الرئيسية
        window_import_success, SafeMainWindow = safe_import_main_window()
        if not window_import_success:
            print("❌ فشل في استيراد النافذة - تشغيل وضع الطوارئ")
            return create_emergency_window(QApplication, QMessageBox)
        
        monitor.heartbeat()
        
        # 3. إعداد التطبيق
        app = setup_application_safe(QApplication, Qt)
        if app is None:
            print("❌ فشل في إعداد التطبيق")
            return 1
        
        monitor.heartbeat()
        
        # 4. إنشاء النافذة الرئيسية
        window = create_main_window_safe(SafeMainWindow, monitor)
        if window is None:
            print("❌ فشل في إنشاء النافذة - تشغيل وضع الطوارئ")
            return create_emergency_window(QApplication, QMessageBox)
        
        monitor.heartbeat()
        
        # 5. تشغيل التطبيق
        return run_application_safe(app, window, monitor)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        monitor.stop()
        return 0
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        monitor.stop()
        
        # محاولة أخيرة لعرض رسالة خطأ
        try:
            return create_emergency_window(QApplication, QMessageBox)
        except:
            return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ خطأ كارثي: {e}")
        sys.exit(1)
