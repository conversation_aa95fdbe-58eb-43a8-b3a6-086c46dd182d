# معالج الفيديوهات المتكامل - Video Editor Pro

## 🎬 نظرة عامة

معالج الفيديوهات المتكامل هو تطبيق سطح مكتب قوي ومتطور مبني بلغة Python يوفر مجموعة شاملة من أدوات معالجة الفيديوهات والذكاء الاصطناعي اللغوي.

## ✨ الميزات الرئيسية

### 🎥 معالجة الفيديوهات
- **قص الفيديوهات**: قص الفيديوهات الطويلة إلى مقاطع قصيرة بطرق مختلفة (تلقائي، متساوي، حسب المشاهد)
- **تحميل الفيديوهات**: تحميل من جميع المنصات الشهيرة (YouTube, TikTok, Facebook, Instagram)
- **المونتاج التلقائي**: إضافة مقدمة/خاتمة، انتقالات، موسيقى، وفلاتر تلقائياً
- **تتبع الوجوه**: تتبع ذكي للوجوه وتحسين العرض باستخدام OpenCV و MediaPipe

### 🌐 الذكاء اللغوي
- **تفريغ الصوت**: تحويل الكلام إلى نص باستخدام Whisper أو Google Speech API
- **الترجمة التلقائية**: ترجمة إلى جميع اللغات باستخدام Google Translate أو DeepL
- **الدبلجة الذكية**: تحويل النص المترجم إلى صوت باستخدام gTTS أو ElevenLabs
- **الكتابة التلقائية**: إنتاج تعليقات وشروحات تلقائية

### 🖥️ واجهة المستخدم
- **واجهة عربية كاملة**: تصميم حديث وسهل الاستخدام
- **معالجة متوازية**: تشغيل العمليات في خيوط منفصلة
- **تتبع التقدم**: مؤشرات تقدم مفصلة لكل عملية
- **إدارة المشاريع**: حفظ وتحميل المشاريع

### 💾 إدارة البيانات
- **قاعدة بيانات SQLite**: حفظ معلومات الفيديوهات والعمليات
- **نظام السجلات**: تسجيل مفصل لجميع العمليات
- **النسخ الاحتياطي**: حفظ تلقائي للبيانات

## 🛠️ التقنيات المستخدمة

### واجهة المستخدم
- **PyQt6**: إطار العمل الرئيسي للواجهة الرسومية
- **qdarkstyle**: تصميم مظلم حديث

### معالجة الفيديو والصوت
- **MoviePy**: معالجة وتحرير الفيديوهات
- **OpenCV**: معالجة الصور والفيديو
- **FFmpeg**: تحويل وضغط الفيديوهات
- **MediaPipe**: كشف وتتبع الوجوه

### الذكاء الاصطناعي واللغات
- **OpenAI Whisper**: تفريغ الصوت عالي الجودة
- **Google Translate API**: ترجمة متعددة اللغات
- **gTTS**: تحويل النص إلى كلام
- **SpeechRecognition**: كشف الكلام

### تحميل الفيديوهات
- **yt-dlp**: تحميل من جميع المنصات
- **pytube**: تحميل من YouTube

### قاعدة البيانات والتخزين
- **SQLite**: قاعدة بيانات محلية
- **SQLAlchemy**: ORM لإدارة قاعدة البيانات

## 📋 متطلبات النظام

### الحد الأدنى
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, Linux Ubuntu 18.04+
- **المعالج**: Intel i3 أو AMD Ryzen 3
- **الذاكرة**: 4 GB RAM
- **التخزين**: 2 GB مساحة فارغة
- **Python**: 3.8 أو أحدث

### الموصى به
- **المعالج**: Intel i5 أو AMD Ryzen 5
- **الذاكرة**: 8 GB RAM أو أكثر
- **كرت الرسوميات**: GPU مدعوم لتسريع المعالجة
- **التخزين**: 5 GB مساحة فارغة

## 🚀 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/video-editor-app.git
cd video-editor-app
```

### 2. إنشاء بيئة افتراضية
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# macOS/Linux
source venv/bin/activate
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. تشغيل التطبيق
```bash
python main.py
```

## 🔧 الإعداد

### مفاتيح API (اختيارية)
لاستخدام الميزات المتقدمة، يمكنك إضافة مفاتيح API في إعدادات التطبيق:

- **OpenAI API**: للدبلجة عالية الجودة وتفريغ الصوت
- **ElevenLabs API**: للدبلجة الواقعية
- **DeepL API**: للترجمة عالية الجودة

### إعداد FFmpeg
تأكد من تثبيت FFmpeg على نظامك:

**Windows:**
```bash
# باستخدام chocolatey
choco install ffmpeg
```

**macOS:**
```bash
# باستخدام homebrew
brew install ffmpeg
```

**Linux:**
```bash
sudo apt update
sudo apt install ffmpeg
```

## 📖 دليل الاستخدام

### 1. إضافة فيديو
- انقر على "فتح ملف" لاختيار فيديو من جهازك
- أو انقر على "تحميل فيديو" لتحميل من رابط

### 2. إعداد المعالجة
- اختر مدة المقاطع المطلوبة
- حدد نوع القص (تلقائي، متساوي، حسب المشاهد)
- اختر اللغات للترجمة والدبلجة
- فعّل الميزات المطلوبة (مونتاج، تتبع وجوه)

### 3. بدء المعالجة
- انقر على "بدء المعالجة"
- تابع التقدم في شريط التقدم
- ستجد النتائج في مجلد الإخراج

## 📁 هيكل المشروع

```
video_editor_app/
├── main.py                 # نقطة دخول التطبيق
├── config.py              # إعدادات التطبيق
├── requirements.txt       # متطلبات Python
├── README.md             # هذا الملف
├── gui/                  # واجهة المستخدم
│   ├── main_window.py    # النافذة الرئيسية
│   └── __init__.py
├── src/                  # النواة الرئيسية
│   ├── video_editor_core.py
│   └── __init__.py
├── video_processing/     # معالجة الفيديوهات
│   ├── video_downloader.py
│   ├── video_processor.py
│   ├── face_tracker.py
│   └── __init__.py
├── language_ai/          # الذكاء اللغوي
│   ├── speech_to_text.py
│   ├── translator.py
│   ├── text_to_speech.py
│   └── __init__.py
├── database/             # قاعدة البيانات
│   ├── db_manager.py
│   └── __init__.py
├── utils/                # أدوات مساعدة
│   ├── logger.py
│   └── __init__.py
├── assets/               # الموارد
├── temp_files/           # ملفات مؤقتة
└── output_videos/        # مجلد الإخراج
```

## 🔄 إنشاء ملف EXE

لإنشاء ملف تنفيذي قابل للتوزيع:

```bash
# تثبيت PyInstaller
pip install pyinstaller

# إنشاء ملف EXE
pyinstaller --onefile --windowed --icon=assets/app_icon.ico main.py
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. خطأ في تحميل الفيديو:**
- تأكد من صحة الرابط
- تحقق من اتصال الإنترنت
- جرب رابط آخر

**2. فشل في معالجة الفيديو:**
- تأكد من تثبيت FFmpeg
- تحقق من مساحة التخزين المتاحة
- جرب فيديو أصغر حجماً

**3. مشاكل في الترجمة:**
- تحقق من اتصال الإنترنت
- تأكد من صحة مفاتيح API
- جرب خدمة ترجمة أخرى

## 🤝 المساهمة

نرحب بمساهماتكم! يرجى:

1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

للحصول على الدعم:
- افتح Issue في GitHub
- راسلنا على البريد الإلكتروني
- انضم إلى مجتمعنا على Discord

## 🙏 شكر وتقدير

شكر خاص لجميع المطورين والمساهمين في المكتبات مفتوحة المصدر المستخدمة في هذا المشروع.

---

**تم تطويره بـ ❤️ للمجتمع العربي**
