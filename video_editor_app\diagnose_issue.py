#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشاكل عدم استجابة التطبيق
Diagnose Application Unresponsiveness Issues
"""

import sys
import time
import traceback
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    print("🔍 اختبار الاستيرادات الأساسية...")
    
    try:
        print("  - Python standard library...")
        import os, sys, json, sqlite3, threading, logging
        print("  ✅ Python standard library")
        
        print("  - PyQt6...")
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        print("  ✅ PyQt6 أساسي")
        
        print("  - إنشاء QApplication...")
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("  ✅ QApplication")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في الاستيرادات: {e}")
        traceback.print_exc()
        return False

def test_project_modules():
    """اختبار وحدات المشروع"""
    print("\n📦 اختبار وحدات المشروع...")
    
    modules = [
        ("config", "import config"),
        ("utils", "from utils import VideoEditorLogger"),
        ("database", "from database import DatabaseManager"),
        ("src", "from src import VideoEditorCore"),
        ("gui", "from gui import MainWindow"),
    ]
    
    for name, import_cmd in modules:
        try:
            print(f"  - {name}...")
            exec(import_cmd)
            print(f"  ✅ {name}")
        except Exception as e:
            print(f"  ❌ {name}: {e}")
            return False
    
    return True

def test_main_window_step_by_step():
    """اختبار إنشاء النافذة خطوة بخطوة"""
    print("\n🖼️ اختبار إنشاء النافذة خطوة بخطوة...")
    
    try:
        print("  - استيراد QApplication...")
        from PyQt6.QtWidgets import QApplication
        
        print("  - إنشاء QApplication...")
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        print("  - استيراد MainWindow...")
        from gui.main_window import MainWindow
        
        print("  - إنشاء MainWindow...")
        window = MainWindow()
        
        print("  - تعيين خصائص النافذة...")
        window.setWindowTitle("اختبار")
        window.resize(800, 600)
        
        print("  ✅ تم إنشاء النافذة بنجاح")
        return True, window
        
    except Exception as e:
        print(f"  ❌ خطأ في إنشاء النافذة: {e}")
        traceback.print_exc()
        return False, None

def test_minimal_window():
    """اختبار نافذة مبسطة"""
    print("\n🪟 اختبار نافذة مبسطة...")
    
    try:
        from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # إنشاء نافذة بسيطة
        window = QMainWindow()
        window.setWindowTitle("اختبار بسيط")
        window.resize(400, 300)
        
        # إضافة محتوى بسيط
        central_widget = QWidget()
        layout = QVBoxLayout(central_widget)
        label = QLabel("مرحباً! التطبيق يعمل بشكل صحيح")
        layout.addWidget(label)
        
        window.setCentralWidget(central_widget)
        
        print("  ✅ تم إنشاء نافذة بسيطة")
        return True, window
        
    except Exception as e:
        print(f"  ❌ خطأ في النافذة البسيطة: {e}")
        traceback.print_exc()
        return False, None

def run_quick_test():
    """تشغيل اختبار سريع"""
    print("\n⚡ اختبار سريع للتطبيق...")
    
    try:
        # اختبار مع timeout
        import signal
        
        def timeout_handler(signum, frame):
            raise TimeoutError("انتهت مهلة الاختبار")
        
        # تعيين timeout لـ 10 ثواني
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(10)
        
        try:
            from PyQt6.QtWidgets import QApplication
            from gui.main_window import MainWindow
            
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            
            window = MainWindow()
            print("  ✅ تم إنشاء النافذة في الوقت المحدد")
            
            signal.alarm(0)  # إلغاء timeout
            return True
            
        except TimeoutError:
            print("  ❌ انتهت مهلة الاختبار - التطبيق معلق")
            return False
            
    except Exception as e:
        print(f"  ❌ خطأ في الاختبار السريع: {e}")
        return False

def main():
    """الدالة الرئيسية للتشخيص"""
    print("🔍 تشخيص مشاكل عدم استجابة التطبيق")
    print("="*50)
    
    # اختبار الاستيرادات الأساسية
    if not test_basic_imports():
        print("\n❌ مشكلة في الاستيرادات الأساسية")
        print("💡 الحل: pip install PyQt6")
        return False
    
    # اختبار وحدات المشروع
    if not test_project_modules():
        print("\n❌ مشكلة في وحدات المشروع")
        print("💡 الحل: python fix_everything.py")
        return False
    
    # اختبار سريع
    if not run_quick_test():
        print("\n❌ التطبيق معلق أو بطيء جداً")
        print("💡 المشكلة: حلقة لا نهائية أو خطأ في التهيئة")
        
        # اختبار نافذة بسيطة
        success, window = test_minimal_window()
        if success:
            print("✅ النافذة البسيطة تعمل - المشكلة في MainWindow")
            print("💡 الحل: إعادة إنشاء MainWindow مبسط")
        else:
            print("❌ حتى النافذة البسيطة لا تعمل")
            print("💡 الحل: إعادة تثبيت PyQt6")
        
        return False
    
    # اختبار النافذة الرئيسية
    success, window = test_main_window_step_by_step()
    if not success:
        print("\n❌ مشكلة في MainWindow")
        print("💡 الحل: إعادة إنشاء MainWindow")
        return False
    
    print("\n✅ جميع الاختبارات نجحت!")
    print("💡 المشكلة قد تكون في:")
    print("  1. حلقة الأحداث")
    print("  2. معالجة الإشارات")
    print("  3. تهيئة قاعدة البيانات")
    
    return True

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
