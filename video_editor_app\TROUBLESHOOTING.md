# دليل حل المشاكل - Troubleshooting Guide

## 🚨 المشاكل الشائعة وحلولها

### 1. خطأ "ModuleNotFoundError"

#### المشكلة:
```
ModuleNotFoundError: No module named 'qdarkstyle'
ModuleNotFoundError: No module named 'moviepy'
```

#### الحل السريع:
```bash
# تثبيت المتطلبات الأساسية فقط
pip install PyQt6 moviepy opencv-python requests sqlalchemy Pillow tqdm

# أو استخدام ملف المتطلبات المبسط
pip install -r requirements-minimal.txt
```

#### الحل الشامل:
```bash
# تثبيت جميع المتطلبات
pip install -r requirements.txt

# إذا فشل، جرب تثبيت كل حزمة منفصلة
pip install PyQt6
pip install moviepy
pip install opencv-python
pip install requests
pip install sqlalchemy
```

### 2. مشاكل PyQt6

#### المشكلة:
```
ImportError: No module named 'PyQt6'
qt.qpa.plugin: Could not load the Qt platform plugin
```

#### الحل:
```bash
# Windows
pip install PyQt6
pip install PyQt6-tools

# Linux
sudo apt install python3-pyqt6
pip install PyQt6

# macOS
brew install pyqt6
pip install PyQt6
```

### 3. مشاكل FFmpeg

#### المشكلة:
```
FileNotFoundError: [Errno 2] No such file or directory: 'ffmpeg'
```

#### الحل:
```bash
# Windows (باستخدام chocolatey)
choco install ffmpeg

# أو تحميل من https://ffmpeg.org/download.html

# Linux
sudo apt install ffmpeg

# macOS
brew install ffmpeg
```

### 4. مشاكل الذاكرة

#### المشكلة:
```
MemoryError: Unable to allocate array
```

#### الحل:
- استخدم فيديوهات أصغر حجماً للاختبار
- أغلق البرامج الأخرى
- قلل جودة الإخراج مؤقتاً
- أعد تشغيل الحاسوب

### 5. مشاكل الأذونات

#### المشكلة:
```
PermissionError: [Errno 13] Permission denied
```

#### الحل:
```bash
# Linux/macOS
sudo python main.py
chmod +x start.sh

# Windows
# شغل CMD كـ Administrator
```

## 🔧 حلول متقدمة

### إعادة تثبيت البيئة الافتراضية

```bash
# حذف البيئة القديمة
rm -rf venv  # Linux/macOS
rmdir /s venv  # Windows

# إنشاء بيئة جديدة
python -m venv venv

# تفعيل البيئة
source venv/bin/activate  # Linux/macOS
venv\Scripts\activate     # Windows

# تثبيت المتطلبات
pip install --upgrade pip
pip install -r requirements-minimal.txt
```

### تثبيت انتقائي للحزم

```bash
# المتطلبات الأساسية فقط (للتشغيل الأساسي)
pip install PyQt6 moviepy opencv-python requests sqlalchemy

# إضافة الميزات المتقدمة تدريجياً
pip install qdarkstyle  # للستايل المظلم
pip install yt-dlp      # لتحميل الفيديوهات
pip install googletrans # للترجمة
pip install gtts        # للدبلجة
```

### فحص التثبيت

```bash
# فحص Python
python --version

# فحص pip
pip --version

# فحص الحزم المثبتة
pip list

# فحص حزمة معينة
pip show PyQt6
```

## 🐛 تشخيص المشاكل

### تشغيل الاختبارات

```bash
# اختبار شامل للتطبيق
python test_app.py

# اختبار استيراد الوحدات
python -c "from gui.main_window import MainWindow; print('GUI OK')"
python -c "import moviepy; print('MoviePy OK')"
python -c "import cv2; print('OpenCV OK')"
```

### فحص السجلات

```bash
# عرض آخر الأخطاء
cat logs/video_editor_*.log  # Linux/macOS
type logs\video_editor_*.log  # Windows

# تشغيل مع تفاصيل أكثر
python main.py --verbose
```

## 🔄 بدائل للحزم المفقودة

### بديل qdarkstyle
إذا لم تستطع تثبيت qdarkstyle، التطبيق سيستخدم ستايل مظلم بسيط تلقائياً.

### بديل moviepy
```bash
# استخدام ffmpeg مباشرة
pip install ffmpeg-python
```

### بديل face-recognition
```bash
# استخدام OpenCV فقط
pip install opencv-python
# تتبع الوجوه سيعمل بـ OpenCV بدلاً من MediaPipe
```

## 📱 مشاكل خاصة بالنظام

### Windows

#### مشكلة: "Microsoft Visual C++ 14.0 is required"
**الحل:**
1. حمل Microsoft Visual C++ Build Tools
2. أو حمل Visual Studio Community
3. أو استخدم conda بدلاً من pip

#### مشكلة: "DLL load failed"
**الحل:**
```bash
pip install --upgrade --force-reinstall PyQt6
```

### Linux

#### مشكلة: "No module named '_tkinter'"
**الحل:**
```bash
sudo apt install python3-tk
```

#### مشكلة: "Qt platform plugin 'xcb'"
**الحل:**
```bash
sudo apt install python3-pyqt6
export QT_QPA_PLATFORM=xcb
```

### macOS

#### مشكلة: "No module named 'cv2'"
**الحل:**
```bash
brew install opencv
pip install opencv-python
```

## 🆘 الحصول على المساعدة

### معلومات مفيدة للدعم

عند طلب المساعدة، أرفق:

```bash
# معلومات النظام
python --version
pip --version
uname -a  # Linux/macOS
systeminfo  # Windows

# قائمة الحزم المثبتة
pip list

# رسالة الخطأ الكاملة
python main.py 2>&1 | tee error.log
```

### قنوات الدعم

1. **GitHub Issues**: للأخطاء والمشاكل التقنية
2. **Discord**: للمساعدة السريعة
3. **Email**: للدعم المفصل

### نصائح عامة

- ✅ تأكد من تحديث pip: `pip install --upgrade pip`
- ✅ استخدم بيئة افتراضية دائماً
- ✅ اقرأ رسائل الخطأ بعناية
- ✅ جرب الحلول البسيطة أولاً
- ✅ ابحث في Google عن رسالة الخطأ

---

**تذكر**: معظم المشاكل تُحل بإعادة تثبيت المتطلبات في بيئة افتراضية نظيفة! 🔧
