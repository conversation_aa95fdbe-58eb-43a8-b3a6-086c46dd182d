#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي الآمن
Safe Main Application
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def safe_import_pyqt6():
    """استيراد آمن لـ PyQt6"""
    try:
        from PyQt6.QtWidgets import QApplication, QMessageBox
        from PyQt6.QtCore import Qt
        return True, (QApplication, QMessageBox, Qt)
    except ImportError as e:
        print(f"❌ خطأ في استيراد PyQt6: {e}")
        print("💡 الحل: pip install PyQt6")
        return False, None

def safe_import_gui():
    """استيراد آمن للواجهة"""
    try:
        from gui import MainWindow
        return True, MainWindow
    except ImportError as e:
        print(f"❌ خطأ في استيراد GUI: {e}")
        return False, None

def setup_rtl(app, Qt):
    """إعداد RTL آمن"""
    try:
        app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
        return True
    except:
        try:
            app.setLayoutDirection(Qt.RightToLeft)
            return True
        except:
            try:
                app.setLayoutDirection(2)
                return True
            except:
                return False

def main():
    """الدالة الرئيسية"""
    print("🎬 معالج الفيديوهات المتكامل")
    print("="*40)
    
    try:
        # 1. استيراد PyQt6
        pyqt6_success, pyqt6_modules = safe_import_pyqt6()
        if not pyqt6_success:
            input("اضغط Enter للخروج...")
            return False
        
        QApplication, QMessageBox, Qt = pyqt6_modules
        
        # 2. إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("معالج الفيديوهات المتكامل")
        
        # 3. إعداد RTL
        setup_rtl(app, Qt)
        
        # 4. استيراد الواجهة
        gui_success, MainWindow = safe_import_gui()
        if not gui_success:
            QMessageBox.critical(None, "خطأ", "فشل في تحميل واجهة المستخدم")
            return False
        
        # 5. إنشاء النافذة الرئيسية
        try:
            window = MainWindow()
            window.show()
            print("✅ تم تشغيل التطبيق بنجاح")
        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة: {e}")
            QMessageBox.critical(None, "خطأ", f"فشل في إنشاء النافذة: {e}")
            return False
        
        # 6. تشغيل التطبيق
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق")
        return True
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ خطأ كارثي: {e}")
        input("اضغط Enter للخروج...")
        sys.exit(1)
