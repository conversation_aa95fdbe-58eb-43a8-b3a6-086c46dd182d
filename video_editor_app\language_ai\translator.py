# -*- coding: utf-8 -*-
"""
وحدة الترجمة
Translation Module for Video Editor Application
"""

import time
from typing import dict, list, Optional, Callable
import logging

try:
    from googletrans import Translator as GoogleTranslator
    GOOGLETRANS_AVAILABLE = True
except ImportError:
    GOOGLETRANS_AVAILABLE = False
    print("⚠️ googletrans غير مثبت - الترجمة محدودة")

try:
    from deep_translator import GoogleTranslator as DeepGoogleTranslator
    from deep_translator import DeepL
    DEEP_TRANSLATOR_AVAILABLE = True
except ImportError:
    DEEP_TRANSLATOR_AVAILABLE = False
    print("⚠️ deep_translator غير مثبت - ترجمة متقدمة محدودة")

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️ OpenAI غير مثبت - ترجمة AI محدودة")

from utils.logger import VideoEditorLogger

class Translator:
    """فئة الترجمة التلقائية"""
    
    def __init__(self, openai_api_key: str = None, deepl_api_key: str = None):
        """
        تهيئة المترجم
        
        Args:
            openai_api_key: مفتاح OpenAI API (اختياري)
            deepl_api_key: مفتاح DeepL API (اختياري)
        """
        self.logger = VideoEditorLogger(__name__)
        self.openai_api_key = openai_api_key
        self.deepl_api_key = deepl_api_key
        
        # إعداد Google Translator
        if GOOGLETRANS_AVAILABLE:
            try:
                self.google_translator = GoogleTranslator()
                self.logger.info("تم تهيئة مترجم Google بنجاح")
            except Exception as e:
                self.logger.warning(f"فشل في تهيئة مترجم Google: {str(e)}")
                self.google_translator = None
        else:
            self.google_translator = None
        
        # إعداد OpenAI
        if OPENAI_AVAILABLE and openai_api_key:
            openai.api_key = openai_api_key
            self.logger.info("تم تهيئة OpenAI للترجمة")
        
        # قاموس اللغات المدعومة
        self.supported_languages = {
            'ar': 'العربية',
            'en': 'English',
            'fr': 'Français',
            'de': 'Deutsch',
            'es': 'Español',
            'it': 'Italiano',
            'ru': 'Русский',
            'ja': '日本語',
            'ko': '한국어',
            'zh': '中文',
            'pt': 'Português',
            'tr': 'Türkçe',
            'hi': 'हिन्दी',
            'ur': 'اردو'
        }
    
    def detect_language(self, text: str, service: str = 'google') -> str:
        """
        كشف لغة النص
        
        Args:
            text: النص المراد كشف لغته
            service: خدمة الكشف ('google', 'openai')
            
        Returns:
            str: رمز اللغة
        """
        try:
            if service == 'google' and self.google_translator:
                detection = self.google_translator.detect(text)
                return detection.lang
            elif service == 'openai' and OPENAI_AVAILABLE and self.openai_api_key:
                return self._detect_language_openai(text)
            else:
                # كشف بسيط بناءً على الأحرف
                return self._simple_language_detection(text)
                
        except Exception as e:
            self.logger.error(f"خطأ في كشف اللغة: {str(e)}")
            return 'unknown'
    
    def _detect_language_openai(self, text: str) -> str:
        """كشف اللغة باستخدام OpenAI"""
        try:
            response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a language detection expert. Respond only with the ISO 639-1 language code."},
                    {"role": "user", "content": f"What language is this text: '{text[:200]}'"}
                ],
                max_tokens=10,
                temperature=0
            )
            
            return response.choices[0].message.content.strip().lower()
            
        except Exception as e:
            self.logger.error(f"خطأ في كشف اللغة بـ OpenAI: {str(e)}")
            return 'unknown'
    
    def _simple_language_detection(self, text: str) -> str:
        """كشف بسيط للغة بناءً على الأحرف"""
        # كشف العربية
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        if arabic_chars > len(text) * 0.3:
            return 'ar'
        
        # كشف الإنجليزية (افتراضي)
        return 'en'
    
    def translate_text(self, text: str, target_language: str, 
                      source_language: str = 'auto', service: str = 'google',
                      progress_callback: Callable = None) -> dict:
        """
        ترجمة النص
        
        Args:
            text: النص المراد ترجمته
            target_language: اللغة الهدف
            source_language: اللغة المصدر (auto للكشف التلقائي)
            service: خدمة الترجمة ('google', 'deepl', 'openai')
            progress_callback: دالة تحديث التقدم
            
        Returns:
            dict: النتيجة مع النص المترجم ومعلومات إضافية
        """
        try:
            self.logger.operation_start("ترجمة النص", f"{service} -> {target_language}")
            
            if progress_callback:
                progress_callback(10)
            
            # كشف اللغة المصدر إذا لزم الأمر
            if source_language == 'auto':
                source_language = self.detect_language(text, service)
            
            if progress_callback:
                progress_callback(30)
            
            # الترجمة حسب الخدمة المحددة
            translated_text = ""
            
            if service == 'google':
                translated_text = self._translate_google(text, target_language, source_language)
            elif service == 'deepl':
                translated_text = self._translate_deepl(text, target_language, source_language)
            elif service == 'openai':
                translated_text = self._translate_openai(text, target_language, source_language)
            else:
                raise Exception(f"خدمة الترجمة غير مدعومة: {service}")
            
            if progress_callback:
                progress_callback(100)
            
            result = {
                'original_text': text,
                'translated_text': translated_text,
                'source_language': source_language,
                'target_language': target_language,
                'service': service,
                'success': bool(translated_text)
            }
            
            self.logger.operation_complete("ترجمة النص", f"{service} -> {target_language}", 0)
            return result
            
        except Exception as e:
            self.logger.operation_error("ترجمة النص", f"{service} -> {target_language}", str(e))
            return {
                'original_text': text,
                'translated_text': '',
                'source_language': source_language,
                'target_language': target_language,
                'service': service,
                'success': False,
                'error': str(e)
            }
    
    def _translate_google(self, text: str, target_lang: str, source_lang: str) -> str:
        """ترجمة باستخدام Google Translate"""
        if not self.google_translator:
            if DEEP_TRANSLATOR_AVAILABLE:
                # استخدام deep-translator كبديل
                translator = DeepGoogleTranslator(source=source_lang, target=target_lang)
                return translator.translate(text)
            else:
                raise Exception("مترجم Google غير متاح")
        
        try:
            result = self.google_translator.translate(
                text, 
                src=source_lang if source_lang != 'auto' else None,
                dest=target_lang
            )
            return result.text
        except Exception as e:
            # إعادة المحاولة مع تأخير
            time.sleep(1)
            result = self.google_translator.translate(
                text,
                src=source_lang if source_lang != 'auto' else None,
                dest=target_lang
            )
            return result.text
    
    def _translate_deepl(self, text: str, target_lang: str, source_lang: str) -> str:
        """ترجمة باستخدام DeepL"""
        if not DEEP_TRANSLATOR_AVAILABLE or not self.deepl_api_key:
            raise Exception("DeepL غير متاح أو مفتاح API غير محدد")
        
        translator = DeepL(
            api_key=self.deepl_api_key,
            source=source_lang if source_lang != 'auto' else 'auto',
            target=target_lang
        )
        
        return translator.translate(text)
    
    def _translate_openai(self, text: str, target_lang: str, source_lang: str) -> str:
        """ترجمة باستخدام OpenAI"""
        if not OPENAI_AVAILABLE or not self.openai_api_key:
            raise Exception("OpenAI غير متاح أو مفتاح API غير محدد")
        
        target_lang_name = self.supported_languages.get(target_lang, target_lang)
        source_lang_name = self.supported_languages.get(source_lang, source_lang)
        
        prompt = f"Translate the following text from {source_lang_name} to {target_lang_name}. Only provide the translation, no explanations:\n\n{text}"
        
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": "You are a professional translator. Provide accurate translations without any additional commentary."},
                {"role": "user", "content": prompt}
            ],
            max_tokens=len(text) * 2,  # تقدير تقريبي
            temperature=0.3
        )
        
        return response.choices[0].message.content.strip()
    
    def translate_segments(self, segments: list[dict], target_language: str,
                          source_language: str = 'auto', service: str = 'google',
                          progress_callback: Callable = None) -> list[dict]:
        """
        ترجمة مقاطع النص مع الحفاظ على التوقيتات
        
        Args:
            segments: قائمة المقاطع مع التوقيتات
            target_language: اللغة الهدف
            source_language: اللغة المصدر
            service: خدمة الترجمة
            progress_callback: دالة تحديث التقدم
            
        Returns:
            list[dict]: المقاطع المترجمة
        """
        try:
            self.logger.info(f"ترجمة {len(segments)} مقطع إلى {target_language}")
            
            translated_segments = []
            total_segments = len(segments)
            
            for i, segment in enumerate(segments):
                # ترجمة النص
                translation_result = self.translate_text(
                    segment['text'],
                    target_language,
                    source_language,
                    service
                )
                
                # إنشاء مقطع مترجم
                translated_segment = segment.copy()
                translated_segment['original_text'] = segment['text']
                translated_segment['text'] = translation_result['translated_text']
                translated_segment['translation_service'] = service
                
                translated_segments.append(translated_segment)
                
                # تحديث التقدم
                if progress_callback:
                    progress = int((i + 1) / total_segments * 100)
                    progress_callback(progress)
                
                # تأخير قصير لتجنب تجاوز حدود API
                time.sleep(0.1)
            
            self.logger.info(f"تم ترجمة {len(translated_segments)} مقطع بنجاح")
            return translated_segments
            
        except Exception as e:
            self.logger.error(f"خطأ في ترجمة المقاطع: {str(e)}")
            return segments  # إرجاع المقاطع الأصلية في حالة الخطأ
    
    def batch_translate(self, texts: list[str], target_language: str,
                       source_language: str = 'auto', service: str = 'google',
                       batch_size: int = 10, progress_callback: Callable = None) -> list[str]:
        """
        ترجمة مجموعة من النصوص
        
        Args:
            texts: قائمة النصوص
            target_language: اللغة الهدف
            source_language: اللغة المصدر
            service: خدمة الترجمة
            batch_size: حجم الدفعة
            progress_callback: دالة تحديث التقدم
            
        Returns:
            list[str]: النصوص المترجمة
        """
        try:
            translated_texts = []
            total_texts = len(texts)
            
            for i in range(0, total_texts, batch_size):
                batch = texts[i:i + batch_size]
                
                for text in batch:
                    result = self.translate_text(text, target_language, source_language, service)
                    translated_texts.append(result['translated_text'])
                
                # تحديث التقدم
                if progress_callback:
                    progress = int(min(i + batch_size, total_texts) / total_texts * 100)
                    progress_callback(progress)
                
                # تأخير بين الدفعات
                time.sleep(0.5)
            
            return translated_texts
            
        except Exception as e:
            self.logger.error(f"خطأ في الترجمة المجمعة: {str(e)}")
            return texts  # إرجاع النصوص الأصلية في حالة الخطأ
    
    def get_supported_languages(self, service: str = 'google') -> dict[str, str]:
        """
        الحصول على قائمة اللغات المدعومة
        
        Args:
            service: خدمة الترجمة
            
        Returns:
            dict[str, str]: قاموس اللغات (رمز: اسم)
        """
        if service == 'google' and GOOGLETRANS_AVAILABLE:
            try:
                from googletrans import LANGUAGES
                return LANGUAGES
            except:
                pass
        
        # إرجاع القائمة الأساسية
        return self.supported_languages
    
    def save_translation(self, translation_result: dict, output_path: str) -> bool:
        """
        حفظ نتيجة الترجمة في ملف
        
        Args:
            translation_result: نتيجة الترجمة
            output_path: مسار الحفظ
            
        Returns:
            bool: True إذا تم الحفظ بنجاح
        """
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(f"النص الأصلي ({translation_result['source_language']}):\n")
                f.write(f"{translation_result['original_text']}\n\n")
                f.write(f"النص المترجم ({translation_result['target_language']}):\n")
                f.write(f"{translation_result['translated_text']}\n\n")
                f.write(f"خدمة الترجمة: {translation_result['service']}\n")
            
            self.logger.info(f"تم حفظ الترجمة في: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في حفظ الترجمة: {str(e)}")
            return False
