# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات
Database Manager for Video Editor Application
"""

import sqlite3
import logging
from pathlib import Path
from datetime import datetime
from typing import list, dict, Optional
import json

class DatabaseManager:
    """مدير قاعدة البيانات لحفظ معلومات الفيديوهات والعمليات"""
    
    def __init__(self, db_path: str = None):
        """
        تهيئة مدير قاعدة البيانات
        
        Args:
            db_path: مسار قاعدة البيانات (اختياري)
        """
        if db_path is None:
            db_path = Path(__file__).parent.parent / "video_editor.db"
        
        self.db_path = str(db_path)
        self.logger = logging.getLogger(__name__)
        
    def initialize_database(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول الفيديوهات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS videos (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        original_filename TEXT NOT NULL,
                        file_path TEXT NOT NULL,
                        file_size INTEGER,
                        duration REAL,
                        resolution TEXT,
                        fps REAL,
                        format TEXT,
                        source_type TEXT, -- 'local', 'youtube', 'tiktok', 'facebook'
                        source_url TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # جدول العمليات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS operations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        video_id INTEGER,
                        operation_type TEXT NOT NULL, -- 'cut', 'translate', 'dub', 'montage', 'face_track'
                        operation_data TEXT, -- JSON data for operation parameters
                        status TEXT DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed'
                        progress INTEGER DEFAULT 0,
                        output_path TEXT,
                        error_message TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        completed_at TIMESTAMP,
                        FOREIGN KEY (video_id) REFERENCES videos (id)
                    )
                ''')
                
                # جدول الترجمات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS translations (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        video_id INTEGER,
                        source_language TEXT,
                        target_language TEXT,
                        original_text TEXT,
                        translated_text TEXT,
                        translation_service TEXT, -- 'google', 'deepl'
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (video_id) REFERENCES videos (id)
                    )
                ''')
                
                # جدول الدبلجة
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS dubbing (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        video_id INTEGER,
                        language TEXT,
                        text TEXT,
                        audio_path TEXT,
                        voice_service TEXT, -- 'gtts', 'elevenlabs', 'pyttsx3'
                        voice_settings TEXT, -- JSON for voice parameters
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (video_id) REFERENCES videos (id)
                    )
                ''')
                
                # جدول إعدادات التطبيق
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS app_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        setting_key TEXT UNIQUE NOT NULL,
                        setting_value TEXT,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                conn.commit()
                self.logger.info("تم إنشاء قاعدة البيانات بنجاح")
                
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء قاعدة البيانات: {str(e)}")
            raise
    
    def add_video(self, video_data: dict) -> int:
        """إضافة فيديو جديد إلى قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO videos (
                        original_filename, file_path, file_size, duration,
                        resolution, fps, format, source_type, source_url
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    video_data.get('original_filename'),
                    video_data.get('file_path'),
                    video_data.get('file_size'),
                    video_data.get('duration'),
                    video_data.get('resolution'),
                    video_data.get('fps'),
                    video_data.get('format'),
                    video_data.get('source_type'),
                    video_data.get('source_url')
                ))
                
                video_id = cursor.lastrowid
                conn.commit()
                
                self.logger.info(f"تم إضافة فيديو جديد برقم: {video_id}")
                return video_id
                
        except Exception as e:
            self.logger.error(f"خطأ في إضافة الفيديو: {str(e)}")
            raise
    
    def add_operation(self, operation_data: dict) -> int:
        """إضافة عملية جديدة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO operations (
                        video_id, operation_type, operation_data, status
                    ) VALUES (?, ?, ?, ?)
                ''', (
                    operation_data.get('video_id'),
                    operation_data.get('operation_type'),
                    json.dumps(operation_data.get('operation_data', {})),
                    operation_data.get('status', 'pending')
                ))
                
                operation_id = cursor.lastrowid
                conn.commit()
                
                return operation_id
                
        except Exception as e:
            self.logger.error(f"خطأ في إضافة العملية: {str(e)}")
            raise
    
    def update_operation_status(self, operation_id: int, status: str, 
                              progress: int = None, output_path: str = None,
                              error_message: str = None):
        """تحديث حالة العملية"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                update_fields = ["status = ?"]
                values = [status]
                
                if progress is not None:
                    update_fields.append("progress = ?")
                    values.append(progress)
                
                if output_path:
                    update_fields.append("output_path = ?")
                    values.append(output_path)
                
                if error_message:
                    update_fields.append("error_message = ?")
                    values.append(error_message)
                
                if status == 'completed':
                    update_fields.append("completed_at = ?")
                    values.append(datetime.now().isoformat())
                
                values.append(operation_id)
                
                query = f"UPDATE operations SET {', '.join(update_fields)} WHERE id = ?"
                cursor.execute(query, values)
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"خطأ في تحديث حالة العملية: {str(e)}")
            raise
    
    def get_videos(self) -> list[dict]:
        """الحصول على قائمة الفيديوهات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("SELECT * FROM videos ORDER BY created_at DESC")
                rows = cursor.fetchall()
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الفيديوهات: {str(e)}")
            return []
    
    def get_operations_by_video(self, video_id: int) -> list[dict]:
        """الحصول على العمليات الخاصة بفيديو معين"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute(
                    "SELECT * FROM operations WHERE video_id = ? ORDER BY created_at DESC",
                    (video_id,)
                )
                rows = cursor.fetchall()
                
                operations = []
                for row in rows:
                    operation = dict(row)
                    if operation['operation_data']:
                        operation['operation_data'] = json.loads(operation['operation_data'])
                    operations.append(operation)
                
                return operations
                
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على العمليات: {str(e)}")
            return []
