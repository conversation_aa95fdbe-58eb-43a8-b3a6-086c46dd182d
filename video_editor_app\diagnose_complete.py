#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص شامل للتطبيق - Complete Application Diagnosis
يكشف جميع المشاكل ويقدم الحلول
"""

import sys
import os
import ast
import traceback
import subprocess
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_python_environment():
    """فحص بيئة Python"""
    print("🐍 فحص بيئة Python...")
    
    issues = []
    
    # فحص إصدار Python
    if sys.version_info < (3, 8):
        issues.append(f"إصدار Python قديم: {sys.version}")
        print(f"  ❌ إصدار Python: {sys.version}")
    else:
        print(f"  ✅ إصدار Python: {sys.version_info.major}.{sys.version_info.minor}")
    
    # فحص مسار Python
    print(f"  📍 مسار Python: {sys.executable}")
    
    # فحص المكتبات الأساسية
    basic_modules = ['os', 'sys', 'pathlib', 'json', 'sqlite3']
    for module in basic_modules:
        try:
            __import__(module)
            print(f"  ✅ {module}")
        except ImportError:
            issues.append(f"مكتبة أساسية مفقودة: {module}")
            print(f"  ❌ {module}")
    
    return issues

def check_pyqt6_installation():
    """فحص تثبيت PyQt6"""
    print("\n🖥️ فحص PyQt6...")
    
    issues = []
    
    try:
        import PyQt6
        print(f"  ✅ PyQt6 متاح - الإصدار: {PyQt6.QtCore.PYQT_VERSION_STR}")
        
        # فحص الوحدات الفرعية
        submodules = ['QtWidgets', 'QtCore', 'QtGui']
        for submodule in submodules:
            try:
                __import__(f'PyQt6.{submodule}')
                print(f"  ✅ PyQt6.{submodule}")
            except ImportError as e:
                issues.append(f"وحدة PyQt6 مفقودة: {submodule}")
                print(f"  ❌ PyQt6.{submodule}: {e}")
        
        # اختبار إنشاء QApplication
        try:
            from PyQt6.QtWidgets import QApplication
            app = QApplication.instance()
            if app is None:
                app = QApplication([])
            print("  ✅ يمكن إنشاء QApplication")
        except Exception as e:
            issues.append(f"خطأ في إنشاء QApplication: {e}")
            print(f"  ❌ QApplication: {e}")
            
    except ImportError as e:
        issues.append(f"PyQt6 غير مثبت: {e}")
        print(f"  ❌ PyQt6 غير متاح: {e}")
    
    return issues

def check_project_structure():
    """فحص هيكل المشروع"""
    print("\n📁 فحص هيكل المشروع...")
    
    issues = []
    
    required_files = [
        "main.py",
        "gui/__init__.py",
        "gui/main_window.py",
        "config.py"
    ]
    
    required_dirs = [
        "gui",
        "src", 
        "utils",
        "database"
    ]
    
    # فحص الملفات المطلوبة
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"  ✅ {file_path}")
        else:
            issues.append(f"ملف مفقود: {file_path}")
            print(f"  ❌ {file_path}")
    
    # فحص المجلدات المطلوبة
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        if full_path.exists() and full_path.is_dir():
            print(f"  ✅ {dir_path}/")
        else:
            issues.append(f"مجلد مفقود: {dir_path}")
            print(f"  ❌ {dir_path}/")
    
    return issues

def check_syntax_errors():
    """فحص أخطاء الصيغة"""
    print("\n🔍 فحص أخطاء الصيغة...")
    
    issues = []
    
    python_files = [
        "main.py",
        "gui/__init__.py",
        "gui/main_window.py"
    ]
    
    for file_path in python_files:
        full_path = project_root / file_path
        if not full_path.exists():
            continue
            
        try:
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # اختبار الصيغة
            ast.parse(content)
            print(f"  ✅ {file_path}")
            
        except SyntaxError as e:
            error_msg = f"خطأ صيغة في {file_path} السطر {e.lineno}: {e.msg}"
            issues.append(error_msg)
            print(f"  ❌ {error_msg}")
        except Exception as e:
            error_msg = f"خطأ في {file_path}: {e}"
            issues.append(error_msg)
            print(f"  ❌ {error_msg}")
    
    return issues

def check_import_errors():
    """فحص أخطاء الاستيراد"""
    print("\n📦 فحص أخطاء الاستيراد...")
    
    issues = []
    
    import_tests = [
        ("PyQt6.QtWidgets", "from PyQt6.QtWidgets import QApplication"),
        ("PyQt6.QtCore", "from PyQt6.QtCore import Qt"),
        ("gui", "import gui"),
        ("gui.main_window", "from gui.main_window import MainWindow")
    ]
    
    for name, import_cmd in import_tests:
        try:
            exec(import_cmd)
            print(f"  ✅ {name}")
        except ImportError as e:
            error_msg = f"خطأ استيراد {name}: {e}"
            issues.append(error_msg)
            print(f"  ❌ {error_msg}")
        except Exception as e:
            error_msg = f"خطأ في {name}: {e}"
            issues.append(error_msg)
            print(f"  ❌ {error_msg}")
    
    return issues

def check_window_creation():
    """فحص إنشاء النافذة"""
    print("\n🖼️ فحص إنشاء النافذة...")
    
    issues = []
    
    try:
        from PyQt6.QtWidgets import QApplication
        from gui.main_window import MainWindow
        
        # إنشاء تطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("  ✅ إنشاء QApplication")
        
        # إنشاء النافذة
        window = MainWindow()
        print("  ✅ إنشاء MainWindow")
        
        # اختبار خصائص النافذة
        window.setWindowTitle("اختبار")
        window.resize(800, 600)
        print("  ✅ تعيين خصائص النافذة")
        
    except Exception as e:
        error_msg = f"خطأ في إنشاء النافذة: {e}"
        issues.append(error_msg)
        print(f"  ❌ {error_msg}")
        traceback.print_exc()
    
    return issues

def suggest_solutions(all_issues):
    """اقتراح الحلول"""
    print("\n💡 الحلول المقترحة:")
    
    if not all_issues:
        print("  🎉 لا توجد مشاكل - التطبيق جاهز للتشغيل!")
        return
    
    # تصنيف المشاكل
    pyqt6_issues = [issue for issue in all_issues if "PyQt6" in issue]
    syntax_issues = [issue for issue in all_issues if "صيغة" in issue or "IndentationError" in issue]
    import_issues = [issue for issue in all_issues if "استيراد" in issue]
    file_issues = [issue for issue in all_issues if "مفقود" in issue]
    
    if pyqt6_issues:
        print("  📦 لحل مشاكل PyQt6:")
        print("    pip install --upgrade PyQt6")
        print("    python -m pip install PyQt6 --force-reinstall")
    
    if syntax_issues:
        print("  🔧 لحل أخطاء الصيغة:")
        print("    python ultimate_fix.py")
        print("    python fix_indentation_final.py")
    
    if import_issues:
        print("  📥 لحل أخطاء الاستيراد:")
        print("    python ultimate_fix.py")
        print("    python fix_all_errors.py")
    
    if file_issues:
        print("  📁 لحل الملفات المفقودة:")
        print("    python ultimate_fix.py")
    
    print("\n🚀 للتشغيل المضمون:")
    print("  python run_perfect.py")
    print("  python main_ultimate_safe.py")

def generate_report(all_issues):
    """إنشاء تقرير شامل"""
    print("\n" + "="*60)
    print("📊 التقرير الشامل")
    print("="*60)
    
    total_issues = len(all_issues)
    
    if total_issues == 0:
        print("🎉 ممتاز! لا توجد مشاكل")
        print("✅ التطبيق جاهز للتشغيل بسلاسة")
        print("✅ جميع المكونات تعمل بشكل مثالي")
        
        print("\n🚀 يمكنك تشغيل التطبيق الآن:")
        print("  python main.py")
        
        return True
    else:
        print(f"⚠️ تم العثور على {total_issues} مشكلة")
        
        print(f"\n📋 قائمة المشاكل:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        
        # تحديد مستوى الخطورة
        critical_keywords = ["PyQt6 غير مثبت", "ملف مفقود: main.py", "خطأ صيغة في main.py"]
        critical_issues = [issue for issue in all_issues if any(keyword in issue for keyword in critical_keywords)]
        
        if critical_issues:
            print(f"\n🔴 مشاكل حرجة ({len(critical_issues)}):")
            for issue in critical_issues:
                print(f"  • {issue}")
            print("💡 يجب حل هذه المشاكل أولاً")
        
        return False

def main():
    """الدالة الرئيسية للتشخيص الشامل"""
    print("🔍 التشخيص الشامل لمعالج الفيديوهات المتكامل")
    print("="*60)
    print("🎯 الهدف: اكتشاف جميع المشاكل وتقديم الحلول")
    print("="*60)
    
    all_issues = []
    
    try:
        # 1. فحص بيئة Python
        all_issues.extend(check_python_environment())
        
        # 2. فحص PyQt6
        all_issues.extend(check_pyqt6_installation())
        
        # 3. فحص هيكل المشروع
        all_issues.extend(check_project_structure())
        
        # 4. فحص أخطاء الصيغة
        all_issues.extend(check_syntax_errors())
        
        # 5. فحص أخطاء الاستيراد
        all_issues.extend(check_import_errors())
        
        # 6. فحص إنشاء النافذة
        all_issues.extend(check_window_creation())
        
        # 7. اقتراح الحلول
        suggest_solutions(all_issues)
        
        # 8. إنشاء التقرير النهائي
        success = generate_report(all_issues)
        
        return success
        
    except Exception as e:
        print(f"\n❌ خطأ في التشخيص: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = main()
        input("\nاضغط Enter للخروج...")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التشخيص")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ كارثي: {e}")
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
