@echo off
chcp 65001 >nul
title معالج الفيديوهات المتكامل - Video Editor Pro

echo ========================================
echo 🎬 معالج الفيديوهات المتكامل
echo    Video Editor Pro
echo ========================================
echo.

:: التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    pause
    exit /b 1
)

echo ✅ تم العثور على Python
python --version

:: التحقق من وجود pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip غير متوفر
    pause
    exit /b 1
)

echo ✅ pip متوفر

:: التحقق من وجود ملف المتطلبات
if not exist "requirements.txt" (
    echo ❌ ملف requirements.txt غير موجود
    pause
    exit /b 1
)

echo.
echo 📦 تثبيت المتطلبات الأساسية...

:: تثبيت المتطلبات الأساسية أولاً
echo تثبيت المتطلبات الأساسية...
pip install PyQt6 moviepy opencv-python requests sqlalchemy Pillow tqdm
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات الأساسية
    echo جاري المحاولة مع ملف requirements-minimal.txt...
    pip install -r requirements-minimal.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

echo.
echo 📦 تثبيت المتطلبات الإضافية (اختيارية)...
pip install qdarkstyle
if errorlevel 1 (
    echo ⚠️ لم يتم تثبيت qdarkstyle - سيتم استخدام الستايل الافتراضي
)

echo ✅ تم تثبيت جميع المتطلبات

echo.
echo 🚀 تشغيل التطبيق...
echo.

:: تشغيل التطبيق
python main.py

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل التطبيق
    pause
    exit /b 1
)

echo.
echo ✅ تم إغلاق التطبيق بنجاح
pause
