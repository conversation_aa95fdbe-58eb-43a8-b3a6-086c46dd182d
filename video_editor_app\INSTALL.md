# دليل التثبيت - Installation Guide

## 🚀 التثبيت السريع

### Windows

#### الطريقة الأولى: ملف EXE (الأسهل)
1. حمل ملف `VideoEditorPro.exe` من [الإصدارات](https://github.com/your-repo/releases)
2. شغل الملف مباشرة - لا يحتاج تثبيت!

#### الطريقة الثانية: من المصدر
```cmd
# 1. تحميل المشروع
git clone https://github.com/your-username/video-editor-app.git
cd video-editor-app

# 2. تشغيل سكريپت التثبيت
start.bat
```

### macOS

```bash
# 1. تحميل المشروع
git clone https://github.com/your-username/video-editor-app.git
cd video-editor-app

# 2. تشغيل سكريپت التثبيت
chmod +x start.sh
./start.sh
```

### Linux

```bash
# 1. تحميل المشروع
git clone https://github.com/your-username/video-editor-app.git
cd video-editor-app

# 2. تشغيل سكريپت التثبيت
chmod +x start.sh
./start.sh
```

## 📋 المتطلبات

### متطلبات النظام
- **Python**: 3.8 أو أحدث
- **نظام التشغيل**: Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **الذاكرة**: 4 GB RAM (8 GB موصى به)
- **التخزين**: 2 GB مساحة فارغة (5 GB موصى به)
- **الإنترنت**: مطلوب للتحميل والترجمة

### متطلبات إضافية
- **FFmpeg**: مطلوب لمعالجة الفيديو
- **Git**: لتحميل المشروع من GitHub

## 🔧 التثبيت المفصل

### 1. تثبيت Python

#### Windows
1. اذهب إلى [python.org](https://python.org)
2. حمل Python 3.8+ 
3. **مهم**: اختر "Add Python to PATH" أثناء التثبيت

#### macOS
```bash
# باستخدام Homebrew
brew install python3

# أو حمل من python.org
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

### 2. تثبيت FFmpeg

#### Windows
```cmd
# باستخدام Chocolatey
choco install ffmpeg

# أو حمل من https://ffmpeg.org/download.html
```

#### macOS
```bash
# باستخدام Homebrew
brew install ffmpeg
```

#### Linux
```bash
# Ubuntu/Debian
sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg
```

### 3. تحميل المشروع

```bash
# باستخدام Git
git clone https://github.com/your-username/video-editor-app.git
cd video-editor-app

# أو حمل ZIP من GitHub وفك الضغط
```

### 4. إعداد البيئة الافتراضية

```bash
# إنشاء بيئة افتراضية
python -m venv video_editor_env

# تفعيل البيئة
# Windows
video_editor_env\Scripts\activate

# macOS/Linux
source video_editor_env/bin/activate
```

### 5. تثبيت المتطلبات

```bash
# تثبيت جميع المكتبات المطلوبة
pip install -r requirements.txt
```

### 6. اختبار التثبيت

```bash
# تشغيل اختبارات التطبيق
python test_app.py

# تشغيل التطبيق
python main.py
```

## ⚙️ إعداد مفاتيح API (اختياري)

لاستخدام الميزات المتقدمة، أضف مفاتيح API:

### 1. OpenAI API (للدبلجة وتفريغ الصوت)
1. اذهب إلى [platform.openai.com](https://platform.openai.com)
2. أنشئ حساب واحصل على API key
3. في التطبيق: الإعدادات → API Keys → OpenAI

### 2. ElevenLabs API (للدبلجة الواقعية)
1. اذهب إلى [elevenlabs.io](https://elevenlabs.io)
2. أنشئ حساب واحصل على API key
3. في التطبيق: الإعدادات → API Keys → ElevenLabs

### 3. DeepL API (للترجمة عالية الجودة)
1. اذهب إلى [deepl.com/pro](https://deepl.com/pro)
2. أنشئ حساب واحصل على API key
3. في التطبيق: الإعدادات → API Keys → DeepL

## 🐛 حل المشاكل الشائعة

### مشكلة: "Python not found"
**الحل:**
```bash
# تأكد من تثبيت Python وإضافته لـ PATH
python --version
# أو
python3 --version
```

### مشكلة: "pip not found"
**الحل:**
```bash
# Windows
python -m ensurepip --upgrade

# macOS/Linux
sudo apt install python3-pip
```

### مشكلة: "FFmpeg not found"
**الحل:**
- تأكد من تثبيت FFmpeg
- أضف FFmpeg إلى PATH
- أعد تشغيل Terminal/CMD

### مشكلة: "Permission denied"
**الحل:**
```bash
# Linux/macOS
chmod +x start.sh
sudo chmod +x start.sh

# أو استخدم sudo
sudo python main.py
```

### مشكلة: "Module not found"
**الحل:**
```bash
# تأكد من تفعيل البيئة الافتراضية
source video_editor_env/bin/activate  # macOS/Linux
video_editor_env\Scripts\activate     # Windows

# إعادة تثبيت المتطلبات
pip install -r requirements.txt --force-reinstall
```

### مشكلة: "Qt platform plugin"
**الحل:**
```bash
# Linux
sudo apt install python3-pyqt6
export QT_QPA_PLATFORM=xcb

# أو
pip install PyQt6 --force-reinstall
```

## 🔄 التحديث

### تحديث من Git
```bash
cd video-editor-app
git pull origin main
pip install -r requirements.txt --upgrade
```

### تحديث ملف EXE
1. حمل الإصدار الجديد من GitHub Releases
2. استبدل الملف القديم

## 🗑️ إلغاء التثبيت

### إزالة البيئة الافتراضية
```bash
# إلغاء تفعيل البيئة
deactivate

# حذف مجلد البيئة
rm -rf video_editor_env  # macOS/Linux
rmdir /s video_editor_env  # Windows
```

### إزالة المشروع
```bash
# حذف مجلد المشروع
rm -rf video-editor-app  # macOS/Linux
rmdir /s video-editor-app  # Windows
```

## 📱 تثبيت على أنظمة أخرى

### Raspberry Pi
```bash
# تثبيت المتطلبات الإضافية
sudo apt install python3-dev python3-pip
sudo apt install libatlas-base-dev libhdf5-dev
sudo apt install python3-pyqt6

# تثبيت التطبيق
git clone https://github.com/your-username/video-editor-app.git
cd video-editor-app
pip3 install -r requirements.txt
```

### Docker (متقدم)
```dockerfile
FROM python:3.10-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "main.py"]
```

## 🎯 التحقق من التثبيت

بعد التثبيت، تأكد من:

- [ ] Python يعمل: `python --version`
- [ ] pip يعمل: `pip --version`
- [ ] FFmpeg يعمل: `ffmpeg -version`
- [ ] المكتبات مثبتة: `pip list`
- [ ] التطبيق يعمل: `python main.py`

## 📞 الحصول على المساعدة

إذا واجهت مشاكل:

1. **راجع هذا الدليل** مرة أخرى
2. **ابحث في Issues** على GitHub
3. **أنشئ Issue جديد** مع تفاصيل المشكلة
4. **انضم لـ Discord** للمساعدة المباشرة

---

**نصيحة**: احفظ هذا الدليل للرجوع إليه لاحقاً! 📖
