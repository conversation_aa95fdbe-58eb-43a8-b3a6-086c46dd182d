# -*- coding: utf-8 -*-
"""
وحدة الأدوات المساعدة
Utilities Module for Video Editor Application
"""

try:
    from .logger import setup_logger, VideoEditorLogger
    __all__ = ['setup_logger', 'VideoEditorLogger']
except ImportError as e:
    print(f"⚠️ خطأ في استيراد نظام السجلات: {e}")

    class VideoEditorLogger:
        def __init__(self, name):
            self.name = name
        def info(self, msg): print(f"INFO: {msg}")
        def error(self, msg): print(f"ERROR: {msg}")
        def warning(self, msg): print(f"WARNING: {msg}")
        def debug(self, msg): print(f"DEBUG: {msg}")

    def setup_logger():
        return VideoEditorLogger("default")

    __all__ = ['setup_logger', 'VideoEditorLogger']
