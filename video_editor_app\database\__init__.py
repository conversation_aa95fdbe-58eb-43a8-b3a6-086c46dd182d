# -*- coding: utf-8 -*-
"""
وحدة قاعدة البيانات
Database Module for Video Editor Application
"""

try:
    from .db_manager import DatabaseManager
    __all__ = ['DatabaseManager']
except ImportError as e:
    print(f"⚠️ خطأ في استيراد قاعدة البيانات: {e}")

    class DatabaseManager:
        def __init__(self):
            pass
        def initialize_database(self):
            pass
        def save_video_info(self, *args):
            pass
        def get_video_info(self, *args):
            return {}

    __all__ = ['DatabaseManager']
