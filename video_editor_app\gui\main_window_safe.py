#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة رئيسية آمنة - Safe Main Window
نسخة محسنة لتجنب التجمد
"""

import sys
import os
from pathlib import Path
from typing import Dict, List, Optional, Any, Union

# استيراد PyQt6 مع معالجة الأخطاء
try:
    from PyQt6.QtWidgets import (
        QApplication,
        QCheckBox,
        QComboBox,
        QFileDialog,
        QFrame,
        QGridLayout,
        QGroupBox,
        QHBoxLayout,
        QInputDialog,
        QLabel,
        QLineEdit,
        QListWidget,
        QMainWindow,
        QMessageBox,
        QProgressBar,
        QPushButton,
        QScrollArea,
        QSizePolicy,
        QSlider,
        QSpinBox,
        QSplitter,
        QTabWidget,
        QTextEdit,
        QVBoxLayout,
        QWidget
    )
    from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
    from PyQt6.QtGui import QFont, QPixmap, QIcon
    PYQT6_AVAILABLE = True
except ImportError as e:
    print(f"❌ خطأ في استيراد PyQt6: {e}")
    PYQT6_AVAILABLE = False
    raise

class SafeMainWindow(QMainWindow):
    """النافذة الرئيسية الآمنة"""
    
    def __init__(self):
        """تهيئة آمنة للنافذة الرئيسية"""
        super().__init__()
        
        print("🔧 بدء تهيئة النافذة الآمنة...")
        
        # متغيرات أساسية
        self.video_files = []
        self.current_video_info = {}
        self.processing_thread = None
        
        # تهيئة تدريجية آمنة
        try:
            self.setup_basic_window()
            print("✅ تم إعداد النافذة الأساسية")
            
            self.setup_ui()
            print("✅ تم إعداد الواجهة")
            
            self.setup_connections()
            print("✅ تم ربط الإشارات")
            
            print("🎉 تم إنجاز تهيئة النافذة الآمنة")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة النافذة: {e}")
            self.setup_emergency_ui()
    
    def setup_basic_window(self):
        """إعداد النافذة الأساسية"""
        self.setWindowTitle("معالج الفيديوهات المتكامل - الوضع الآمن")
        self.setGeometry(100, 100, 1200, 800)
        
        # إعداد الخط
        font = QFont("Arial", 10)
        self.setFont(font)
    
    def setup_ui(self):
        """إعداد واجهة المستخدم الآمنة"""
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        
        # شريط علوي
        self.create_top_toolbar(main_layout)
        
        # التبويبات الرئيسية
        self.create_main_tabs(main_layout)
        
        # شريط الحالة
        self.create_status_bar()
    
    def create_top_toolbar(self, main_layout):
        """إنشاء شريط الأدوات العلوي"""
        toolbar_layout = QHBoxLayout()
        
        # أزرار سريعة
        new_project_btn = QPushButton("🆕 مشروع جديد")
        new_project_btn.clicked.connect(self.new_project)
        toolbar_layout.addWidget(new_project_btn)
        
        open_file_btn = QPushButton("📁 فتح ملف")
        open_file_btn.clicked.connect(self.open_file)
        toolbar_layout.addWidget(open_file_btn)
        
        download_btn = QPushButton("⬇️ تحميل فيديو")
        download_btn.clicked.connect(self.download_video)
        toolbar_layout.addWidget(download_btn)
        
        toolbar_layout.addStretch()
        
        settings_btn = QPushButton("⚙️ الإعدادات")
        settings_btn.clicked.connect(self.show_settings)
        toolbar_layout.addWidget(settings_btn)
        
        main_layout.addLayout(toolbar_layout)
    
    def create_main_tabs(self, main_layout):
        """إنشاء التبويبات الرئيسية"""
        self.tab_widget = QTabWidget()
        
        # تبويب معالجة الفيديوهات
        self.create_video_processing_tab()
        
        # تبويب الذكاء اللغوي
        self.create_language_ai_tab()
        
        # تبويب المشاريع
        self.create_projects_tab()
        
        # تبويب الإعدادات
        self.create_settings_tab()
        
        main_layout.addWidget(self.tab_widget)
    
    def create_video_processing_tab(self):
        """إنشاء تبويب معالجة الفيديوهات"""
        video_tab = QWidget()
        layout = QVBoxLayout(video_tab)
        
        # قائمة الفيديوهات
        video_group = QGroupBox("📹 الفيديوهات المحملة")
        video_layout = QVBoxLayout(video_group)
        
        self.video_list = QListWidget()
        video_layout.addWidget(self.video_list)
        
        # أزرار إدارة الفيديوهات
        video_buttons_layout = QHBoxLayout()
        
        add_video_btn = QPushButton("➕ إضافة فيديو")
        add_video_btn.clicked.connect(self.add_video)
        video_buttons_layout.addWidget(add_video_btn)
        
        remove_video_btn = QPushButton("➖ حذف فيديو")
        remove_video_btn.clicked.connect(self.remove_video)
        video_buttons_layout.addWidget(remove_video_btn)
        
        video_layout.addLayout(video_buttons_layout)
        layout.addWidget(video_group)
        
        # إعدادات المعالجة
        processing_group = QGroupBox("⚙️ إعدادات المعالجة")
        processing_layout = QGridLayout(processing_group)
        
        # مدة المقطع
        processing_layout.addWidget(QLabel("مدة المقطع (ثانية):"), 0, 0)
        self.segment_duration = QSpinBox()
        self.segment_duration.setRange(5, 300)
        self.segment_duration.setValue(30)
        processing_layout.addWidget(self.segment_duration, 0, 1)
        
        # نوع القص
        processing_layout.addWidget(QLabel("نوع القص:"), 1, 0)
        self.cut_type = QComboBox()
        self.cut_type.addItems(["تلقائي (ذكي)", "مقاطع متساوية", "حسب المشاهد", "حسب الصوت"])
        processing_layout.addWidget(self.cut_type, 1, 1)
        
        # جودة الإخراج
        processing_layout.addWidget(QLabel("جودة الإخراج:"), 2, 0)
        self.output_quality = QComboBox()
        self.output_quality.addItems(["4K (2160p)", "Full HD (1080p)", "HD (720p)", "SD (480p)"])
        self.output_quality.setCurrentText("Full HD (1080p)")
        processing_layout.addWidget(self.output_quality, 2, 1)
        
        layout.addWidget(processing_group)
        
        # زر المعالجة
        process_btn = QPushButton("🚀 بدء المعالجة")
        process_btn.clicked.connect(self.start_processing)
        process_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 10px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        layout.addWidget(process_btn)
        
        # شريط التقدم
        self.progress_bar = QProgressBar()
        layout.addWidget(self.progress_bar)
        
        self.tab_widget.addTab(video_tab, "🎬 معالجة الفيديوهات")
    
    def create_language_ai_tab(self):
        """إنشاء تبويب الذكاء اللغوي"""
        ai_tab = QWidget()
        layout = QVBoxLayout(ai_tab)
        
        # تفريغ الصوت
        transcription_group = QGroupBox("📝 تفريغ الصوت")
        transcription_layout = QVBoxLayout(transcription_group)
        
        transcribe_btn = QPushButton("🎤 استخراج النص من الصوت")
        transcribe_btn.clicked.connect(self.transcribe_audio)
        transcription_layout.addWidget(transcribe_btn)
        
        self.transcription_text = QTextEdit()
        self.transcription_text.setPlaceholderText("سيظهر النص المستخرج هنا...")
        transcription_layout.addWidget(self.transcription_text)
        
        layout.addWidget(transcription_group)
        
        # الترجمة
        translation_group = QGroupBox("🌐 الترجمة")
        translation_layout = QGridLayout(translation_group)
        
        translation_layout.addWidget(QLabel("من:"), 0, 0)
        self.source_language = QComboBox()
        self.source_language.addItems(["تلقائي", "العربية", "الإنجليزية", "الفرنسية", "الألمانية", "الإسبانية"])
        translation_layout.addWidget(self.source_language, 0, 1)
        
        translation_layout.addWidget(QLabel("إلى:"), 0, 2)
        self.target_language = QComboBox()
        self.target_language.addItems(["الإنجليزية", "العربية", "الفرنسية", "الألمانية", "الإسبانية"])
        translation_layout.addWidget(self.target_language, 0, 3)
        
        translate_btn = QPushButton("🔄 ترجمة")
        translate_btn.clicked.connect(self.translate_text)
        translation_layout.addWidget(translate_btn, 1, 0, 1, 4)
        
        layout.addWidget(translation_group)
        
        # الدبلجة
        dubbing_group = QGroupBox("🎬 الدبلجة")
        dubbing_layout = QGridLayout(dubbing_group)
        
        dubbing_layout.addWidget(QLabel("نوع الصوت:"), 0, 0)
        self.voice_type = QComboBox()
        self.voice_type.addItems(["ذكر", "أنثى"])
        dubbing_layout.addWidget(self.voice_type, 0, 1)
        
        dubbing_layout.addWidget(QLabel("سرعة الكلام:"), 1, 0)
        self.speech_speed = QSlider(Qt.Orientation.Horizontal)
        self.speech_speed.setRange(50, 200)
        self.speech_speed.setValue(100)
        dubbing_layout.addWidget(self.speech_speed, 1, 1)
        
        generate_dubbing_btn = QPushButton("🎬 إنتاج الدبلجة")
        generate_dubbing_btn.clicked.connect(self.generate_dubbing)
        dubbing_layout.addWidget(generate_dubbing_btn, 2, 0, 1, 2)
        
        layout.addWidget(dubbing_group)
        
        self.tab_widget.addTab(ai_tab, "🌐 الذكاء اللغوي")
    
    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        projects_tab = QWidget()
        layout = QVBoxLayout(projects_tab)
        
        # قائمة المشاريع
        projects_group = QGroupBox("📋 المشاريع المحفوظة")
        projects_layout = QVBoxLayout(projects_group)
        
        self.projects_list = QListWidget()
        projects_layout.addWidget(self.projects_list)
        
        # أزرار إدارة المشاريع
        projects_buttons_layout = QHBoxLayout()
        
        save_project_btn = QPushButton("💾 حفظ المشروع")
        save_project_btn.clicked.connect(self.save_project)
        projects_buttons_layout.addWidget(save_project_btn)
        
        load_project_btn = QPushButton("📂 فتح مشروع")
        load_project_btn.clicked.connect(self.load_project)
        projects_buttons_layout.addWidget(load_project_btn)
        
        export_project_btn = QPushButton("📤 تصدير مشروع")
        export_project_btn.clicked.connect(self.export_project)
        projects_buttons_layout.addWidget(export_project_btn)
        
        projects_layout.addLayout(projects_buttons_layout)
        layout.addWidget(projects_group)
        
        self.tab_widget.addTab(projects_tab, "📋 المشاريع")
    
    def create_settings_tab(self):
        """إنشاء تبويب الإعدادات"""
        settings_tab = QWidget()
        layout = QVBoxLayout(settings_tab)
        
        # إعدادات عامة
        general_group = QGroupBox("⚙️ الإعدادات العامة")
        general_layout = QGridLayout(general_group)
        
        general_layout.addWidget(QLabel("لغة التطبيق:"), 0, 0)
        self.app_language = QComboBox()
        self.app_language.addItems(["العربية", "English"])
        general_layout.addWidget(self.app_language, 0, 1)
        
        general_layout.addWidget(QLabel("مجلد الإخراج:"), 1, 0)
        self.output_folder = QLineEdit()
        self.output_folder.setText("./output_videos")
        general_layout.addWidget(self.output_folder, 1, 1)
        
        browse_folder_btn = QPushButton("📁 تصفح")
        browse_folder_btn.clicked.connect(self.browse_output_folder)
        general_layout.addWidget(browse_folder_btn, 1, 2)
        
        layout.addWidget(general_group)
        
        # إعدادات الأداء
        performance_group = QGroupBox("⚡ إعدادات الأداء")
        performance_layout = QGridLayout(performance_group)
        
        performance_layout.addWidget(QLabel("عدد المعالجات:"), 0, 0)
        self.cpu_cores = QSpinBox()
        self.cpu_cores.setRange(1, 16)
        self.cpu_cores.setValue(4)
        performance_layout.addWidget(self.cpu_cores, 0, 1)
        
        self.use_gpu = QCheckBox("استخدام GPU للتسريع")
        performance_layout.addWidget(self.use_gpu, 1, 0, 1, 2)
        
        layout.addWidget(performance_group)
        
        self.tab_widget.addTab(settings_tab, "⚙️ الإعدادات")
    
    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bar = self.statusBar()
        status_bar.showMessage("التطبيق جاهز - الوضع الآمن")
    
    def setup_connections(self):
        """ربط الإشارات بشكل آمن"""
        # لا نربط إشارات معقدة في الوضع الآمن
        pass
    
    def setup_emergency_ui(self):
        """إعداد واجهة الطوارئ"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        label = QLabel("⚠️ وضع الطوارئ - حدث خطأ في التهيئة")
        label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        label.setStyleSheet("color: red; font-size: 16px; font-weight: bold;")
        layout.addWidget(label)
        
        restart_btn = QPushButton("🔄 إعادة المحاولة")
        restart_btn.clicked.connect(self.restart_application)
        layout.addWidget(restart_btn)
    
    # دوال الأحداث البسيطة
    def new_project(self):
        """مشروع جديد"""
        QMessageBox.information(self, "مشروع جديد", "تم إنشاء مشروع جديد")
    
    def open_file(self):
        """فتح ملف"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "اختر ملف فيديو", "", 
            "Video Files (*.mp4 *.avi *.mov *.mkv *.wmv)"
        )
        if file_path:
            self.video_list.addItem(file_path)
            QMessageBox.information(self, "تم", f"تم إضافة الملف: {file_path}")
    
    def download_video(self):
        """تحميل فيديو"""
        url, ok = QInputDialog.getText(self, "تحميل فيديو", "أدخل رابط الفيديو:")
        if ok and url:
            QMessageBox.information(self, "تحميل", f"سيتم تحميل: {url}")
    
    def show_settings(self):
        """عرض الإعدادات"""
        self.tab_widget.setCurrentIndex(3)  # تبويب الإعدادات
    
    def add_video(self):
        """إضافة فيديو"""
        self.open_file()
    
    def remove_video(self):
        """حذف فيديو"""
        current_item = self.video_list.currentItem()
        if current_item:
            self.video_list.takeItem(self.video_list.row(current_item))
    
    def start_processing(self):
        """بدء المعالجة"""
        if self.video_list.count() == 0:
            QMessageBox.warning(self, "تحذير", "يرجى إضافة فيديو أولاً")
            return
        
        QMessageBox.information(self, "معالجة", "تم بدء المعالجة (وضع آمن)")
        
        # محاكاة التقدم
        for i in range(101):
            self.progress_bar.setValue(i)
            QApplication.processEvents()
            import time
            time.sleep(0.01)
    
    def transcribe_audio(self):
        """تفريغ الصوت"""
        self.transcription_text.setText("مثال على النص المستخرج من الصوت...")
    
    def translate_text(self):
        """ترجمة النص"""
        QMessageBox.information(self, "ترجمة", "تم ترجمة النص")
    
    def generate_dubbing(self):
        """إنتاج الدبلجة"""
        QMessageBox.information(self, "دبلجة", "تم إنتاج الدبلجة")
    
    def save_project(self):
        """حفظ المشروع"""
        name, ok = QInputDialog.getText(self, "حفظ المشروع", "اسم المشروع:")
        if ok and name:
            self.projects_list.addItem(name)
            QMessageBox.information(self, "حفظ", f"تم حفظ المشروع: {name}")
    
    def load_project(self):
        """فتح مشروع"""
        current_item = self.projects_list.currentItem()
        if current_item:
            QMessageBox.information(self, "فتح", f"تم فتح المشروع: {current_item.text()}")
    
    def export_project(self):
        """تصدير مشروع"""
        QMessageBox.information(self, "تصدير", "تم تصدير المشروع")
    
    def browse_output_folder(self):
        """تصفح مجلد الإخراج"""
        folder = QFileDialog.getExistingDirectory(self, "اختر مجلد الإخراج")
        if folder:
            self.output_folder.setText(folder)
    
    def restart_application(self):
        """إعادة تشغيل التطبيق"""
        QMessageBox.information(self, "إعادة تشغيل", "سيتم إعادة تشغيل التطبيق")
        QApplication.quit()

# للتوافق مع الكود الموجود
MainWindow = SafeMainWindow
