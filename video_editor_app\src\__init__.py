# -*- coding: utf-8 -*-
"""
وحدة المصدر الرئيسية
Source Module for Video Editor Application
"""

try:
    from .video_editor_core import VideoEditorCore, VideoProcessingThread
    __all__ = ['VideoEditorCore', 'VideoProcessingThread']
except ImportError as e:
    print(f"⚠️ خطأ في استيراد النواة الرئيسية: {e}")

    # إنشاء فئات وهمية
    class VideoEditorCore:
        def __init__(self):
            pass
        def get_video_info(self, path):
            return {}
        def get_supported_platforms(self):
            return []

    class VideoProcessingThread:
        def __init__(self, *args):
            pass
        def start(self):
            pass

    __all__ = ['VideoEditorCore', 'VideoProcessingThread']
