# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
#   According to pypa/pipenv#598, it is recommended to include Pipfile.lock in version control.
#   However, in case of collaboration, if having platform-specific dependencies or dependencies
#   having no cross-platform support, pipenv may install dependencies that don't work, or not
#   install all needed dependencies.
#Pipfile.lock

# PEP 582; used by e.g. github.com/David-OConnor/pyflow
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Video Editor App specific
# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
temp_files/
*.tmp
*.temp

# Output files
output_videos/
*.mp4
*.avi
*.mov
*.mkv
*.wmv
*.flv
*.webm

# Audio files
*.wav
*.mp3
*.aac
*.ogg
*.flac

# Image files (except icons)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
!assets/*.ico
!assets/*.png
!assets/*.jpg

# Log files
logs/
*.log

# Configuration files with sensitive data
config.json
api_keys.json
secrets.json

# Cache directories
.cache/
cache/

# Downloaded files
downloads/

# Backup files
*.bak
*.backup

# OS specific files
# Windows
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.Spotlight-V100
.Trashes

# Linux
*~
.nfs*

# IDE specific files
# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Project specific
# Release files
release/
installer/
*.exe
*.msi
*.dmg
*.deb
*.rpm

# Test files
test_videos/
test_audio/
test_images/

# Documentation build
docs/build/

# Jupyter notebooks
*.ipynb

# Model files (AI models can be large)
models/
*.model
*.pkl
*.h5
*.pb

# FFmpeg binaries (if included)
ffmpeg/
ffprobe/

# Virtual environment
video_editor_env/

# API response cache
api_cache/

# User data
user_data/
projects/

# Thumbnails
thumbnails/

# Whisper model cache
~/.cache/whisper/
