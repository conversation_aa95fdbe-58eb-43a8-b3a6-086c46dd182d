# -*- coding: utf-8 -*-
"""
وحدات احتياطية للمكتبات المفقودة
Fallback modules for missing libraries
"""

class DummyVideoFileClip:
    def __init__(self, *args, **kwargs):
        raise ImportError("MoviePy غير مثبت")

class DummyTranslator:
    def __init__(self, *args, **kwargs):
        pass
    def translate(self, *args, **kwargs):
        return "ترجمة غير متاحة"

class DummyTTS:
    def __init__(self, *args, **kwargs):
        pass
    def save(self, *args, **kwargs):
        pass

# تصدير الفئات
__all__ = ['DummyVideoFileClip', 'DummyTranslator', 'DummyTTS']
