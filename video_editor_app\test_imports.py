#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لجميع الاستيرادات
Comprehensive Import Test for Video Editor Application
"""

import sys
import traceback
from pathlib import Path

def test_basic_imports():
    """اختبار الاستيرادات الأساسية"""
    print("🔍 اختبار الاستيرادات الأساسية...")
    
    tests = [
        ("Python Standard Library", "import os, sys, pathlib, json, sqlite3"),
        ("Typing", "from typing import Dict, List, Optional, Any, Union"),
        ("Threading", "import threading"),
        ("Datetime", "from datetime import datetime"),
        ("Logging", "import logging"),
    ]
    
    results = {}
    for name, import_cmd in tests:
        try:
            exec(import_cmd)
            print(f"  ✅ {name}")
            results[name] = True
        except Exception as e:
            print(f"  ❌ {name}: {e}")
            results[name] = False
    
    return results

def test_pyqt6_imports():
    """اختبار استيراد PyQt6"""
    print("\n🖥️ اختبار PyQt6...")
    
    tests = [
        ("PyQt6.QtWidgets", "from PyQt6.QtWidgets import QApplication, QMainWindow"),
        ("PyQt6.QtCore", "from PyQt6.QtCore import Qt, QThread, pyqtSignal"),
        ("PyQt6.QtGui", "from PyQt6.QtGui import QFont, QIcon"),
    ]
    
    results = {}
    for name, import_cmd in tests:
        try:
            exec(import_cmd)
            print(f"  ✅ {name}")
            results[name] = True
        except Exception as e:
            print(f"  ❌ {name}: {e}")
            results[name] = False
    
    return results

def test_video_processing_imports():
    """اختبار استيراد مكتبات معالجة الفيديو"""
    print("\n🎬 اختبار مكتبات معالجة الفيديو...")
    
    tests = [
        ("MoviePy", "import moviepy"),
        ("OpenCV", "import cv2"),
        ("Pillow", "from PIL import Image"),
        ("Requests", "import requests"),
    ]
    
    results = {}
    for name, import_cmd in tests:
        try:
            exec(import_cmd)
            print(f"  ✅ {name}")
            results[name] = True
        except Exception as e:
            print(f"  ❌ {name}: {e}")
            results[name] = False
    
    return results

def test_optional_imports():
    """اختبار الاستيرادات الاختيارية"""
    print("\n⚙️ اختبار المكتبات الاختيارية...")
    
    tests = [
        ("QDarkStyle", "import qdarkstyle"),
        ("MediaPipe", "import mediapipe"),
        ("Whisper", "import whisper"),
        ("OpenAI", "import openai"),
        ("Google Translate", "from googletrans import Translator"),
        ("gTTS", "from gtts import gTTS"),
        ("YT-DLP", "import yt_dlp"),
        ("Face Recognition", "import face_recognition"),
    ]
    
    results = {}
    for name, import_cmd in tests:
        try:
            exec(import_cmd)
            print(f"  ✅ {name}")
            results[name] = True
        except Exception as e:
            print(f"  ⚠️ {name}: غير متاح")
            results[name] = False
    
    return results

def test_project_modules():
    """اختبار وحدات المشروع"""
    print("\n📦 اختبار وحدات المشروع...")
    
    # إضافة مسار المشروع
    project_root = Path(__file__).parent
    sys.path.insert(0, str(project_root))
    
    tests = [
        ("Config", "import config"),
        ("Database", "from database import DatabaseManager"),
        ("Utils", "from utils import VideoEditorLogger, setup_logger"),
        ("Video Processing", "from video_processing import VideoDownloader, VideoProcessor, FaceTracker"),
        ("Language AI", "from language_ai import SpeechToText, Translator, TextToSpeech"),
        ("Core", "from src import VideoEditorCore, VideoProcessingThread"),
        ("GUI", "from gui import MainWindow"),
    ]
    
    results = {}
    for name, import_cmd in tests:
        try:
            exec(import_cmd)
            print(f"  ✅ {name}")
            results[name] = True
        except Exception as e:
            print(f"  ❌ {name}: {e}")
            results[name] = False
    
    return results

def test_main_application():
    """اختبار التطبيق الرئيسي"""
    print("\n🚀 اختبار التطبيق الرئيسي...")
    
    try:
        # محاولة استيراد main
        import main
        print("  ✅ استيراد main.py")
        
        # محاولة إنشاء QApplication (بدون تشغيل)
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("  ✅ إنشاء QApplication")
        
        # محاولة إنشاء MainWindow (بدون عرض)
        from gui.main_window import MainWindow
        window = MainWindow()
        print("  ✅ إنشاء MainWindow")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في التطبيق الرئيسي: {e}")
        traceback.print_exc()
        return False

def generate_report(all_results):
    """إنشاء تقرير شامل"""
    print("\n" + "="*60)
    print("📊 تقرير الاختبار الشامل")
    print("="*60)
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        if isinstance(results, dict):
            category_passed = sum(results.values())
            category_total = len(results)
            print(f"\n{category}: {category_passed}/{category_total}")
            
            for test_name, result in results.items():
                status = "✅" if result else "❌"
                print(f"  {status} {test_name}")
            
            total_tests += category_total
            passed_tests += category_passed
        else:
            # للاختبارات البسيطة
            status = "✅" if results else "❌"
            print(f"\n{category}: {status}")
            total_tests += 1
            if results:
                passed_tests += 1
    
    print("\n" + "="*60)
    print(f"📈 النتيجة النهائية: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    
    # تحديد حالة التطبيق
    essential_modules = ['PyQt6.QtWidgets', 'PyQt6.QtCore', 'Requests']
    essential_available = all(
        all_results.get('PyQt6', {}).get(module, False) 
        for module in essential_modules
    )
    
    if essential_available and all_results.get('التطبيق الرئيسي', False):
        print("🎉 التطبيق جاهز للتشغيل!")
        return True
    elif essential_available:
        print("⚠️ التطبيق يمكن تشغيله مع ميزات محدودة")
        return True
    else:
        print("❌ التطبيق غير جاهز للتشغيل - مكتبات أساسية مفقودة")
        return False

def main():
    """الدالة الرئيسية للاختبار"""
    print("🧪 اختبار شامل لمعالج الفيديوهات المتكامل")
    print("="*60)
    
    # تشغيل جميع الاختبارات
    all_results = {}
    
    all_results['الأساسيات'] = test_basic_imports()
    all_results['PyQt6'] = test_pyqt6_imports()
    all_results['معالجة الفيديو'] = test_video_processing_imports()
    all_results['المكتبات الاختيارية'] = test_optional_imports()
    all_results['وحدات المشروع'] = test_project_modules()
    all_results['التطبيق الرئيسي'] = test_main_application()
    
    # إنشاء التقرير
    app_ready = generate_report(all_results)
    
    # نصائح للإصلاح
    if not app_ready:
        print("\n💡 نصائح للإصلاح:")
        print("1. تثبيت المكتبات الأساسية: pip install PyQt6 requests Pillow")
        print("2. تثبيت مكتبات الفيديو: pip install moviepy opencv-python")
        print("3. تشغيل: python safe_run.py")
    
    return app_ready

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
