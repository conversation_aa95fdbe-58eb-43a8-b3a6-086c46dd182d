# -*- coding: utf-8 -*-
"""
وحدة تحويل النص إلى كلام (الدبلجة)
Text-to-Speech Module for Video Editor Application
"""

import os
import tempfile
from pathlib import Path
from typing import dict, list, Optional, Callable
import logging

try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False
    print("⚠️ gTTS غير مثبت - الدبلجة محدودة")

try:
    import pyttsx3
    PYTTSX3_AVAILABLE = True
except ImportError:
    PYTTSX3_AVAILABLE = False
    print("⚠️ pyttsx3 غير مثبت - دبلجة محلية محدودة")

try:
    import openai
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("⚠️ OpenAI غير مثبت - دبلجة متقدمة محدودة")

try:
    from moviepy.editor import VideoFileClip, AudioFileClip, CompositeAudioClip
    MOVIEPY_AVAILABLE = True
except ImportError:
    MOVIEPY_AVAILABLE = False
    print("⚠️ MoviePy غير مثبت - دمج الصوت محدود")

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    print("⚠️ pydub غير مثبت - معالجة الصوت محدودة")

from utils.logger import VideoEditorLogger

class TextToSpeech:
    """فئة تحويل النص إلى كلام للدبلجة"""
    
    def __init__(self, openai_api_key: str = None, elevenlabs_api_key: str = None):
        """
        تهيئة محول النص إلى كلام
        
        Args:
            openai_api_key: مفتاح OpenAI API (اختياري)
            elevenlabs_api_key: مفتاح ElevenLabs API (اختياري)
        """
        self.logger = VideoEditorLogger(__name__)
        self.openai_api_key = openai_api_key
        self.elevenlabs_api_key = elevenlabs_api_key
        
        # إعداد pyttsx3
        if PYTTSX3_AVAILABLE:
            try:
                self.tts_engine = pyttsx3.init()
                self.logger.info("تم تهيئة محرك pyttsx3 بنجاح")
            except Exception as e:
                self.logger.warning(f"فشل في تهيئة pyttsx3: {str(e)}")
                self.tts_engine = None
        else:
            self.tts_engine = None
        
        # إعداد OpenAI
        if OPENAI_AVAILABLE and openai_api_key:
            openai.api_key = openai_api_key
            self.logger.info("تم تهيئة OpenAI للدبلجة")
        
        # إعدادات الأصوات المدعومة
        self.voice_settings = {
            'gtts': {
                'ar': {'lang': 'ar', 'tld': 'com'},
                'en': {'lang': 'en', 'tld': 'com'},
                'fr': {'lang': 'fr', 'tld': 'fr'},
                'de': {'lang': 'de', 'tld': 'de'},
                'es': {'lang': 'es', 'tld': 'es'},
                'it': {'lang': 'it', 'tld': 'it'},
                'ru': {'lang': 'ru', 'tld': 'ru'},
                'ja': {'lang': 'ja', 'tld': 'co.jp'},
                'ko': {'lang': 'ko', 'tld': 'co.kr'},
                'zh': {'lang': 'zh', 'tld': 'com'}
            }
        }
    
    def generate_speech_gtts(self, text: str, language: str = 'ar',
                            voice_settings: dict = None, progress_callback: Callable = None) -> str:
        """
        إنتاج الكلام باستخدام Google TTS
        
        Args:
            text: النص المراد تحويله
            language: لغة الكلام
            voice_settings: إعدادات الصوت
            progress_callback: دالة تحديث التقدم
            
        Returns:
            str: مسار ملف الصوت
        """
        try:
            if not GTTS_AVAILABLE:
                raise Exception("مكتبة gTTS غير متاحة")
            
            self.logger.operation_start("إنتاج الكلام بـ gTTS", f"لغة: {language}")
            
            if progress_callback:
                progress_callback(20)
            
            # إعداد gTTS
            lang_settings = self.voice_settings['gtts'].get(language, {'lang': language, 'tld': 'com'})
            
            tts = gTTS(
                text=text,
                lang=lang_settings['lang'],
                tld=lang_settings['tld'],
                slow=voice_settings.get('slow', False) if voice_settings else False
            )
            
            if progress_callback:
                progress_callback(60)
            
            # حفظ الملف الصوتي
            temp_file = tempfile.NamedTemporaryFile(suffix='.mp3', delete=False)
            temp_file.close()
            
            tts.save(temp_file.name)
            
            if progress_callback:
                progress_callback(100)
            
            self.logger.operation_complete("إنتاج الكلام بـ gTTS", f"لغة: {language}", 0)
            return temp_file.name
            
        except Exception as e:
            self.logger.operation_error("إنتاج الكلام بـ gTTS", f"لغة: {language}", str(e))
            return ""
    
    def generate_speech_pyttsx3(self, text: str, voice_settings: dict = None,
                               progress_callback: Callable = None) -> str:
        """
        إنتاج الكلام باستخدام pyttsx3
        
        Args:
            text: النص المراد تحويله
            voice_settings: إعدادات الصوت
            progress_callback: دالة تحديث التقدم
            
        Returns:
            str: مسار ملف الصوت
        """
        try:
            if not self.tts_engine:
                raise Exception("محرك pyttsx3 غير متاح")
            
            self.logger.operation_start("إنتاج الكلام بـ pyttsx3", "محلي")
            
            if progress_callback:
                progress_callback(20)
            
            # تطبيق إعدادات الصوت
            if voice_settings:
                if 'rate' in voice_settings:
                    self.tts_engine.setProperty('rate', voice_settings['rate'])
                if 'volume' in voice_settings:
                    self.tts_engine.setProperty('volume', voice_settings['volume'])
                if 'voice_id' in voice_settings:
                    voices = self.tts_engine.getProperty('voices')
                    if voice_settings['voice_id'] < len(voices):
                        self.tts_engine.setProperty('voice', voices[voice_settings['voice_id']].id)
            
            if progress_callback:
                progress_callback(50)
            
            # إنشاء ملف مؤقت
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            temp_file.close()
            
            # حفظ الكلام
            self.tts_engine.save_to_file(text, temp_file.name)
            self.tts_engine.runAndWait()
            
            if progress_callback:
                progress_callback(100)
            
            self.logger.operation_complete("إنتاج الكلام بـ pyttsx3", "محلي", 0)
            return temp_file.name
            
        except Exception as e:
            self.logger.operation_error("إنتاج الكلام بـ pyttsx3", "محلي", str(e))
            return ""
    
    def generate_speech_openai(self, text: str, voice: str = 'alloy',
                              voice_settings: dict = None, progress_callback: Callable = None) -> str:
        """
        إنتاج الكلام باستخدام OpenAI TTS
        
        Args:
            text: النص المراد تحويله
            voice: نوع الصوت ('alloy', 'echo', 'fable', 'onyx', 'nova', 'shimmer')
            voice_settings: إعدادات الصوت
            progress_callback: دالة تحديث التقدم
            
        Returns:
            str: مسار ملف الصوت
        """
        try:
            if not OPENAI_AVAILABLE or not self.openai_api_key:
                raise Exception("OpenAI غير متاح أو مفتاح API غير محدد")
            
            self.logger.operation_start("إنتاج الكلام بـ OpenAI", f"صوت: {voice}")
            
            if progress_callback:
                progress_callback(20)
            
            # إنتاج الكلام
            response = openai.Audio.speech.create(
                model="tts-1",
                voice=voice,
                input=text,
                response_format="mp3",
                speed=voice_settings.get('speed', 1.0) if voice_settings else 1.0
            )
            
            if progress_callback:
                progress_callback(80)
            
            # حفظ الملف
            temp_file = tempfile.NamedTemporaryFile(suffix='.mp3', delete=False)
            temp_file.write(response.content)
            temp_file.close()
            
            if progress_callback:
                progress_callback(100)
            
            self.logger.operation_complete("إنتاج الكلام بـ OpenAI", f"صوت: {voice}", 0)
            return temp_file.name
            
        except Exception as e:
            self.logger.operation_error("إنتاج الكلام بـ OpenAI", f"صوت: {voice}", str(e))
            return ""
    
    def generate_speech_elevenlabs(self, text: str, voice_id: str = None,
                                  voice_settings: dict = None, progress_callback: Callable = None) -> str:
        """
        إنتاج الكلام باستخدام ElevenLabs (يحتاج تنفيذ API)
        
        Args:
            text: النص المراد تحويله
            voice_id: معرف الصوت
            voice_settings: إعدادات الصوت
            progress_callback: دالة تحديث التقدم
            
        Returns:
            str: مسار ملف الصوت
        """
        try:
            if not self.elevenlabs_api_key:
                raise Exception("مفتاح ElevenLabs API غير محدد")
            
            # هذا يحتاج تنفيذ API الخاص بـ ElevenLabs
            # يمكن إضافته لاحقاً
            raise Exception("ElevenLabs API غير مُنفذ بعد")
            
        except Exception as e:
            self.logger.operation_error("إنتاج الكلام بـ ElevenLabs", "API", str(e))
            return ""
    
    def generate_speech(self, text: str, service: str = 'gtts', language: str = 'ar',
                       voice_settings: dict = None, progress_callback: Callable = None) -> str:
        """
        إنتاج الكلام باستخدام الخدمة المحددة
        
        Args:
            text: النص المراد تحويله
            service: خدمة TTS ('gtts', 'pyttsx3', 'openai', 'elevenlabs')
            language: لغة الكلام
            voice_settings: إعدادات الصوت
            progress_callback: دالة تحديث التقدم
            
        Returns:
            str: مسار ملف الصوت
        """
        try:
            if service == 'gtts':
                return self.generate_speech_gtts(text, language, voice_settings, progress_callback)
            elif service == 'pyttsx3':
                return self.generate_speech_pyttsx3(text, voice_settings, progress_callback)
            elif service == 'openai':
                voice = voice_settings.get('voice', 'alloy') if voice_settings else 'alloy'
                return self.generate_speech_openai(text, voice, voice_settings, progress_callback)
            elif service == 'elevenlabs':
                voice_id = voice_settings.get('voice_id') if voice_settings else None
                return self.generate_speech_elevenlabs(text, voice_id, voice_settings, progress_callback)
            else:
                raise Exception(f"خدمة TTS غير مدعومة: {service}")
                
        except Exception as e:
            self.logger.error(f"خطأ في إنتاج الكلام: {str(e)}")
            return ""
    
    def create_dubbing_for_segments(self, segments: list[dict], service: str = 'gtts',
                                   language: str = 'ar', voice_settings: dict = None,
                                   progress_callback: Callable = None) -> list[dict]:
        """
        إنشاء دبلجة للمقاطع مع التوقيتات
        
        Args:
            segments: قائمة المقاطع مع النصوص والتوقيتات
            service: خدمة TTS
            language: لغة الكلام
            voice_settings: إعدادات الصوت
            progress_callback: دالة تحديث التقدم
            
        Returns:
            list[dict]: المقاطع مع ملفات الصوت
        """
        try:
            self.logger.info(f"إنشاء دبلجة لـ {len(segments)} مقطع")
            
            dubbed_segments = []
            total_segments = len(segments)
            
            for i, segment in enumerate(segments):
                # إنتاج الكلام للمقطع
                audio_path = self.generate_speech(
                    segment['text'],
                    service,
                    language,
                    voice_settings
                )
                
                if audio_path:
                    # إضافة معلومات الصوت للمقطع
                    dubbed_segment = segment.copy()
                    dubbed_segment['audio_path'] = audio_path
                    dubbed_segment['tts_service'] = service
                    dubbed_segment['tts_language'] = language
                    
                    # حساب مدة الصوت
                    if PYDUB_AVAILABLE:
                        try:
                            audio = AudioSegment.from_file(audio_path)
                            dubbed_segment['audio_duration'] = len(audio) / 1000.0  # تحويل إلى ثوان
                        except:
                            dubbed_segment['audio_duration'] = segment.get('end', 0) - segment.get('start', 0)
                    
                    dubbed_segments.append(dubbed_segment)
                else:
                    # في حالة فشل إنتاج الصوت، إضافة المقطع بدون صوت
                    dubbed_segments.append(segment)
                
                # تحديث التقدم
                if progress_callback:
                    progress = int((i + 1) / total_segments * 100)
                    progress_callback(progress)
            
            self.logger.info(f"تم إنشاء دبلجة لـ {len(dubbed_segments)} مقطع")
            return dubbed_segments
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء الدبلجة: {str(e)}")
            return segments
    
    def combine_audio_segments(self, segments: list[dict], output_path: str,
                              progress_callback: Callable = None) -> str:
        """
        دمج مقاطع الصوت في ملف واحد
        
        Args:
            segments: المقاطع مع ملفات الصوت
            output_path: مسار الملف النهائي
            progress_callback: دالة تحديث التقدم
            
        Returns:
            str: مسار الملف المدموج
        """
        try:
            if not PYDUB_AVAILABLE:
                raise Exception("مكتبة pydub مطلوبة لدمج الصوت")
            
            self.logger.operation_start("دمج مقاطع الصوت", output_path)
            
            # إنشاء صوت فارغ
            combined_audio = AudioSegment.empty()
            
            for i, segment in enumerate(segments):
                if 'audio_path' in segment and segment['audio_path']:
                    # تحميل مقطع الصوت
                    audio_segment = AudioSegment.from_file(segment['audio_path'])
                    
                    # حساب التوقيت
                    start_time = segment.get('start', 0) * 1000  # تحويل إلى ميلي ثانية
                    current_length = len(combined_audio)
                    
                    # إضافة صمت إذا لزم الأمر
                    if start_time > current_length:
                        silence_duration = start_time - current_length
                        silence = AudioSegment.silent(duration=silence_duration)
                        combined_audio += silence
                    
                    # إضافة مقطع الصوت
                    combined_audio += audio_segment
                
                # تحديث التقدم
                if progress_callback:
                    progress = int((i + 1) / len(segments) * 100)
                    progress_callback(progress)
            
            # حفظ الملف النهائي
            combined_audio.export(output_path, format="wav")
            
            self.logger.operation_complete("دمج مقاطع الصوت", output_path, 0)
            return output_path
            
        except Exception as e:
            self.logger.operation_error("دمج مقاطع الصوت", output_path, str(e))
            return ""
    
    def add_dubbing_to_video(self, video_path: str, audio_path: str, output_path: str,
                            mix_with_original: bool = False, progress_callback: Callable = None) -> str:
        """
        إضافة الدبلجة إلى الفيديو
        
        Args:
            video_path: مسار الفيديو الأصلي
            audio_path: مسار ملف الدبلجة
            output_path: مسار الفيديو النهائي
            mix_with_original: خلط مع الصوت الأصلي
            progress_callback: دالة تحديث التقدم
            
        Returns:
            str: مسار الفيديو المدبلج
        """
        try:
            if not MOVIEPY_AVAILABLE:
                raise Exception("مكتبة MoviePy مطلوبة لإضافة الدبلجة")
            
            self.logger.operation_start("إضافة الدبلجة للفيديو", video_path)
            
            # تحميل الفيديو والصوت
            video = VideoFileClip(video_path)
            dubbing_audio = AudioFileClip(audio_path)
            
            if progress_callback:
                progress_callback(30)
            
            # إعداد الصوت النهائي
            if mix_with_original and video.audio:
                # خلط الدبلجة مع الصوت الأصلي
                original_audio = video.audio.volumex(0.3)  # تقليل مستوى الصوت الأصلي
                final_audio = CompositeAudioClip([original_audio, dubbing_audio.volumex(0.8)])
            else:
                # استبدال الصوت الأصلي بالدبلجة
                final_audio = dubbing_audio
            
            if progress_callback:
                progress_callback(60)
            
            # إنشاء الفيديو النهائي
            final_video = video.set_audio(final_audio)
            
            # حفظ الفيديو
            final_video.write_videofile(
                output_path,
                codec='libx264',
                audio_codec='aac',
                verbose=False,
                logger=None
            )
            
            # تنظيف الموارد
            video.close()
            dubbing_audio.close()
            final_video.close()
            
            if progress_callback:
                progress_callback(100)
            
            self.logger.operation_complete("إضافة الدبلجة للفيديو", video_path, 0)
            return output_path
            
        except Exception as e:
            self.logger.operation_error("إضافة الدبلجة للفيديو", video_path, str(e))
            return ""
    
    def get_available_voices(self, service: str = 'pyttsx3') -> list[dict]:
        """
        الحصول على قائمة الأصوات المتاحة
        
        Args:
            service: خدمة TTS
            
        Returns:
            list[dict]: قائمة الأصوات المتاحة
        """
        voices = []
        
        try:
            if service == 'pyttsx3' and self.tts_engine:
                engine_voices = self.tts_engine.getProperty('voices')
                for i, voice in enumerate(engine_voices):
                    voices.append({
                        'id': i,
                        'name': voice.name,
                        'language': getattr(voice, 'languages', ['unknown'])[0] if hasattr(voice, 'languages') else 'unknown',
                        'gender': getattr(voice, 'gender', 'unknown')
                    })
            elif service == 'openai':
                voices = [
                    {'id': 'alloy', 'name': 'Alloy', 'language': 'en', 'gender': 'neutral'},
                    {'id': 'echo', 'name': 'Echo', 'language': 'en', 'gender': 'male'},
                    {'id': 'fable', 'name': 'Fable', 'language': 'en', 'gender': 'neutral'},
                    {'id': 'onyx', 'name': 'Onyx', 'language': 'en', 'gender': 'male'},
                    {'id': 'nova', 'name': 'Nova', 'language': 'en', 'gender': 'female'},
                    {'id': 'shimmer', 'name': 'Shimmer', 'language': 'en', 'gender': 'female'}
                ]
            elif service == 'gtts':
                voices = [
                    {'id': 'ar', 'name': 'Arabic', 'language': 'ar', 'gender': 'neutral'},
                    {'id': 'en', 'name': 'English', 'language': 'en', 'gender': 'neutral'},
                    {'id': 'fr', 'name': 'French', 'language': 'fr', 'gender': 'neutral'},
                    {'id': 'de', 'name': 'German', 'language': 'de', 'gender': 'neutral'},
                    {'id': 'es', 'name': 'Spanish', 'language': 'es', 'gender': 'neutral'}
                ]
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على الأصوات: {str(e)}")
        
        return voices
