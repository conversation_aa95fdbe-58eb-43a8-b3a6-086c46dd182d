#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التطبيق الجميل والمحدث - Beautiful Updated Application
معالج الفيديوهات المتكامل مع واجهة جميلة وحديثة
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_and_update_requirements():
    """فحص وتحديث المتطلبات"""
    print("🔍 فحص وتحديث المتطلبات...")
    
    # فحص Python
    if sys.version_info < (3, 8):
        print(f"❌ يتطلب Python 3.8+ (الحالي: {sys.version})")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    
    # فحص وتحديث PyQt6
    try:
        import PyQt6
        print(f"✅ PyQt6 {PyQt6.QtCore.PYQT_VERSION_STR}")
    except ImportError:
        print("📦 تثبيت PyQt6...")
        try:
            import subprocess
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", "PyQt6", "--upgrade"
            ])
            import PyQt6
            print(f"✅ تم تثبيت PyQt6 {PyQt6.QtCore.PYQT_VERSION_STR}")
        except:
            print("❌ فشل في تثبيت PyQt6")
            return False
    
    return True

def setup_beautiful_application():
    """إعداد التطبيق الجميل"""
    print("🎨 إعداد التطبيق الجميل...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        from PyQt6.QtGui import QFont, QPalette, QColor
        
        # إنشاء التطبيق
        app = QApplication(sys.argv)
        app.setApplicationName("معالج الفيديوهات المتكامل الجميل")
        app.setApplicationVersion("3.0.0")
        app.setOrganizationName("Video Editor Pro")
        
        # إعداد RTL
        try:
            app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            print("✅ تم إعداد اتجاه RTL")
        except:
            try:
                app.setLayoutDirection(Qt.RightToLeft)
                print("✅ تم إعداد اتجاه RTL (طريقة بديلة)")
            except:
                app.setLayoutDirection(2)
                print("✅ تم إعداد اتجاه RTL (رقمي)")
        
        # إعداد الخط العام
        font = QFont("Segoe UI", 10)
        font.setHintingPreference(QFont.HintingPreference.PreferFullHinting)
        app.setFont(font)
        
        # إعداد الألوان العامة
        palette = QPalette()
        palette.setColor(QPalette.ColorRole.Window, QColor(248, 249, 250))
        palette.setColor(QPalette.ColorRole.WindowText, QColor(44, 62, 80))
        palette.setColor(QPalette.ColorRole.Base, QColor(255, 255, 255))
        palette.setColor(QPalette.ColorRole.AlternateBase, QColor(248, 249, 250))
        palette.setColor(QPalette.ColorRole.ToolTipBase, QColor(52, 58, 64))
        palette.setColor(QPalette.ColorRole.ToolTipText, QColor(255, 255, 255))
        palette.setColor(QPalette.ColorRole.Text, QColor(44, 62, 80))
        palette.setColor(QPalette.ColorRole.Button, QColor(233, 236, 239))
        palette.setColor(QPalette.ColorRole.ButtonText, QColor(44, 62, 80))
        palette.setColor(QPalette.ColorRole.BrightText, QColor(220, 53, 69))
        palette.setColor(QPalette.ColorRole.Link, QColor(76, 175, 80))
        palette.setColor(QPalette.ColorRole.Highlight, QColor(76, 175, 80))
        palette.setColor(QPalette.ColorRole.HighlightedText, QColor(255, 255, 255))
        app.setPalette(palette)
        
        print("✅ تم إعداد التطبيق الجميل")
        return app
        
    except Exception as e:
        print(f"❌ خطأ في إعداد التطبيق: {e}")
        return None

def create_beautiful_window():
    """إنشاء النافذة الجميلة"""
    print("🖼️ إنشاء النافذة الجميلة...")
    
    try:
        from gui.beautiful_main_window import BeautifulMainWindow
        
        window = BeautifulMainWindow()
        print("✅ تم إنشاء النافذة الجميلة")
        
        return window
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد النافذة الجميلة: {e}")
        print("🔄 محاولة استخدام النافذة البديلة...")
        
        try:
            from gui.main_window_complete import CompleteMainWindow
            window = CompleteMainWindow()
            print("✅ تم إنشاء النافذة البديلة")
            return window
        except:
            print("❌ فشل في إنشاء النافذة البديلة")
            return None
    except Exception as e:
        print(f"❌ خطأ في إنشاء النافذة: {e}")
        return None

def show_beautiful_welcome():
    """عرض رسالة ترحيب جميلة"""
    print("\n" + "🎨" + "="*68 + "🎨")
    print("🎬 معالج الفيديوهات المتكامل - الإصدار الجميل والمحدث 🎬")
    print("🎨" + "="*68 + "🎨")
    print()
    print("✨ الميزات الجديدة والمحدثة:")
    print("  🎨 واجهة جميلة وحديثة مع تصميم عصري")
    print("  🌈 ألوان متناسقة وجذابة")
    print("  🎯 أزرار حديثة مع تأثيرات جميلة")
    print("  📱 تصميم متجاوب وسهل الاستخدام")
    print("  ⚡ أداء محسن وسرعة عالية")
    print()
    print("🎬 الميزات الأساسية:")
    print("  📹 معالجة وتقطيع الفيديوهات المتقدمة")
    print("  ⬇️ تحميل من جميع المنصات الشائعة")
    print("  🧠 ذكاء اصطناعي متطور (تفريغ، ترجمة، دبلجة)")
    print("  📊 إدارة مشاريع احترافية")
    print("  ⚙️ إعدادات متقدمة وقابلة للتخصيص")
    print()
    print("🎨" + "="*68 + "🎨")
    print("🚀 التطبيق الجميل جاهز للتشغيل!")
    print("🎨" + "="*68 + "🎨")

def create_output_directories():
    """إنشاء مجلدات الإخراج"""
    print("📁 إنشاء مجلدات الإخراج...")
    
    base_dir = Path.home() / "VideoEditor_Pro_Output"
    directories = [
        base_dir,
        base_dir / "processed_videos",
        base_dir / "downloads",
        base_dir / "transcriptions",
        base_dir / "translations",
        base_dir / "dubbing",
        base_dir / "projects",
        base_dir / "temp",
        base_dir / "exports",
        base_dir / "backups"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
    
    print(f"✅ تم إنشاء مجلدات الإخراج في: {base_dir}")
    return str(base_dir)

def apply_beautiful_fixes():
    """تطبيق الإصلاحات الجميلة"""
    print("🔧 تطبيق الإصلاحات الجميلة...")
    
    try:
        # إصلاح gui/__init__.py
        gui_init_path = project_root / "gui" / "__init__.py"
        gui_init_content = '''# -*- coding: utf-8 -*-
"""
وحدة واجهة المستخدم الجميلة والمحدثة
Beautiful Updated GUI Module
"""

try:
    from .beautiful_main_window import BeautifulMainWindow as MainWindow
    print("✅ تم استيراد BeautifulMainWindow")
except ImportError:
    try:
        from .main_window_complete import CompleteMainWindow as MainWindow
        print("✅ تم استيراد CompleteMainWindow")
    except ImportError:
        try:
            from .main_window import MainWindow
            print("✅ تم استيراد MainWindow الأصلي")
        except ImportError:
            print("❌ فشل في استيراد أي نافذة رئيسية")
            
            # إنشاء نافذة طوارئ جميلة
            from PyQt6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QLabel
            from PyQt6.QtCore import Qt
            
            class MainWindow(QMainWindow):
                def __init__(self):
                    super().__init__()
                    self.setWindowTitle("معالج الفيديوهات - الإصدار الجميل")
                    self.setGeometry(100, 100, 1000, 700)
                    
                    # إعداد الستايل الجميل
                    self.setStyleSheet("""
                        QMainWindow {
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #f8f9fa, stop:1 #e9ecef);
                        }
                    """)
                    
                    central_widget = QWidget()
                    self.setCentralWidget(central_widget)
                    
                    layout = QVBoxLayout(central_widget)
                    
                    label = QLabel("🎬 معالج الفيديوهات المتكامل - الإصدار الجميل")
                    label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    label.setStyleSheet("""
                        QLabel {
                            font-size: 28px;
                            font-weight: bold;
                            color: #2c3e50;
                            padding: 50px;
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #ffffff, stop:1 #f8f9fa);
                            border-radius: 20px;
                            margin: 20px;
                            border: 2px solid #e9ecef;
                        }
                    """)
                    layout.addWidget(label)

__all__ = ['MainWindow']
'''
        
        with open(gui_init_path, 'w', encoding='utf-8') as f:
            f.write(gui_init_content)
        print("✅ تم إصلاح gui/__init__.py")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في تطبيق الإصلاحات: {e}")
        return False

def main():
    """الدالة الرئيسية للتطبيق الجميل"""
    try:
        # عرض رسالة الترحيب الجميلة
        show_beautiful_welcome()
        
        # فحص وتحديث المتطلبات
        if not check_and_update_requirements():
            input("\nاضغط Enter للخروج...")
            return 1
        
        # إنشاء مجلدات الإخراج
        output_dir = create_output_directories()
        
        # تطبيق الإصلاحات الجميلة
        apply_beautiful_fixes()
        
        # إعداد التطبيق الجميل
        app = setup_beautiful_application()
        if not app:
            input("\nاضغط Enter للخروج...")
            return 1
        
        # إنشاء النافذة الجميلة
        window = create_beautiful_window()
        if not window:
            print("❌ فشل في إنشاء النافذة الجميلة")
            input("\nاضغط Enter للخروج...")
            return 1
        
        # عرض النافذة
        print("📺 عرض النافذة الجميلة...")
        window.show()
        
        print("✅ تم تشغيل التطبيق الجميل بنجاح!")
        print("🎨 استمتع بالواجهة الجميلة والميزات المتقدمة!")
        print("✨ جميع الميزات المطلوبة متوفرة ومحدثة!")
        
        # تشغيل حلقة الأحداث
        return app.exec()
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        return 0
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        import traceback
        traceback.print_exc()
        input("\nاضغط Enter للخروج...")
        return 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ خطأ كارثي: {e}")
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
