#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لجميع مشاكل المشروع
Comprehensive Fix for All Project Issues
"""

import sys
import os
import subprocess
import re
from pathlib import Path

def fix_typing_issues():
    """إصلاح مشاكل الـ typing"""
    print("🔧 إصلاح مشاكل الـ typing...")
    
    # قائمة الملفات التي تحتاج إصلاح
    files_to_fix = [
        "gui/main_window.py",
        "src/video_editor_core.py",
        "video_processing/video_processor.py",
        "video_processing/video_downloader.py",
        "video_processing/face_tracker.py",
        "language_ai/speech_to_text.py",
        "language_ai/translator.py",
        "language_ai/text_to_speech.py",
        "database/db_manager.py",
    ]
    
    for file_path in files_to_fix:
        full_path = Path(__file__).parent / file_path
        if full_path.exists():
            try:
                # قراءة الملف
                with open(full_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # استبدال Dict بـ dict
                content = re.sub(r'\bDict\b', 'dict', content)
                content = re.sub(r'\bList\b', 'list', content)
                content = re.sub(r'\bOptional\b', 'Optional', content)
                content = re.sub(r'\bUnion\b', 'Union', content)
                
                # كتابة الملف
                with open(full_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"  ✅ تم إصلاح {file_path}")
                
            except Exception as e:
                print(f"  ❌ خطأ في إصلاح {file_path}: {e}")

def add_safe_imports():
    """إضافة استيرادات آمنة لجميع الملفات"""
    print("🛡️ إضافة استيرادات آمنة...")
    
    # قالب الاستيراد الآمن
    safe_import_template = '''
# استيراد آمن للمكتبات
try:
    {original_import}
    {module_name}_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ {module_name} غير متاح: {{e}}")
    {module_name}_AVAILABLE = False
    {dummy_classes}
'''
    
    # قائمة الملفات والاستيرادات التي تحتاج إصلاح
    import_fixes = {
        "video_processing/video_processor.py": [
            ("moviepy", "from moviepy.editor import VideoFileClip, concatenate_videoclips"),
        ],
        "video_processing/video_downloader.py": [
            ("yt_dlp", "import yt_dlp"),
            ("pytube", "from pytube import YouTube"),
        ],
        "language_ai/speech_to_text.py": [
            ("whisper", "import whisper"),
            ("openai", "import openai"),
        ],
    }
    
    for file_path, imports in import_fixes.items():
        full_path = Path(__file__).parent / file_path
        if full_path.exists():
            print(f"  🔧 معالجة {file_path}")

def install_essential_packages():
    """تثبيت الحزم الأساسية"""
    print("📦 تثبيت الحزم الأساسية...")
    
    essential_packages = [
        "PyQt6>=6.6.0",
        "requests>=2.31.0", 
        "Pillow>=10.0.0",
    ]
    
    for package in essential_packages:
        try:
            print(f"  تثبيت {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet", "--upgrade"
            ])
            print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"  ❌ فشل في تثبيت {package}")

def install_video_packages():
    """تثبيت حزم معالجة الفيديو"""
    print("🎬 تثبيت حزم معالجة الفيديو...")
    
    video_packages = [
        "moviepy>=1.0.0",
        "opencv-python>=4.8.0",
    ]
    
    for package in video_packages:
        try:
            print(f"  تثبيت {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet", "--upgrade"
            ])
            print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"  ⚠️ فشل في تثبيت {package} - سيتم تخطيه")

def install_optional_packages():
    """تثبيت الحزم الاختيارية"""
    print("⚙️ تثبيت الحزم الاختيارية...")
    
    optional_packages = [
        "qdarkstyle>=3.2.0",
        "tqdm>=4.66.0",
        "psutil>=5.9.0",
        "sqlalchemy>=2.0.0",
    ]
    
    for package in optional_packages:
        try:
            print(f"  تثبيت {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--quiet", "--upgrade"
            ])
            print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"  ⚠️ فشل في تثبيت {package} - سيتم تخطيه")

def create_fallback_modules():
    """إنشاء وحدات احتياطية"""
    print("🔄 إنشاء وحدات احتياطية...")
    
    # إنشاء ملف fallback للمكتبات المفقودة
    fallback_content = '''# -*- coding: utf-8 -*-
"""
وحدات احتياطية للمكتبات المفقودة
Fallback modules for missing libraries
"""

class DummyVideoFileClip:
    def __init__(self, *args, **kwargs):
        raise ImportError("MoviePy غير مثبت")

class DummyTranslator:
    def __init__(self, *args, **kwargs):
        pass
    def translate(self, *args, **kwargs):
        return "ترجمة غير متاحة"

class DummyTTS:
    def __init__(self, *args, **kwargs):
        pass
    def save(self, *args, **kwargs):
        pass

# تصدير الفئات
__all__ = ['DummyVideoFileClip', 'DummyTranslator', 'DummyTTS']
'''
    
    fallback_path = Path(__file__).parent / "utils" / "fallbacks.py"
    try:
        with open(fallback_path, 'w', encoding='utf-8') as f:
            f.write(fallback_content)
        print("  ✅ تم إنشاء وحدات احتياطية")
    except Exception as e:
        print(f"  ❌ خطأ في إنشاء وحدات احتياطية: {e}")

def test_application():
    """اختبار التطبيق"""
    print("🧪 اختبار التطبيق...")
    
    try:
        # اختبار الاستيرادات الأساسية
        from PyQt6.QtWidgets import QApplication
        print("  ✅ PyQt6 متاح")
        
        # اختبار استيراد الوحدات الرئيسية
        from gui.main_window import MainWindow
        print("  ✅ واجهة المستخدم متاحة")
        
        from src.video_editor_core import VideoEditorCore
        print("  ✅ النواة الرئيسية متاحة")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في الاختبار: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح"""
    print("🔧 إصلاح شامل لمعالج الفيديوهات المتكامل")
    print("="*60)
    
    # تشغيل جميع الإصلاحات
    try:
        # 1. إصلاح مشاكل الـ typing
        fix_typing_issues()
        print()
        
        # 2. تثبيت الحزم الأساسية
        install_essential_packages()
        print()
        
        # 3. تثبيت حزم الفيديو
        install_video_packages()
        print()
        
        # 4. تثبيت الحزم الاختيارية
        install_optional_packages()
        print()
        
        # 5. إنشاء وحدات احتياطية
        create_fallback_modules()
        print()
        
        # 6. اختبار التطبيق
        app_works = test_application()
        print()
        
        # النتيجة النهائية
        print("="*60)
        if app_works:
            print("🎉 تم إصلاح جميع المشاكل بنجاح!")
            print("✅ التطبيق جاهز للتشغيل")
            print("\nلتشغيل التطبيق:")
            print("  python main.py")
            print("  أو")
            print("  python safe_run.py")
        else:
            print("⚠️ تم إصلاح معظم المشاكل")
            print("❌ قد تحتاج لتثبيت مكتبات إضافية")
            print("\nجرب:")
            print("  pip install PyQt6 requests Pillow moviepy opencv-python")
        
        return app_works
        
    except Exception as e:
        print(f"❌ خطأ في عملية الإصلاح: {e}")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
