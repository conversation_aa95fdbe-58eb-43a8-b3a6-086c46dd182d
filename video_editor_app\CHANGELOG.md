# سجل التغييرات - Changelog

جميع التغييرات المهمة في هذا المشروع سيتم توثيقها في هذا الملف.

التنسيق مبني على [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)،
وهذا المشروع يتبع [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [غير منشور] - Unreleased

### مخطط للإضافة
- دعم المزيد من تنسيقات الفيديو
- تحسين خوارزميات تتبع الوجوه
- إضافة المزيد من الفلاتر والتأثيرات
- دعم الدبلجة بأصوات متعددة
- واجهة مستخدم محسنة للأجهزة اللوحية

## [1.0.0] - 2025-01-11

### ✨ إضافات جديدة
- **واجهة مستخدم عربية كاملة**: تصميم حديث وسهل الاستخدام باللغة العربية
- **تحميل الفيديوهات**: دعم تحميل من YouTube, TikTok, Facebook, Instagram وغيرها
- **قص الفيديوهات الذكي**: 
  - قص متساوي للمقاطع
  - قص ذكي حسب المحتوى
  - قص حسب المشاهد
  - قص حسب الصوت
- **تتبع الوجوه المتقدم**:
  - تتبع الوجه الرئيسي
  - تتبع جميع الوجوه
  - تتبع أكبر وجه
  - دعم MediaPipe و OpenCV
- **تفريغ الصوت التلقائي**:
  - دعم OpenAI Whisper
  - دعم Google Speech Recognition
  - دعم OpenAI API
  - حفظ بتنسيقات مختلفة (TXT, SRT, VTT)
- **الترجمة التلقائية**:
  - دعم Google Translate
  - دعم DeepL API
  - دعم OpenAI للترجمة
  - ترجمة إلى أكثر من 50 لغة
- **الدبلجة الذكية**:
  - تحويل النص إلى كلام بـ Google TTS
  - دعم OpenAI TTS
  - دعم ElevenLabs API
  - أصوات متعددة ولغات مختلفة
- **المونتاج التلقائي**:
  - إضافة مقدمة وخاتمة
  - انتقالات تلقائية
  - فلاتر وتأثيرات
  - موسيقى خلفية
- **قاعدة بيانات متقدمة**:
  - حفظ معلومات الفيديوهات
  - تتبع العمليات
  - نسخ احتياطي تلقائي
- **نظام إعدادات شامل**:
  - إعدادات قابلة للتخصيص
  - حفظ واستيراد الإعدادات
  - دعم مفاتيح API
- **معالجة متوازية**:
  - تشغيل العمليات في خيوط منفصلة
  - مؤشرات تقدم مفصلة
  - إمكانية إلغاء العمليات

### 🛠️ تحسينات تقنية
- **هيكل مشروع منظم**: تقسيم الكود إلى وحدات منطقية
- **معالجة أخطاء شاملة**: نظام تسجيل متقدم ومعالجة استثناءات
- **واجهة برمجية نظيفة**: فصل المنطق عن الواجهة
- **دعم التشغيل المتوازي**: تحسين الأداء للعمليات الثقيلة
- **نظام إعدادات مرن**: إمكانية تخصيص جميع جوانب التطبيق

### 📦 أدوات التطوير والنشر
- **سكريبت بناء EXE**: إنشاء ملف تنفيذي بسهولة
- **اختبارات تلقائية**: فحص سلامة التطبيق
- **سكريبتات تشغيل**: تشغيل سريع على جميع المنصات
- **دليل شامل**: توثيق مفصل للاستخدام والتطوير

### 🎯 الميزات المدعومة
- **تنسيقات الفيديو**: MP4, AVI, MOV, MKV, WMV
- **تنسيقات الصوت**: WAV, MP3, AAC
- **جودة الإخراج**: 4K, 1080p, 720p, 480p
- **اللغات المدعومة**: العربية، الإنجليزية، الفرنسية، الألمانية، الإسبانية، وأكثر
- **المنصات المدعومة**: Windows, macOS, Linux

### 🔧 متطلبات النظام
- **الحد الأدنى**:
  - Python 3.8+
  - 4 GB RAM
  - 2 GB مساحة تخزين
  - Windows 10/macOS 10.14/Ubuntu 18.04+
- **الموصى به**:
  - Python 3.10+
  - 8 GB RAM
  - 5 GB مساحة تخزين
  - GPU للتسريع (اختياري)

### 📚 المكتبات الرئيسية
- **PyQt6**: واجهة المستخدم الرسومية
- **MoviePy**: معالجة الفيديو والصوت
- **OpenCV**: معالجة الصور والفيديو
- **MediaPipe**: كشف وتتبع الوجوه
- **OpenAI Whisper**: تفريغ الصوت عالي الجودة
- **Google Translate**: ترجمة متعددة اللغات
- **gTTS**: تحويل النص إلى كلام
- **yt-dlp**: تحميل الفيديوهات من المنصات
- **SQLAlchemy**: إدارة قاعدة البيانات

### 🐛 إصلاحات معروفة
- تحسين استقرار تحميل الفيديوهات من المنصات المختلفة
- إصلاح مشاكل الذاكرة في معالجة الفيديوهات الكبيرة
- تحسين دقة تتبع الوجوه في الإضاءة المنخفضة
- إصلاح مشاكل التزامن في العمليات المتوازية

### ⚠️ مشاكل معروفة
- قد تحتاج بعض الميزات إلى مفاتيح API (OpenAI, ElevenLabs, DeepL)
- تتبع الوجوه قد يكون بطيئاً على الأجهزة القديمة
- بعض تنسيقات الفيديو النادرة قد لا تكون مدعومة
- الدبلجة بـ ElevenLabs تتطلب اتصال إنترنت مستقر

### 🔮 خطط مستقبلية
- **الإصدار 1.1.0**:
  - دعم الذكاء الاصطناعي لتحسين جودة الفيديو
  - إضافة المزيد من خيارات المونتاج
  - تحسين واجهة المستخدم
  - دعم المزيد من اللغات

- **الإصدار 1.2.0**:
  - دعم البث المباشر
  - تحرير فيديو متقدم
  - مشاركة المشاريع السحابية
  - تطبيق موبايل مصاحب

### 🙏 شكر وتقدير
- فريق OpenAI على Whisper
- مطوري MoviePy و OpenCV
- مجتمع Python مفتوح المصدر
- جميع المساهمين والمختبرين

### 📞 الدعم والمساهمة
- **GitHub**: [رابط المشروع]
- **البريد الإلكتروني**: <EMAIL>
- **Discord**: [رابط الخادم]
- **التوثيق**: [رابط الويكي]

---

**ملاحظة**: هذا هو الإصدار الأول من التطبيق. نرحب بملاحظاتكم واقتراحاتكم لتحسين التطبيق في الإصدارات القادمة.
