#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي شامل للتطبيق
Final Comprehensive Test for Video Editor Application
"""

import sys
import traceback
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_python():
    """اختبار Python الأساسي"""
    print("🐍 اختبار Python الأساسي...")
    
    tests = [
        ("Python Version", f"Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"),
        ("Standard Library", "import os, sys, json, sqlite3, threading, logging"),
        ("Pathlib", "from pathlib import Path"),
        ("Typing", "from typing import Dict, List, Optional, Any, Union"),
        ("Datetime", "from datetime import datetime"),
    ]
    
    for name, test in tests:
        try:
            if name == "Python Version":
                print(f"  ✅ {test}")
            else:
                exec(test)
                print(f"  ✅ {name}")
        except Exception as e:
            print(f"  ❌ {name}: {e}")
            return False
    
    return True

def test_pyqt6_complete():
    """اختبار PyQt6 الشامل"""
    print("\n🖥️ اختبار PyQt6 الشامل...")
    
    try:
        # اختبار الاستيرادات الأساسية
        from PyQt6.QtWidgets import QApplication, QMainWindow, QWidget
        print("  ✅ PyQt6 Widgets أساسي")
        
        from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
        print("  ✅ PyQt6 Core أساسي")
        
        from PyQt6.QtGui import QFont, QIcon, QPixmap
        print("  ✅ PyQt6 Gui أساسي")
        
        # اختبار الاستيرادات المتقدمة
        from PyQt6.QtWidgets import (
            QLineEdit, QScrollArea, QSizePolicy, QDialog,
            QDialogButtonBox, QFormLayout, QStackedWidget
        )
        print("  ✅ PyQt6 Widgets متقدم")
        
        from PyQt6.QtCore import (
            QUrl, QDir, QFile, QIODevice, QByteArray,
            QVariant, QSettings, QStandardPaths
        )
        print("  ✅ PyQt6 Core متقدم")
        
        from PyQt6.QtGui import (
            QImage, QPainter, QBrush, QPen, QColor,
            QFontMetrics, QCursor, QKeySequence
        )
        print("  ✅ PyQt6 Gui متقدم")
        
        # اختبار إنشاء تطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("  ✅ إنشاء QApplication")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في PyQt6: {e}")
        traceback.print_exc()
        return False

def test_project_imports():
    """اختبار استيراد وحدات المشروع"""
    print("\n📦 اختبار وحدات المشروع...")
    
    modules = [
        ("config", "import config"),
        ("utils.safe_imports", "from utils.safe_imports import safe_importer"),
        ("database", "from database import DatabaseManager"),
        ("utils", "from utils import VideoEditorLogger, setup_logger"),
        ("video_processing", "from video_processing import VideoDownloader"),
        ("language_ai", "from language_ai import SpeechToText"),
        ("src", "from src import VideoEditorCore"),
        ("gui", "from gui import MainWindow"),
    ]
    
    results = {}
    for name, import_cmd in modules:
        try:
            exec(import_cmd)
            print(f"  ✅ {name}")
            results[name] = True
        except Exception as e:
            print(f"  ❌ {name}: {e}")
            results[name] = False
    
    return results

def test_main_window_creation():
    """اختبار إنشاء النافذة الرئيسية"""
    print("\n🖼️ اختبار إنشاء النافذة الرئيسية...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from gui.main_window import MainWindow
        
        # إنشاء تطبيق
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        print("  ✅ إنشاء QApplication")
        
        # إنشاء النافذة الرئيسية
        window = MainWindow()
        print("  ✅ إنشاء MainWindow")
        
        # اختبار بعض الخصائص
        window.setWindowTitle("اختبار")
        print("  ✅ تعيين عنوان النافذة")
        
        # لا نعرض النافذة في الاختبار
        # window.show()
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في إنشاء النافذة: {e}")
        traceback.print_exc()
        return False

def test_rtl_support():
    """اختبار دعم RTL"""
    print("\n🔄 اختبار دعم RTL...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import Qt
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        # اختبار الطرق المختلفة لـ RTL
        rtl_methods = [
            ("Qt.LayoutDirection.RightToLeft", lambda: Qt.LayoutDirection.RightToLeft),
            ("Qt.RightToLeft", lambda: Qt.RightToLeft),
            ("الرقم المباشر", lambda: 2),
        ]
        
        working_methods = 0
        for name, method in rtl_methods:
            try:
                value = method()
                app.setLayoutDirection(value)
                print(f"  ✅ {name}")
                working_methods += 1
            except Exception as e:
                print(f"  ❌ {name}: {e}")
        
        return working_methods > 0
        
    except Exception as e:
        print(f"  ❌ خطأ في اختبار RTL: {e}")
        return False

def test_main_application():
    """اختبار التطبيق الرئيسي"""
    print("\n🚀 اختبار التطبيق الرئيسي...")
    
    try:
        # اختبار استيراد main
        import main
        print("  ✅ استيراد main.py")
        
        # اختبار دالة setup_rtl_layout
        from main import setup_rtl_layout
        from PyQt6.QtWidgets import QApplication
        
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        
        result = setup_rtl_layout(app)
        print(f"  ✅ setup_rtl_layout: {'نجح' if result else 'فشل'}")
        
        # اختبار دالة setup_application
        from main import setup_application
        app2 = setup_application()
        print("  ✅ setup_application")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في التطبيق الرئيسي: {e}")
        traceback.print_exc()
        return False

def generate_final_report(results):
    """إنشاء تقرير نهائي شامل"""
    print("\n" + "="*60)
    print("📊 التقرير النهائي الشامل")
    print("="*60)
    
    # حساب النتائج
    total_tests = 0
    passed_tests = 0
    
    for category, result in results.items():
        if isinstance(result, bool):
            total_tests += 1
            if result:
                passed_tests += 1
            status = "✅ نجح" if result else "❌ فشل"
            print(f"{category}: {status}")
        elif isinstance(result, dict):
            category_passed = sum(result.values())
            category_total = len(result)
            total_tests += category_total
            passed_tests += category_passed
            print(f"\n{category}: {category_passed}/{category_total}")
            for test_name, test_result in result.items():
                status = "✅" if test_result else "❌"
                print(f"  {status} {test_name}")
    
    # النتيجة النهائية
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    
    print("\n" + "="*60)
    print(f"📈 النتيجة النهائية: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
    
    # تحديد حالة التطبيق
    essential_passed = (
        results.get('Python الأساسي', False) and
        results.get('PyQt6 الشامل', False) and
        results.get('النافذة الرئيسية', False)
    )
    
    if essential_passed and success_rate >= 80:
        print("🎉 التطبيق جاهز للتشغيل بالكامل!")
        print("✅ جميع المشاكل تم حلها")
        status = "excellent"
    elif essential_passed and success_rate >= 60:
        print("✅ التطبيق جاهز للتشغيل مع ميزات محدودة")
        print("⚠️ بعض الميزات المتقدمة قد لا تعمل")
        status = "good"
    elif essential_passed:
        print("⚠️ التطبيق يمكن تشغيله بوظائف أساسية")
        print("❌ العديد من الميزات لن تعمل")
        status = "basic"
    else:
        print("❌ التطبيق غير جاهز للتشغيل")
        print("🔧 يحتاج إصلاحات إضافية")
        status = "failed"
    
    # نصائح للتحسين
    print("\n💡 نصائح:")
    if status == "excellent":
        print("  🎬 يمكنك تشغيل التطبيق: python main.py")
        print("  🚀 أو استخدام: python safe_run.py")
    elif status in ["good", "basic"]:
        print("  🔧 جرب: python fix_all_imports.py")
        print("  📦 ثم: pip install --upgrade PyQt6 moviepy opencv-python")
        print("  🚀 ثم: python main.py")
    else:
        print("  📦 تثبيت المتطلبات: pip install PyQt6 requests Pillow")
        print("  🔧 إصلاح المشاكل: python fix_all_imports.py")
        print("  🧪 إعادة الاختبار: python test_final.py")
    
    return status

def main():
    """الدالة الرئيسية للاختبار النهائي"""
    print("🧪 الاختبار النهائي الشامل لمعالج الفيديوهات المتكامل")
    print("="*60)
    
    # تشغيل جميع الاختبارات
    results = {}
    
    results['Python الأساسي'] = test_basic_python()
    results['PyQt6 الشامل'] = test_pyqt6_complete()
    results['وحدات المشروع'] = test_project_imports()
    results['النافذة الرئيسية'] = test_main_window_creation()
    results['دعم RTL'] = test_rtl_support()
    results['التطبيق الرئيسي'] = test_main_application()
    
    # إنشاء التقرير النهائي
    status = generate_final_report(results)
    
    return status in ["excellent", "good"]

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
