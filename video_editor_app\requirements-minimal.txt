# المتطلبات الأساسية للتطبيق - Minimal Requirements

# ===== المتطلبات الأساسية (مطلوبة للتشغيل) =====

# GUI Framework (مطلوب)
PyQt6>=6.6.0

# HTTP Requests (مطلوب)
requests>=2.31.0

# Basic image processing (مطلوب)
Pillow>=10.0.0

# ===== متطلبات معالجة الفيديو (موصى بها) =====

# Video Processing
moviepy>=1.0.0

# Image Processing
opencv-python>=4.8.0

# ===== متطلبات قاعدة البيانات (اختيارية) =====

# Database ORM
sqlalchemy>=2.0.0

# ===== أدوات مساعدة (اختيارية) =====

# Progress bars
tqdm>=4.66.0

# System monitoring
psutil>=5.9.0

# ===== ملاحظات =====
# المكتبات التالية مدمجة في Python ولا تحتاج تثبيت:
# - sqlite3 (قاعدة البيانات)
# - json (معالجة JSON)
# - datetime (التاريخ والوقت)
# - threading (المعالجة المتوازية)
# - logging (نظام السجلات)
# - pathlib (مسارات الملفات)
# - os, sys (نظام التشغيل)
