class ConfigManager:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()

    def load_config(self):
        # TODO: Implement config loading from JSON file
        return {}

    def save_config(self):
        # TODO: Implement config saving to JSON file
        pass

    def get(self, key):
        return self.config.get(key)

    def set(self, key, value):
        self.config[key] = value
        self.save_config()