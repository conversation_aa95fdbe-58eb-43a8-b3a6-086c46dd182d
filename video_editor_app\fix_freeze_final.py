#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح نهائي لمشكلة التجمد
Final Fix for Application Freeze Issue
"""

import sys
import os
import subprocess
import shutil
from pathlib import Path

def backup_files():
    """نسخ احتياطي للملفات"""
    print("💾 إنشاء نسخ احتياطية...")
    
    backup_dir = Path(__file__).parent / "backup_freeze_fix"
    backup_dir.mkdir(exist_ok=True)
    
    files_to_backup = [
        "main.py",
        "gui/main_window.py"
    ]
    
    for file_path in files_to_backup:
        source = Path(__file__).parent / file_path
        if source.exists():
            dest = backup_dir / f"{source.name}.backup"
            shutil.copy2(source, dest)
            print(f"  ✅ تم نسخ {file_path}")

def install_required_packages():
    """تثبيت الحزم المطلوبة"""
    print("📦 تثبيت الحزم المطلوبة...")
    
    essential_packages = [
        "PyQt6>=6.6.0",
        "requests>=2.31.0",
        "Pillow>=10.0.0"
    ]
    
    for package in essential_packages:
        try:
            print(f"  تثبيت {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, "--upgrade", "--quiet"
            ])
            print(f"  ✅ تم تثبيت {package}")
        except subprocess.CalledProcessError:
            print(f"  ⚠️ فشل في تثبيت {package}")

def test_safe_version():
    """اختبار النسخة الآمنة"""
    print("🧪 اختبار النسخة الآمنة...")
    
    try:
        # اختبار سريع للنسخة الآمنة
        result = subprocess.run([
            sys.executable, "main_safe.py"
        ], timeout=10, capture_output=True, text=True)
        
        if "التطبيق يعمل" in result.stdout:
            print("  ✅ النسخة الآمنة تعمل")
            return True
        else:
            print("  ❌ النسخة الآمنة لا تعمل")
            return False
            
    except subprocess.TimeoutExpired:
        print("  ⚠️ النسخة الآمنة بطيئة لكنها تعمل")
        return True
    except Exception as e:
        print(f"  ❌ خطأ في اختبار النسخة الآمنة: {e}")
        return False

def create_launcher_script():
    """إنشاء سكريپت تشغيل ذكي"""
    print("🚀 إنشاء سكريپت تشغيل ذكي...")
    
    launcher_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل ذكي للتطبيق - Smart Application Launcher
يختار أفضل نسخة للتشغيل
"""

import sys
import subprocess
import time
from pathlib import Path

def try_launch_version(script_name, description, timeout=15):
    """محاولة تشغيل نسخة معينة"""
    print(f"🚀 محاولة تشغيل {description}...")
    
    try:
        # تشغيل مع timeout
        process = subprocess.Popen([sys.executable, script_name])
        
        # انتظار قصير للتحقق من التشغيل
        time.sleep(3)
        
        if process.poll() is None:
            # العملية ما زالت تعمل
            print(f"✅ {description} يعمل بنجاح!")
            process.wait()  # انتظار انتهاء العملية
            return True
        else:
            print(f"❌ {description} توقف مبكراً")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في {description}: {e}")
        return False

def main():
    """الدالة الرئيسية للمشغل الذكي"""
    print("🎬 مشغل ذكي لمعالج الفيديوهات المتكامل")
    print("="*50)
    
    # قائمة النسخ مرتبة حسب الأولوية
    versions = [
        ("main_safe.py", "النسخة الآمنة"),
        ("main_fixed.py", "النسخة المحسنة"),
        ("main_simple.py", "النسخة البسيطة"),
        ("main.py", "النسخة الأصلية"),
    ]
    
    for script, description in versions:
        script_path = Path(script)
        if script_path.exists():
            if try_launch_version(script, description):
                return True
        else:
            print(f"⚠️ {script} غير موجود")
    
    print("❌ فشل في تشغيل جميع النسخ")
    print("💡 جرب:")
    print("  1. pip install --upgrade PyQt6")
    print("  2. python debug_freeze.py")
    print("  3. python fix_freeze_final.py")
    
    return False

if __name__ == "__main__":
    success = main()
    if not success:
        input("\\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
'''
    
    try:
        launcher_path = Path(__file__).parent / "launch.py"
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_script)
        
        print(f"  ✅ تم إنشاء {launcher_path}")
        return True
        
    except Exception as e:
        print(f"  ❌ فشل في إنشاء المشغل: {e}")
        return False

def create_batch_files():
    """إنشاء ملفات batch للتشغيل السريع"""
    print("📄 إنشاء ملفات التشغيل السريع...")
    
    # ملف Windows
    batch_content = '''@echo off
echo 🎬 معالج الفيديوهات المتكامل
echo ========================
echo.

echo 🚀 تشغيل النسخة الآمنة...
python main_safe.py

if errorlevel 1 (
    echo.
    echo ⚠️ فشل في النسخة الآمنة، جاري تجربة البدائل...
    python launch.py
)

pause
'''
    
    try:
        batch_path = Path(__file__).parent / "run_safe.bat"
        with open(batch_path, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        print(f"  ✅ تم إنشاء {batch_path}")
    except Exception as e:
        print(f"  ❌ فشل في إنشاء ملف batch: {e}")
    
    # ملف Linux/macOS
    shell_content = '''#!/bin/bash
echo "🎬 معالج الفيديوهات المتكامل"
echo "========================"
echo

echo "🚀 تشغيل النسخة الآمنة..."
python3 main_safe.py

if [ $? -ne 0 ]; then
    echo
    echo "⚠️ فشل في النسخة الآمنة، جاري تجربة البدائل..."
    python3 launch.py
fi
'''
    
    try:
        shell_path = Path(__file__).parent / "run_safe.sh"
        with open(shell_path, 'w', encoding='utf-8') as f:
            f.write(shell_content)
        
        # جعل الملف قابل للتنفيذ
        import stat
        shell_path.chmod(shell_path.stat().st_mode | stat.S_IEXEC)
        
        print(f"  ✅ تم إنشاء {shell_path}")
    except Exception as e:
        print(f"  ❌ فشل في إنشاء ملف shell: {e}")

def run_final_test():
    """اختبار نهائي"""
    print("🧪 اختبار نهائي...")
    
    try:
        # اختبار الاستيرادات الأساسية
        from PyQt6.QtWidgets import QApplication
        print("  ✅ PyQt6 متاح")
        
        # اختبار إنشاء تطبيق
        app = QApplication([])
        print("  ✅ يمكن إنشاء QApplication")
        
        # اختبار النافذة الآمنة
        from gui.main_window_safe import SafeMainWindow
        window = SafeMainWindow()
        print("  ✅ يمكن إنشاء النافذة الآمنة")
        
        return True
        
    except Exception as e:
        print(f"  ❌ خطأ في الاختبار النهائي: {e}")
        return False

def main():
    """الدالة الرئيسية للإصلاح النهائي"""
    print("🔧 الإصلاح النهائي لمشكلة التجمد")
    print("="*40)
    
    # 1. نسخ احتياطي
    backup_files()
    print()
    
    # 2. تثبيت الحزم
    install_required_packages()
    print()
    
    # 3. اختبار النسخة الآمنة
    safe_works = test_safe_version()
    print()
    
    # 4. إنشاء المشغل الذكي
    launcher_created = create_launcher_script()
    print()
    
    # 5. إنشاء ملفات التشغيل السريع
    create_batch_files()
    print()
    
    # 6. اختبار نهائي
    final_test_passed = run_final_test()
    print()
    
    # النتيجة النهائية
    print("="*40)
    print("📊 النتائج:")
    print(f"  النسخة الآمنة: {'✅ تعمل' if safe_works else '❌ لا تعمل'}")
    print(f"  المشغل الذكي: {'✅ تم إنشاؤه' if launcher_created else '❌ فشل'}")
    print(f"  الاختبار النهائي: {'✅ نجح' if final_test_passed else '❌ فشل'}")
    
    if safe_works and final_test_passed:
        print("\n🎉 تم حل مشكلة التجمد نهائياً!")
        print("\n🚀 طرق التشغيل:")
        print("  1. python main_safe.py (الأفضل)")
        print("  2. python launch.py (ذكي)")
        print("  3. run_safe.bat (Windows)")
        print("  4. ./run_safe.sh (Linux/macOS)")
        return True
    else:
        print("\n⚠️ تم إنشاء بدائل للتشغيل")
        print("💡 جرب:")
        print("  python debug_freeze.py")
        print("  python main_safe.py")
        return False

if __name__ == "__main__":
    success = main()
    input("\nاضغط Enter للخروج...")
    sys.exit(0 if success else 1)
