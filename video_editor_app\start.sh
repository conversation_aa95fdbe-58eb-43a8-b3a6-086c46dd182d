#!/bin/bash

# معالج الفيديوهات المتكامل - Video Editor Pro
# سكريبت التشغيل لـ Linux/macOS

echo "========================================"
echo "🎬 معالج الفيديوهات المتكامل"
echo "   Video Editor Pro"
echo "========================================"
echo

# التحقق من وجود Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ Python غير مثبت"
        echo "يرجى تثبيت Python 3.8 أو أحدث"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "✅ تم العثور على Python"
$PYTHON_CMD --version

# التحقق من إصدار Python
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
REQUIRED_VERSION="3.8"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$PYTHON_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "❌ يتطلب Python 3.8 أو أحدث"
    echo "الإصدار الحالي: $PYTHON_VERSION"
    exit 1
fi

# التحقق من وجود pip
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo "❌ pip غير متوفر"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

echo "✅ pip متوفر"

# التحقق من وجود ملف المتطلبات
if [ ! -f "requirements.txt" ]; then
    echo "❌ ملف requirements.txt غير موجود"
    exit 1
fi

echo
echo "📦 تثبيت المتطلبات الأساسية..."

# تثبيت المتطلبات الأساسية أولاً
echo "تثبيت المتطلبات الأساسية..."
$PIP_CMD install PyQt6 moviepy opencv-python requests sqlalchemy Pillow tqdm

if [ $? -ne 0 ]; then
    echo "❌ فشل في تثبيت المتطلبات الأساسية"
    echo "جاري المحاولة مع ملف requirements-minimal.txt..."
    $PIP_CMD install -r requirements-minimal.txt
    if [ $? -ne 0 ]; then
        echo "❌ فشل في تثبيت المتطلبات"
        echo "جرب تشغيل الأمر التالي يدوياً:"
        echo "$PIP_CMD install -r requirements-minimal.txt"
        exit 1
    fi
fi

echo
echo "📦 تثبيت المتطلبات الإضافية (اختيارية)..."
$PIP_CMD install qdarkstyle
if [ $? -ne 0 ]; then
    echo "⚠️ لم يتم تثبيت qdarkstyle - سيتم استخدام الستايل الافتراضي"
fi

echo "✅ تم تثبيت جميع المتطلبات"

echo
echo "🚀 تشغيل التطبيق..."
echo

# تشغيل التطبيق
$PYTHON_CMD main.py

if [ $? -ne 0 ]; then
    echo
    echo "❌ حدث خطأ في تشغيل التطبيق"
    exit 1
fi

echo
echo "✅ تم إغلاق التطبيق بنجاح"
