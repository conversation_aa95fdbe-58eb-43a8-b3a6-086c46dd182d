#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل التطبيق الجميل والمحدث - Run Beautiful Updated App
يحدث المكتبات ويصلح المشاكل ويشغل التطبيق بواجهة جميلة
"""

import sys
import os
import subprocess
import time
from pathlib import Path

# إضافة مسار المشروع
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def print_beautiful_header(title):
    """طباعة عنوان جميل"""
    print("\n" + "🎨" + "="*58 + "🎨")
    print(f"🎬 {title}")
    print("🎨" + "="*58 + "🎨")

def print_step(step, description):
    """طباعة خطوة جميلة"""
    print(f"\n🔹 الخطوة {step}: {description}")
    print("─" * 50)

def update_all_packages():
    """تحديث جميع المكتبات"""
    print("📦 تحديث جميع المكتبات...")
    
    # تحديث pip أولاً
    try:
        print("  🔄 تحديث pip...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("  ✅ تم تحديث pip")
    except:
        print("  ⚠️ فشل في تحديث pip")
    
    # قائمة المكتبات المحدثة
    packages = [
        "PyQt6>=6.7.0",
        "requests>=2.32.0", 
        "Pillow>=10.4.0",
        "opencv-python>=4.10.0",
        "numpy>=1.26.0",
        "matplotlib>=3.8.0",
        "psutil>=5.9.0",
        "beautifulsoup4>=4.12.0",
        "lxml>=5.0.0"
    ]
    
    success_count = 0
    for package in packages:
        try:
            print(f"  📥 تثبيت {package}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", package, 
                "--upgrade", "--force-reinstall", "--no-cache-dir"
            ], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"  ✅ {package}")
            success_count += 1
        except:
            print(f"  ❌ فشل {package}")
    
    print(f"\n📊 تم تحديث {success_count}/{len(packages)} مكتبة")
    return success_count >= len(packages) * 0.8  # نجاح 80% على الأقل

def fix_all_files():
    """إصلاح جميع الملفات"""
    print("🔧 إصلاح جميع الملفات...")
    
    # إصلاح gui/__init__.py
    gui_init_content = '''# -*- coding: utf-8 -*-
"""
وحدة واجهة المستخدم الجميلة والمحدثة
Beautiful Updated GUI Module
"""

try:
    from .beautiful_main_window import BeautifulMainWindow as MainWindow
    print("✅ تم استيراد BeautifulMainWindow")
except ImportError:
    try:
        from .main_window_complete import CompleteMainWindow as MainWindow
        print("✅ تم استيراد CompleteMainWindow")
    except ImportError:
        try:
            from .main_window import MainWindow
            print("✅ تم استيراد MainWindow الأصلي")
        except ImportError:
            print("❌ فشل في استيراد أي نافذة رئيسية")
            
            # إنشاء نافذة طوارئ جميلة
            from PyQt6.QtWidgets import QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton
            from PyQt6.QtCore import Qt
            
            class MainWindow(QMainWindow):
                def __init__(self):
                    super().__init__()
                    self.setWindowTitle("معالج الفيديوهات - الإصدار الجميل المحدث")
                    self.setGeometry(100, 100, 1200, 800)
                    
                    # إعداد الستايل الجميل
                    self.setStyleSheet("""
                        QMainWindow {
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #f8f9fa, stop:1 #e9ecef);
                        }
                    """)
                    
                    central_widget = QWidget()
                    self.setCentralWidget(central_widget)
                    
                    layout = QVBoxLayout(central_widget)
                    layout.setContentsMargins(40, 40, 40, 40)
                    layout.setSpacing(30)
                    
                    # العنوان الجميل
                    title = QLabel("🎬 معالج الفيديوهات المتكامل")
                    title.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    title.setStyleSheet("""
                        QLabel {
                            font-size: 32px;
                            font-weight: bold;
                            color: #2c3e50;
                            padding: 30px;
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #ffffff, stop:1 #f8f9fa);
                            border-radius: 20px;
                            border: 3px solid #4CAF50;
                        }
                    """)
                    layout.addWidget(title)
                    
                    # العنوان الفرعي
                    subtitle = QLabel("الإصدار الجميل والمحدث - جميع الميزات متاحة")
                    subtitle.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    subtitle.setStyleSheet("""
                        QLabel {
                            font-size: 18px;
                            color: #6c757d;
                            padding: 15px;
                            background-color: rgba(76, 175, 80, 0.1);
                            border-radius: 10px;
                        }
                    """)
                    layout.addWidget(subtitle)
                    
                    # زر اختبار
                    test_btn = QPushButton("🧪 اختبار التطبيق")
                    test_btn.setStyleSheet("""
                        QPushButton {
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #4CAF50, stop:1 #45a049);
                            color: white;
                            border: none;
                            border-radius: 15px;
                            padding: 15px 30px;
                            font-weight: bold;
                            font-size: 16px;
                        }
                        QPushButton:hover {
                            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                stop:0 #45a049, stop:1 #3d8b40);
                        }
                    """)
                    test_btn.clicked.connect(lambda: print("✅ التطبيق يعمل بشكل مثالي!"))
                    layout.addWidget(test_btn)

__all__ = ['MainWindow']
'''
    
    try:
        gui_init_path = project_root / "gui" / "__init__.py"
        with open(gui_init_path, 'w', encoding='utf-8') as f:
            f.write(gui_init_content)
        print("  ✅ gui/__init__.py")
        return True
    except Exception as e:
        print(f"  ❌ خطأ في إصلاح الملفات: {e}")
        return False

def create_beautiful_directories():
    """إنشاء مجلدات جميلة ومنظمة"""
    print("📁 إنشاء مجلدات جميلة ومنظمة...")
    
    base_dir = Path.home() / "VideoEditor_Beautiful_Output"
    directories = [
        base_dir,
        base_dir / "🎬_processed_videos",
        base_dir / "⬇️_downloads",
        base_dir / "🎤_transcriptions",
        base_dir / "🌐_translations", 
        base_dir / "🎭_dubbing",
        base_dir / "📊_projects",
        base_dir / "⚙️_settings",
        base_dir / "🗂️_temp",
        base_dir / "📤_exports",
        base_dir / "💾_backups"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        print(f"  ✅ {directory.name}")
    
    print(f"\n📍 مجلد الإخراج الجميل: {base_dir}")
    return str(base_dir)

def test_beautiful_app():
    """اختبار التطبيق الجميل"""
    print("🧪 اختبار التطبيق الجميل...")
    
    tests = []
    
    # اختبار Python
    tests.append(("Python", sys.version_info >= (3, 8), f"Python {sys.version_info.major}.{sys.version_info.minor}"))
    
    # اختبار PyQt6
    try:
        import PyQt6
        tests.append(("PyQt6", True, f"PyQt6 {PyQt6.QtCore.PYQT_VERSION_STR}"))
    except ImportError:
        tests.append(("PyQt6", False, "PyQt6 غير متاح"))
    
    # اختبار إنشاء التطبيق
    try:
        from PyQt6.QtWidgets import QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication([])
        tests.append(("QApplication", True, "QApplication يعمل"))
    except:
        tests.append(("QApplication", False, "QApplication لا يعمل"))
    
    # اختبار النافذة الجميلة
    try:
        from gui.beautiful_main_window import BeautifulMainWindow
        window = BeautifulMainWindow()
        tests.append(("BeautifulMainWindow", True, "النافذة الجميلة تعمل"))
    except:
        tests.append(("BeautifulMainWindow", False, "النافذة الجميلة لا تعمل"))
    
    # عرض النتائج
    passed = sum(1 for test in tests if test[1])
    total = len(tests)
    
    print(f"\n📊 نتائج الاختبار: {passed}/{total} نجح")
    
    for test in tests:
        status = "✅" if test[1] else "❌"
        print(f"  {status} {test[2]}")
    
    return passed >= total * 0.75  # نجاح 75% على الأقل

def run_beautiful_app():
    """تشغيل التطبيق الجميل"""
    print("🚀 تشغيل التطبيق الجميل...")
    
    try:
        # تشغيل التطبيق الجميل
        subprocess.run([sys.executable, "main_beautiful.py"])
        return True
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def show_final_message(success):
    """عرض الرسالة النهائية"""
    if success:
        print_beautiful_header("تم التحديث والتشغيل بنجاح!")
        print("🎉 التطبيق الجميل يعمل بشكل مثالي!")
        print("✨ جميع الميزات محدثة ومتاحة:")
        print("  🎨 واجهة جميلة وحديثة")
        print("  🎬 معالجة فيديو متقدمة")
        print("  ⬇️ تحميل من الإنترنت")
        print("  🧠 ذكاء اصطناعي متطور")
        print("  📊 إدارة مشاريع احترافية")
        print("  ⚙️ إعدادات متقدمة")
        print("\n🚀 استمتع بالتطبيق الجميل!")
    else:
        print_beautiful_header("حدثت بعض المشاكل")
        print("⚠️ بعض المكونات قد لا تعمل بشكل مثالي")
        print("💡 جرب تشغيل السكريپت مرة أخرى")
        print("🔧 أو استخدم: python main_beautiful.py")

def main():
    """الدالة الرئيسية للتحديث والتشغيل"""
    print_beautiful_header("تحديث وتشغيل التطبيق الجميل")
    
    try:
        print_step(1, "تحديث جميع المكتبات")
        packages_updated = update_all_packages()
        
        print_step(2, "إصلاح جميع الملفات")
        files_fixed = fix_all_files()
        
        print_step(3, "إنشاء مجلدات جميلة")
        output_dir = create_beautiful_directories()
        
        print_step(4, "اختبار التطبيق الجميل")
        app_tested = test_beautiful_app()
        
        print_step(5, "تشغيل التطبيق الجميل")
        
        # تحديد مستوى النجاح
        success_level = sum([packages_updated, files_fixed, app_tested])
        
        if success_level >= 2:
            print("🎉 جميع الاختبارات نجحت - تشغيل التطبيق...")
            app_running = run_beautiful_app()
            show_final_message(app_running)
            return app_running
        else:
            print("⚠️ بعض المشاكل موجودة - محاولة التشغيل...")
            app_running = run_beautiful_app()
            show_final_message(app_running)
            return app_running
            
    except Exception as e:
        print(f"\n❌ خطأ في التحديث والتشغيل: {e}")
        import traceback
        traceback.print_exc()
        show_final_message(False)
        return False

if __name__ == "__main__":
    try:
        success = main()
        input(f"\n{'🎉 اضغط Enter للخروج...' if success else '❌ اضغط Enter للخروج...'}")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التحديث والتشغيل")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ خطأ كارثي: {e}")
        input("\nاضغط Enter للخروج...")
        sys.exit(1)
