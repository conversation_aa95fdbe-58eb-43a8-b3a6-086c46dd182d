# -*- coding: utf-8 -*-
"""
نظام التسجيل والمراقبة
Logging System for Video Editor Application
"""

import logging
import os
from pathlib import Path
from datetime import datetime

def setup_logger(log_level=logging.INFO):
    """
    إعداد نظام التسجيل
    
    Args:
        log_level: مستوى التسجيل
    
    Returns:
        logger: كائن المسجل
    """
    
    # إنشاء مجلد السجلات
    log_dir = Path(__file__).parent.parent / "logs"
    log_dir.mkdir(exist_ok=True)
    
    # اسم ملف السجل مع التاريخ
    log_filename = f"video_editor_{datetime.now().strftime('%Y%m%d')}.log"
    log_path = log_dir / log_filename
    
    # إعداد التنسيق
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # إعداد المسجل الرئيسي
    logger = logging.getLogger()
    logger.setLevel(log_level)
    
    # إزالة المعالجات الموجودة
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # معالج الملف
    file_handler = logging.FileHandler(log_path, encoding='utf-8')
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    # معالج وحدة التحكم
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.WARNING)  # فقط التحذيرات والأخطاء في وحدة التحكم
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    return logger

class VideoEditorLogger:
    """فئة مخصصة لتسجيل أحداث معالج الفيديوهات"""
    
    def __init__(self, name: str):
        self.logger = logging.getLogger(name)
    
    def info(self, message: str):
        """تسجيل معلومة"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """تسجيل تحذير"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """تسجيل خطأ"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """تسجيل معلومة تطوير"""
        self.logger.debug(message)
    
    def operation_start(self, operation_type: str, video_file: str):
        """تسجيل بداية عملية"""
        self.info(f"بدء عملية {operation_type} للفيديو: {video_file}")
    
    def operation_complete(self, operation_type: str, video_file: str, duration: float):
        """تسجيل انتهاء عملية"""
        self.info(f"انتهت عملية {operation_type} للفيديو: {video_file} في {duration:.2f} ثانية")
    
    def operation_error(self, operation_type: str, video_file: str, error: str):
        """تسجيل خطأ في عملية"""
        self.error(f"خطأ في عملية {operation_type} للفيديو: {video_file} - {error}")
    
    def progress_update(self, operation_type: str, progress: int):
        """تسجيل تقدم العملية"""
        self.debug(f"تقدم عملية {operation_type}: {progress}%")
